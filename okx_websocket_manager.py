#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX WebSocket管理器 - 实时数据流处理
支持行情数据、账户数据、订单数据等实时推送
"""

import json
import os
import asyncio
import websockets
import hmac
import hashlib
import base64
import time
from datetime import datetime
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
from enum import Enum

class OKXWSChannel(Enum):
    """OKX WebSocket频道"""
    # 公共频道
    TICKERS = "tickers"                    # 行情频道
    ORDER_BOOK = "books"                   # 订单簿频道
    ORDER_BOOK_L2_TBT = "books-l2-tbt"   # 订单簿逐笔频道
    TRADES = "trades"                      # 成交频道
    CANDLESTICKS = "candle1m"             # K线频道
    
    # 私有频道
    ACCOUNT = "account"                    # 账户频道
    POSITIONS = "positions"                # 持仓频道
    ORDERS = "orders"                      # 订单频道
    ORDERS_ALGO = "orders-algo"           # 策略委托订单频道

@dataclass
class OKXWSConfig:
    """OKX WebSocket配置"""
    api_key: str
    secret_key: str
    passphrase: str
    is_sandbox: bool = False
    public_url: str = "wss://ws.okx.com:8443/ws/v5/public"
    private_url: str = "wss://ws.okx.com:8443/ws/v5/private"
    business_url: str = "wss://ws.okx.com:8443/ws/v5/business"

class OKXWebSocketManager:
    """OKX WebSocket管理器"""
    
    def __init__(self, config: OKXWSConfig):
        self.config = config
        self.public_ws = None
        self.private_ws = None
        self.business_ws = None
        
        # 回调函数
        self.message_handlers: Dict[str, List[Callable]] = {}
        self.error_handlers: List[Callable] = []
        
        # 连接状态
        self.is_connected = False
        self.reconnect_interval = 5
        self.max_reconnect_attempts = 10
        
        # 订阅管理
        self.subscriptions: Dict[str, List[Dict]] = {
            'public': [],
            'private': [],
            'business': []
        }
    
    def add_message_handler(self, channel: str, handler: Callable):
        """添加消息处理器"""
        if channel not in self.message_handlers:
            self.message_handlers[channel] = []
        self.message_handlers[channel].append(handler)
    
    def add_error_handler(self, handler: Callable):
        """添加错误处理器"""
        self.error_handlers.append(handler)
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str) -> str:
        """生成签名"""
        message = timestamp + method + request_path
        signature = hmac.new(
            self.config.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).digest()
        return base64.b64encode(signature).decode('utf-8')
    
    def _get_auth_message(self) -> Dict:
        """获取认证消息"""
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        signature = self._generate_signature(timestamp, 'GET', '/users/self/verify')
        
        return {
            "op": "login",
            "args": [{
                "apiKey": self.config.api_key,
                "passphrase": self.config.passphrase,
                "timestamp": timestamp,
                "sign": signature
            }]
        }
    
    async def _handle_message(self, message: str, ws_type: str):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            
            # 处理不同类型的消息
            if 'event' in data:
                # 事件消息（订阅确认、错误等）
                await self._handle_event_message(data, ws_type)
            elif 'arg' in data and 'data' in data:
                # 数据推送消息
                await self._handle_data_message(data, ws_type)
            else:
                print(f"Unknown message format: {data}")
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse message: {e}")
        except Exception as e:
            print(f"Error handling message: {e}")
            for handler in self.error_handlers:
                try:
                    await handler(e, message)
                except:
                    pass
    
    async def _handle_event_message(self, data: Dict, ws_type: str):
        """处理事件消息"""
        event = data.get('event')
        
        if event == 'subscribe':
            print(f"Subscription confirmed: {data.get('arg')}")
        elif event == 'unsubscribe':
            print(f"Unsubscription confirmed: {data.get('arg')}")
        elif event == 'error':
            print(f"Error: {data}")
            for handler in self.error_handlers:
                try:
                    await handler(Exception(data.get('msg', 'Unknown error')), data)
                except:
                    pass
        elif event == 'login':
            if data.get('code') == '0':
                print("Authentication successful")
            else:
                print(f"Authentication failed: {data}")
    
    async def _handle_data_message(self, data: Dict, ws_type: str):
        """处理数据推送消息"""
        arg = data.get('arg', {})
        channel = arg.get('channel')
        
        if channel in self.message_handlers:
            for handler in self.message_handlers[channel]:
                try:
                    await handler(data)
                except Exception as e:
                    print(f"Error in message handler: {e}")
    
    async def _websocket_handler(self, websocket, ws_type: str):
        """WebSocket消息处理循环"""
        try:
            async for message in websocket:
                await self._handle_message(message, ws_type)
        except websockets.exceptions.ConnectionClosed:
            print(f"{ws_type} WebSocket connection closed")
        except Exception as e:
            print(f"Error in {ws_type} WebSocket handler: {e}")
    
    async def connect_public(self):
        """连接公共WebSocket"""
        try:
            self.public_ws = await websockets.connect(self.config.public_url)
            print("Public WebSocket connected")
            
            # 重新订阅
            for sub in self.subscriptions['public']:
                await self.public_ws.send(json.dumps(sub))
            
            # 启动消息处理
            asyncio.create_task(self._websocket_handler(self.public_ws, 'public'))
            
        except Exception as e:
            print(f"Failed to connect public WebSocket: {e}")
            raise
    
    async def connect_private(self):
        """连接私有WebSocket"""
        try:
            self.private_ws = await websockets.connect(self.config.private_url)
            print("Private WebSocket connected")
            
            # 认证
            auth_message = self._get_auth_message()
            await self.private_ws.send(json.dumps(auth_message))
            
            # 等待认证结果
            await asyncio.sleep(1)
            
            # 重新订阅
            for sub in self.subscriptions['private']:
                await self.private_ws.send(json.dumps(sub))
            
            # 启动消息处理
            asyncio.create_task(self._websocket_handler(self.private_ws, 'private'))
            
        except Exception as e:
            print(f"Failed to connect private WebSocket: {e}")
            raise
    
    async def connect_business(self):
        """连接业务WebSocket"""
        try:
            self.business_ws = await websockets.connect(self.config.business_url)
            print("Business WebSocket connected")
            
            # 认证
            auth_message = self._get_auth_message()
            await self.business_ws.send(json.dumps(auth_message))
            
            # 等待认证结果
            await asyncio.sleep(1)
            
            # 重新订阅
            for sub in self.subscriptions['business']:
                await self.business_ws.send(json.dumps(sub))
            
            # 启动消息处理
            asyncio.create_task(self._websocket_handler(self.business_ws, 'business'))
            
        except Exception as e:
            print(f"Failed to connect business WebSocket: {e}")
            raise
    
    async def connect_all(self):
        """连接所有WebSocket"""
        await self.connect_public()
        await self.connect_private()
        await self.connect_business()
        self.is_connected = True
    
    async def subscribe_ticker(self, inst_id: str):
        """订阅行情数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": "tickers",
                "instId": inst_id
            }]
        }
        
        self.subscriptions['public'].append(message)
        
        if self.public_ws:
            await self.public_ws.send(json.dumps(message))
    
    async def subscribe_orderbook(self, inst_id: str, channel: str = "books"):
        """订阅订单簿数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": channel,
                "instId": inst_id
            }]
        }
        
        self.subscriptions['public'].append(message)
        
        if self.public_ws:
            await self.public_ws.send(json.dumps(message))
    
    async def subscribe_trades(self, inst_id: str):
        """订阅成交数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": "trades",
                "instId": inst_id
            }]
        }
        
        self.subscriptions['public'].append(message)
        
        if self.public_ws:
            await self.public_ws.send(json.dumps(message))
    
    async def subscribe_candlesticks(self, inst_id: str, channel: str = "candle1m"):
        """订阅K线数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": channel,
                "instId": inst_id
            }]
        }
        
        self.subscriptions['public'].append(message)
        
        if self.public_ws:
            await self.public_ws.send(json.dumps(message))
    
    async def subscribe_account(self):
        """订阅账户数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": "account"
            }]
        }
        
        self.subscriptions['private'].append(message)
        
        if self.private_ws:
            await self.private_ws.send(json.dumps(message))
    
    async def subscribe_positions(self, inst_type: str = "ANY"):
        """订阅持仓数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": "positions",
                "instType": inst_type
            }]
        }
        
        self.subscriptions['private'].append(message)
        
        if self.private_ws:
            await self.private_ws.send(json.dumps(message))
    
    async def subscribe_orders(self, inst_type: str = "ANY"):
        """订阅订单数据"""
        message = {
            "op": "subscribe",
            "args": [{
                "channel": "orders",
                "instType": inst_type
            }]
        }
        
        self.subscriptions['private'].append(message)
        
        if self.private_ws:
            await self.private_ws.send(json.dumps(message))
    
    async def unsubscribe(self, channel: str, inst_id: str = None):
        """取消订阅"""
        args = {"channel": channel}
        if inst_id:
            args["instId"] = inst_id
        
        message = {
            "op": "unsubscribe",
            "args": [args]
        }
        
        # 从订阅列表中移除
        for ws_type in self.subscriptions:
            self.subscriptions[ws_type] = [
                sub for sub in self.subscriptions[ws_type]
                if not (sub.get('args', [{}])[0].get('channel') == channel and
                       (not inst_id or sub.get('args', [{}])[0].get('instId') == inst_id))
            ]
        
        # 发送取消订阅消息
        if channel in ['tickers', 'books', 'trades', 'candle1m'] and self.public_ws:
            await self.public_ws.send(json.dumps(message))
        elif self.private_ws:
            await self.private_ws.send(json.dumps(message))
    
    async def close(self):
        """关闭所有连接"""
        self.is_connected = False
        
        if self.public_ws:
            await self.public_ws.close()
        if self.private_ws:
            await self.private_ws.close()
        if self.business_ws:
            await self.business_ws.close()

# 使用示例
async def example_usage():
    """使用示例"""
    config = OKXWSConfig(
        api_key=os.getenv("OKX_API_KEY", ""),
        secret_key=os.getenv("OKX_SECRET_KEY", ""),
        passphrase=os.getenv("OKX_PASSPHRASE", "")
    )
    
    ws_manager = OKXWebSocketManager(config)
    
    # 添加消息处理器
    async def handle_ticker(data):
        print(f"Ticker data: {data}")
    
    async def handle_orderbook(data):
        print(f"Orderbook data: {data}")
    
    async def handle_error(error, data):
        print(f"Error: {error}, Data: {data}")
    
    ws_manager.add_message_handler("tickers", handle_ticker)
    ws_manager.add_message_handler("books", handle_orderbook)
    ws_manager.add_error_handler(handle_error)
    
    try:
        # 连接WebSocket
        await ws_manager.connect_all()
        
        # 订阅数据
        await ws_manager.subscribe_ticker("BTC-USDT")
        await ws_manager.subscribe_orderbook("BTC-USDT")
        await ws_manager.subscribe_account()
        await ws_manager.subscribe_orders()
        
        # 保持连接
        await asyncio.sleep(60)
        
    finally:
        await ws_manager.close()

if __name__ == "__main__":
    asyncio.run(example_usage())
