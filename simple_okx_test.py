#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的OKX连接测试
"""

import requests
import json
from datetime import datetime

def test_okx_simple():
    """简单测试OKX API"""
    print("🔍 简单测试OKX API连接...")
    
    try:
        # 测试1: 获取服务器时间
        print("   测试1: 获取服务器时间...")
        response = requests.get("https://www.okx.com/api/v5/public/time", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务器时间: {data}")
        else:
            print(f"   ❌ 服务器时间获取失败: {response.status_code}")
            return False
        
        # 测试2: 获取K线数据
        print("   测试2: 获取BTC-USDT K线数据...")
        params = {
            'instId': 'BTC-USDT',
            'bar': '1m',
            'limit': '5'
        }
        response = requests.get("https://www.okx.com/api/v5/market/candles", params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == '0' and data.get('data'):
                klines = data['data']
                print(f"   ✅ K线数据获取成功: {len(klines)}条")
                if klines:
                    latest = klines[0]
                    timestamp = int(latest[0])
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    print(f"   最新K线: {dt.strftime('%Y-%m-%d %H:%M:%S')}, 收盘价={latest[4]}")
                return True
            else:
                print(f"   ❌ K线数据格式错误: {data}")
                return False
        else:
            print(f"   ❌ K线数据获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def test_network():
    """测试网络连接"""
    print("\n🔍 测试网络连接...")
    
    try:
        # 测试百度
        response = requests.get("https://www.baidu.com", timeout=5)
        if response.status_code == 200:
            print("   ✅ 百度连接正常")
        else:
            print(f"   ⚠️ 百度连接异常: {response.status_code}")
        
        # 测试OKX主站
        response = requests.get("https://www.okx.com", timeout=10)
        if response.status_code == 200:
            print("   ✅ OKX主站连接正常")
        else:
            print(f"   ⚠️ OKX主站连接异常: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 网络测试异常: {e}")

def main():
    """主函数"""
    print("🔧 简单OKX连接测试")
    print("=" * 40)
    
    # 测试网络
    test_network()
    
    # 测试OKX API
    result = test_okx_simple()
    
    print("\n" + "=" * 40)
    if result:
        print("🎉 OKX连接测试通过！")
        print("✅ 可以正常获取K线数据")
    else:
        print("❌ OKX连接测试失败")
        print("🔧 请检查网络连接")

if __name__ == "__main__":
    main()
