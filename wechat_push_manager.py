#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC量化交易系统 - 微信推送管理器
基于Server酱实现微信消息推送功能
"""

import asyncio
import aiohttp
import json
import logging
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import re

class PushType(Enum):
    """推送类型枚举"""
    STRATEGY_TRIGGER = "strategy_trigger"
    TRADING_SIGNAL = "trading_signal"
    SYSTEM_ALERT = "system_alert"
    ERROR_NOTIFICATION = "error_notification"

@dataclass
class PushMessage:
    """推送消息结构"""
    title: str
    content: str
    push_type: PushType
    timestamp: datetime
    extra_data: Dict = None

@dataclass
class PushHistory:
    """推送历史记录"""
    id: int
    title: str
    content: str
    push_type: str
    status: str  # success, failed, pending
    timestamp: datetime
    response_data: str = None

class WeChatPushManager:
    """微信推送管理器"""
    
    def __init__(self, db_manager=None):
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.session = None
        
        # Server酱配置
        self.server_chan_url = "https://sct.ftqq.com"
        self.send_key = None
        self.is_enabled = False
        
        # 推送配置
        self.strategy_push_config = {}  # 策略推送配置
        self.signal_push_config = {     # 交易信号推送配置
            'buy_signal': False,
            'sell_signal': False
        }
        
        # 频率限制配置
        self.rate_limit = {
            'max_per_minute': 10,
            'max_per_hour': 100,
            'max_per_day': 500
        }
        
        # 推送历史记录
        self.push_history = []
        self.last_push_times = {}
        
        # 初始化数据库表
        self.init_database()

    async def initialize(self):
        """初始化推送管理器"""
        try:
            # 创建HTTP会话
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'WMZC Trading System 1.0'}
            )
            
            # 加载配置
            await self.load_config()
            
            self.logger.info("微信推送管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化微信推送管理器失败: {e}")
            raise

    async def close(self):
        """关闭推送管理器"""
        try:
            if self.session:
                await self.session.close()
            self.logger.info("微信推送管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭微信推送管理器失败: {e}")

    def init_database(self):
        """初始化数据库表"""
        try:
            if not self.db_manager:
                return
                
            # 创建推送配置表
            self.db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS wechat_push_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    send_key TEXT,
                    is_enabled BOOLEAN DEFAULT 0,
                    strategy_config TEXT,
                    signal_config TEXT,
                    rate_limit_config TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建推送历史表
            self.db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS wechat_push_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    push_type TEXT NOT NULL,
                    status TEXT NOT NULL,
                    response_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.logger.info("微信推送数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化推送数据库表失败: {e}")

    async def load_config(self):
        """加载推送配置"""
        try:
            if not self.db_manager:
                return
                
            # 查询配置
            result = self.db_manager.fetch_one(
                "SELECT * FROM wechat_push_config ORDER BY id DESC LIMIT 1"
            )
            
            if result:
                self.send_key = self.decrypt_send_key(result[1]) if result[1] else None
                self.is_enabled = bool(result[2])
                
                # 解析JSON配置
                if result[3]:
                    self.strategy_push_config = json.loads(result[3])
                if result[4]:
                    self.signal_push_config = json.loads(result[4])
                if result[5]:
                    self.rate_limit = json.loads(result[5])
                    
                self.logger.info("微信推送配置加载完成")
            else:
                self.logger.info("未找到微信推送配置，使用默认设置")
                
        except Exception as e:
            self.logger.error(f"加载推送配置失败: {e}")

    async def save_config(self):
        """保存推送配置"""
        try:
            if not self.db_manager:
                return False
                
            # 加密SendKey
            encrypted_send_key = self.encrypt_send_key(self.send_key) if self.send_key else None
            
            # 序列化配置
            strategy_config_json = json.dumps(self.strategy_push_config)
            signal_config_json = json.dumps(self.signal_push_config)
            rate_limit_json = json.dumps(self.rate_limit)
            
            # 保存到数据库
            self.db_manager.execute_query('''
                INSERT OR REPLACE INTO wechat_push_config 
                (id, send_key, is_enabled, strategy_config, signal_config, rate_limit_config, updated_at)
                VALUES (1, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (encrypted_send_key, self.is_enabled, strategy_config_json, 
                  signal_config_json, rate_limit_json))
            
            self.logger.info("微信推送配置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存推送配置失败: {e}")
            return False

    def encrypt_send_key(self, send_key: str) -> str:
        """加密SendKey"""
        try:
            if not send_key:
                return ""
            # 简单的加密方法，实际应用中应使用更安全的加密
            return hashlib.sha256(f"wmzc_{send_key}_salt".encode()).hexdigest()[:32] + send_key
        except Exception:
            return send_key

    def decrypt_send_key(self, encrypted_key: str) -> str:
        """解密SendKey"""
        try:
            if not encrypted_key or len(encrypted_key) <= 32:
                return encrypted_key
            # 提取原始SendKey
            return encrypted_key[32:]
        except Exception:
            return encrypted_key

    def validate_send_key(self, send_key: str) -> bool:
        """验证SendKey格式"""
        try:
            if not send_key:
                return False
            
            # Server酱SendKey格式验证
            # 通常格式为: SCT + 数字字母组合
            pattern = r'^SCT[A-Za-z0-9]{20,}$'
            return bool(re.match(pattern, send_key))
            
        except Exception:
            return False

    async def test_send_key(self, send_key: str) -> Dict[str, Any]:
        """测试SendKey有效性"""
        try:
            if not self.validate_send_key(send_key):
                return {
                    'success': False,
                    'message': 'SendKey格式不正确',
                    'error_code': 'INVALID_FORMAT'
                }
            
            # 发送测试消息
            test_message = PushMessage(
                title="WMZC系统测试",
                content="这是一条测试消息，如果您收到此消息，说明微信推送配置成功！",
                push_type=PushType.SYSTEM_ALERT,
                timestamp=datetime.now()
            )
            
            result = await self._send_message(send_key, test_message)
            
            if result['success']:
                return {
                    'success': True,
                    'message': '测试消息发送成功！请检查您的微信',
                    'response': result.get('response')
                }
            else:
                return {
                    'success': False,
                    'message': f"测试失败: {result.get('message', '未知错误')}",
                    'error_code': result.get('error_code', 'UNKNOWN')
                }
                
        except Exception as e:
            self.logger.error(f"测试SendKey失败: {e}")
            return {
                'success': False,
                'message': f'测试过程出错: {e}',
                'error_code': 'EXCEPTION'
            }

    async def send_strategy_trigger(self, strategy_name: str, trigger_info: Dict):
        """发送策略触发推送"""
        try:
            if not self.is_enabled or not self.send_key:
                return
                
            # 检查策略是否启用推送
            if not self.strategy_push_config.get(strategy_name, False):
                return
                
            # 检查频率限制
            if not self._check_rate_limit(PushType.STRATEGY_TRIGGER):
                self.logger.warning(f"策略触发推送频率限制: {strategy_name}")
                return
            
            # 构建推送消息
            title = f"🎯 策略触发: {strategy_name}"
            content = f"""
**策略名称**: {strategy_name}
**触发时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**交易对**: {trigger_info.get('symbol', 'N/A')}
**当前价格**: {trigger_info.get('price', 'N/A')}
**信号类型**: {trigger_info.get('signal_type', 'N/A')}
**信号强度**: {trigger_info.get('signal_strength', 'N/A')}

有策略触发啦，快去看吧！
"""
            
            message = PushMessage(
                title=title,
                content=content,
                push_type=PushType.STRATEGY_TRIGGER,
                timestamp=datetime.now(),
                extra_data=trigger_info
            )
            
            await self._send_message(self.send_key, message)
            
        except Exception as e:
            self.logger.error(f"发送策略触发推送失败: {e}")

    async def send_trading_signal(self, signal_type: str, signal_info: Dict):
        """发送交易信号推送"""
        try:
            if not self.is_enabled or not self.send_key:
                return
                
            # 检查信号类型是否启用推送
            signal_key = f"{signal_type.lower()}_signal"
            if not self.signal_push_config.get(signal_key, False):
                return
                
            # 检查频率限制
            if not self._check_rate_limit(PushType.TRADING_SIGNAL):
                self.logger.warning(f"交易信号推送频率限制: {signal_type}")
                return
            
            # 构建推送消息
            signal_emoji = "🟢" if signal_type.lower() == "buy" else "🔴"
            title = f"{signal_emoji} 交易信号: {signal_type.upper()}"
            content = f"""
**信号类型**: {signal_type.upper()}
**交易对**: {signal_info.get('symbol', 'N/A')}
**当前价格**: {signal_info.get('price', 'N/A')}
**信号时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**策略来源**: {signal_info.get('strategy', 'N/A')}
**信号强度**: {signal_info.get('strength', 'N/A')}

有新的交易信号啦！
"""
            
            message = PushMessage(
                title=title,
                content=content,
                push_type=PushType.TRADING_SIGNAL,
                timestamp=datetime.now(),
                extra_data=signal_info
            )
            
            await self._send_message(self.send_key, message)
            
        except Exception as e:
            self.logger.error(f"发送交易信号推送失败: {e}")

    async def _send_message(self, send_key: str, message: PushMessage) -> Dict[str, Any]:
        """发送消息到Server酱"""
        try:
            if not self.session:
                await self.initialize()
            
            # 构建请求URL
            url = f"{self.server_chan_url}/{send_key}.send"
            
            # 构建请求数据
            data = {
                'title': message.title,
                'desp': message.content
            }
            
            # 发送POST请求
            async with self.session.post(url, data=data) as response:
                response_text = await response.text()
                response_data = json.loads(response_text) if response_text else {}
                
                # 记录推送历史
                await self._save_push_history(message, 
                                            'success' if response.status == 200 else 'failed',
                                            response_text)
                
                if response.status == 200 and response_data.get('code') == 0:
                    self.logger.info(f"推送消息成功: {message.title}")
                    return {
                        'success': True,
                        'message': '推送成功',
                        'response': response_data
                    }
                else:
                    error_msg = response_data.get('message', f'HTTP {response.status}')
                    self.logger.error(f"推送消息失败: {error_msg}")
                    return {
                        'success': False,
                        'message': error_msg,
                        'error_code': response_data.get('code', response.status)
                    }
                    
        except Exception as e:
            self.logger.error(f"发送推送消息异常: {e}")
            await self._save_push_history(message, 'failed', str(e))
            return {
                'success': False,
                'message': str(e),
                'error_code': 'EXCEPTION'
            }

    def _check_rate_limit(self, push_type: PushType) -> bool:
        """检查推送频率限制"""
        try:
            now = datetime.now()
            type_key = push_type.value
            
            if type_key not in self.last_push_times:
                self.last_push_times[type_key] = []
            
            # 清理过期记录
            minute_ago = now - timedelta(minutes=1)
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            
            recent_pushes = self.last_push_times[type_key]
            recent_pushes = [t for t in recent_pushes if t > day_ago]
            
            # 检查各时间段限制
            minute_count = len([t for t in recent_pushes if t > minute_ago])
            hour_count = len([t for t in recent_pushes if t > hour_ago])
            day_count = len(recent_pushes)
            
            if (minute_count >= self.rate_limit['max_per_minute'] or
                hour_count >= self.rate_limit['max_per_hour'] or
                day_count >= self.rate_limit['max_per_day']):
                return False
            
            # 记录本次推送时间
            recent_pushes.append(now)
            self.last_push_times[type_key] = recent_pushes
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查推送频率限制失败: {e}")
            return True  # 出错时允许推送

    async def _save_push_history(self, message: PushMessage, status: str, response_data: str):
        """保存推送历史记录"""
        try:
            if not self.db_manager:
                return
                
            self.db_manager.execute_query('''
                INSERT INTO wechat_push_history 
                (title, content, push_type, status, response_data)
                VALUES (?, ?, ?, ?, ?)
            ''', (message.title, message.content, message.push_type.value, 
                  status, response_data))
            
        except Exception as e:
            self.logger.error(f"保存推送历史失败: {e}")

    async def get_push_history(self, limit: int = 100) -> List[PushHistory]:
        """获取推送历史记录"""
        try:
            if not self.db_manager:
                return []
                
            results = self.db_manager.fetch_all('''
                SELECT id, title, content, push_type, status, response_data, created_at
                FROM wechat_push_history 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            history = []
            for row in results:
                history.append(PushHistory(
                    id=row[0],
                    title=row[1],
                    content=row[2],
                    push_type=row[3],
                    status=row[4],
                    response_data=row[5],
                    timestamp=datetime.fromisoformat(row[6])
                ))
            
            return history
            
        except Exception as e:
            self.logger.error(f"获取推送历史失败: {e}")
            return []

    def get_server_chan_info(self) -> str:
        """获取Server酱说明信息"""
        return """
Server酱是什么？

「Server酱」，英文名「ServerChan」，是一款「手机」和「服务器」、「智能设备」之间的通信软件。

说人话？就是从服务器、路由器等设备上推消息到手机的工具。

使用步骤：
1. 访问 https://sct.ftqq.com/ 注册账号
2. 微信扫码登录
3. 获取您的SendKey
4. 将SendKey填入上方输入框即可

开通并使用只需要一分钟！

参考资料：
- 官方网站：https://sct.ftqq.com/
- 多语言调用实例：https://gitee.com/easychen/serverchan-demo
- SDK文档：https://github.com/easychen/serverchan-sdk
"""
