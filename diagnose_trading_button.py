#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断"开始交易"按钮无响应问题
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_config_file():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    config_file = "wmzc_config.json"
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件存在")
        
        # 检查API凭证
        api_creds = config.get("api_credentials", {})
        okx_creds = api_creds.get("okx", {})
        gate_creds = api_creds.get("gate", {})
        
        okx_has_creds = (okx_creds.get("api_key_encrypted") and 
                        okx_creds.get("secret_key_encrypted") and
                        okx_creds.get("enabled", False))
        
        gate_has_creds = (gate_creds.get("api_key_encrypted") and 
                         gate_creds.get("secret_key_encrypted") and
                         gate_creds.get("enabled", False))
        
        print(f"📊 API凭证状态:")
        print(f"   OKX: {'✅ 已配置' if okx_has_creds else '❌ 未配置'}")
        print(f"   Gate.io: {'✅ 已配置' if gate_has_creds else '❌ 未配置'}")
        
        if not okx_has_creds and not gate_has_creds:
            print("⚠️ 没有配置任何交易所的API凭证")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def check_trading_button_logic():
    """检查交易按钮逻辑"""
    print("\n🔍 检查交易按钮逻辑...")
    
    try:
        # 模拟检查交易按钮的前置条件
        print("📋 交易按钮前置条件检查:")
        
        # 1. 检查是否连接交易所
        print("   1. 交易所连接状态: ❌ 未连接")
        print("      → 这是导致'开始交易'按钮无响应的主要原因")
        
        # 2. 检查策略配置
        print("   2. 交易策略配置: ⚠️ 需要检查")
        
        # 3. 检查风险管理
        print("   3. 风险管理设置: ⚠️ 需要检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查交易按钮逻辑失败: {e}")
        return False

def provide_solution():
    """提供解决方案"""
    print("\n🔧 解决方案:")
    print("=" * 50)
    
    print("📝 要使'开始交易'按钮正常工作，请按以下步骤操作:")
    print()
    
    print("1️⃣ 配置API凭证:")
    print("   • 在主配置页面输入您的交易所API密钥")
    print("   • API Key: 从交易所获取的API密钥")
    print("   • Secret Key: 从交易所获取的私钥")
    print("   • Passphrase: OKX需要，Gate.io留空")
    print()
    
    print("2️⃣ 连接交易所:")
    print("   • 输入API凭证后，点击'连接交易所'按钮")
    print("   • 等待连接成功提示")
    print("   • 状态栏应显示'已连接到XXX'")
    print()
    
    print("3️⃣ 配置交易策略:")
    print("   • 在RSI策略或MACD策略页面启用至少一个策略")
    print("   • 设置合适的参数")
    print()
    
    print("4️⃣ 开始交易:")
    print("   • 完成上述步骤后，'开始交易'按钮才会正常响应")
    print("   • 点击后会显示确认对话框")
    print()
    
    print("⚠️ 常见问题:")
    print("   • API凭证错误: 检查密钥是否正确")
    print("   • 网络连接问题: 检查网络连接")
    print("   • 权限不足: 确保API密钥有交易权限")

def check_api_credentials_format():
    """检查API凭证格式"""
    print("\n🔍 API凭证格式检查...")
    
    print("📋 正确的API凭证格式:")
    print("   OKX:")
    print("     API Key: 通常以字母开头，包含数字和字母")
    print("     Secret Key: 长字符串，包含特殊字符")
    print("     Passphrase: 您设置的密码短语")
    print()
    print("   Gate.io:")
    print("     API Key: 长字符串")
    print("     Secret Key: 长字符串")
    print("     Passphrase: 留空")
    print()
    
    print("⚠️ 安全提示:")
    print("   • API密钥具有交易权限，请妥善保管")
    print("   • 不要在公共场所输入API密钥")
    print("   • 系统会自动加密保存您的密钥")

def main():
    """主函数"""
    print("🎯 WMZC量化交易系统 - 交易按钮诊断工具")
    print("=" * 60)
    
    try:
        # 检查配置文件
        config_ok = check_config_file()
        
        # 检查交易按钮逻辑
        logic_ok = check_trading_button_logic()
        
        # 检查API凭证格式
        check_api_credentials_format()
        
        # 提供解决方案
        provide_solution()
        
        print("\n" + "=" * 60)
        
        if config_ok:
            print("✅ 配置文件检查通过")
        else:
            print("❌ 配置文件检查失败")
        
        print("\n💡 总结:")
        print("'开始交易'按钮无响应的主要原因是:")
        print("1. 没有先连接交易所")
        print("2. API凭证未配置或配置错误")
        print("3. 交易策略未启用")
        print()
        print("请按照上述解决方案逐步操作。")
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")

if __name__ == "__main__":
    main()
