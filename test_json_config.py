#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON配置管理器功能
"""

import os
import json
import tempfile
import shutil

def test_json_config_manager():
    """测试JSON配置管理器"""
    print("🔍 测试JSON配置管理器...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    test_config_file = os.path.join(temp_dir, "test_config.json")
    
    try:
        from asp import JSONConfigManager
        
        # 创建配置管理器
        config_manager = JSONConfigManager(test_config_file)
        
        # 测试1：默认配置创建
        assert os.path.exists(test_config_file), "配置文件应该被创建"
        print("✅ 默认配置文件创建成功")
        
        # 测试2：API凭证保存
        success = config_manager.save_api_credentials(
            exchange="OKX",
            api_key="test_api_key_123",
            secret_key="test_secret_key_456",
            passphrase="test_passphrase_789"
        )
        assert success, "API凭证保存应该成功"
        print("✅ API凭证保存成功")
        
        # 测试3：API凭证加载
        credentials = config_manager.load_api_credentials("OKX")
        assert credentials['api_key'] == "test_api_key_123", "API Key应该正确"
        assert credentials['secret_key'] == "test_secret_key_456", "Secret Key应该正确"
        assert credentials['passphrase'] == "test_passphrase_789", "Passphrase应该正确"
        print("✅ API凭证加载成功")
        
        # 测试4：配置设置和获取
        config_manager.set_config("trading_parameters", "position_size", 0.5)
        position_size = config_manager.get_config("trading_parameters", "position_size")
        assert position_size == 0.5, "配置值应该正确"
        print("✅ 配置设置和获取成功")
        
        # 测试5：配置导出
        export_file = os.path.join(temp_dir, "exported_config.json")
        success = config_manager.export_config(export_file, include_sensitive=False)
        assert success, "配置导出应该成功"
        assert os.path.exists(export_file), "导出文件应该存在"
        print("✅ 配置导出成功")
        
        # 测试6：配置导入
        new_config_file = os.path.join(temp_dir, "new_config.json")
        new_config_manager = JSONConfigManager(new_config_file)
        success = new_config_manager.import_config(export_file)
        assert success, "配置导入应该成功"
        print("✅ 配置导入成功")
        
        # 测试7：验证导入的配置
        imported_position_size = new_config_manager.get_config("trading_parameters", "position_size")
        assert imported_position_size == 0.5, "导入的配置值应该正确"
        print("✅ 导入配置验证成功")
        
        # 测试8：配置重置
        success = config_manager.reset_to_default()
        assert success, "配置重置应该成功"
        reset_position_size = config_manager.get_config("trading_parameters", "position_size")
        assert reset_position_size == 0.1, "重置后应该是默认值"
        print("✅ 配置重置成功")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON配置管理器测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def test_config_encryption():
    """测试配置加密功能"""
    print("🔍 测试配置加密功能...")
    
    temp_dir = tempfile.mkdtemp()
    test_config_file = os.path.join(temp_dir, "test_encryption_config.json")
    
    try:
        from asp import JSONConfigManager
        
        config_manager = JSONConfigManager(test_config_file)
        
        # 保存API凭证
        config_manager.save_api_credentials(
            exchange="OKX",
            api_key="sensitive_api_key",
            secret_key="sensitive_secret_key",
            passphrase="sensitive_passphrase"
        )
        
        # 检查配置文件内容是否加密
        with open(test_config_file, 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # 敏感信息不应该以明文形式出现
        assert "sensitive_api_key" not in config_content, "API Key不应该以明文存储"
        assert "sensitive_secret_key" not in config_content, "Secret Key不应该以明文存储"
        assert "sensitive_passphrase" not in config_content, "Passphrase不应该以明文存储"
        
        # 但应该包含加密字段
        config_data = json.loads(config_content)
        okx_creds = config_data["api_credentials"]["okx"]
        assert okx_creds["api_key_encrypted"] != "", "应该有加密的API Key"
        assert okx_creds["secret_key_encrypted"] != "", "应该有加密的Secret Key"
        assert okx_creds["passphrase_encrypted"] != "", "应该有加密的Passphrase"
        
        print("✅ 配置加密测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置加密测试失败: {e}")
        return False
    finally:
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def test_config_persistence():
    """测试配置持久化"""
    print("🔍 测试配置持久化...")
    
    temp_dir = tempfile.mkdtemp()
    test_config_file = os.path.join(temp_dir, "test_persistence_config.json")
    
    try:
        from asp import JSONConfigManager
        
        # 第一次创建配置管理器并保存数据
        config_manager1 = JSONConfigManager(test_config_file)
        config_manager1.save_api_credentials("OKX", "key1", "secret1", "pass1")
        config_manager1.set_config("trading_parameters", "position_size", 0.8)
        
        # 创建新的配置管理器实例（模拟程序重启）
        config_manager2 = JSONConfigManager(test_config_file)
        
        # 验证数据是否持久化
        credentials = config_manager2.load_api_credentials("OKX")
        assert credentials['api_key'] == "key1", "API Key应该持久化"
        
        position_size = config_manager2.get_config("trading_parameters", "position_size")
        assert position_size == 0.8, "配置值应该持久化"
        
        print("✅ 配置持久化测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置持久化测试失败: {e}")
        return False
    finally:
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

if __name__ == "__main__":
    print("🔍 开始测试JSON配置管理功能...")
    
    # 测试基本功能
    test1 = test_json_config_manager()
    
    print("\n" + "="*50 + "\n")
    
    # 测试加密功能
    test2 = test_config_encryption()
    
    print("\n" + "="*50 + "\n")
    
    # 测试持久化
    test3 = test_config_persistence()
    
    print("\n" + "="*50 + "\n")
    
    if all([test1, test2, test3]):
        print("🎉 所有测试通过！JSON配置管理功能正常！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
