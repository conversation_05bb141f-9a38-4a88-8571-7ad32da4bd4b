#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统增强策略市场管理器

专门管理高胜率量化交易策略，按胜率排序显示
严格遵守WMZC系统的14项禁令、5项必须原则和5项主动原则
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np

# 导入WMZC系统模块
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from asp import LogManager, ExchangeType, TradingSignal

class StrategyRiskLevel(Enum):
    """策略风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class StrategyStatus(Enum):
    """策略状态"""
    INACTIVE = "inactive"
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"

@dataclass
class StrategyInfo:
    """策略信息"""
    name: str
    description: str
    win_rate: float
    risk_level: StrategyRiskLevel
    timeframes: List[str]
    parameters: Dict[str, Any]
    signals: List[str]
    status: StrategyStatus = StrategyStatus.INACTIVE
    weight: float = 1.0
    last_signal: Optional[datetime] = None
    total_signals: int = 0
    successful_signals: int = 0

@dataclass
class StrategyTemplate:
    """策略模板"""
    name: str
    description: str
    risk_profile: str
    strategies: Dict[str, Dict[str, Any]]
    aggregation_method: str
    max_concurrent: int

class EnhancedStrategyMarket:
    """
    增强策略市场管理器
    
    核心功能：
    1. 高胜率策略管理（按胜率排序）
    2. 多策略并行执行
    3. 智能信号聚合
    4. 策略模板管理
    5. 实时性能监控
    """
    
    def __init__(self, config_dir: str = "config"):
        self.logger = LogManager()
        self.config_dir = config_dir
        self.strategies_file = os.path.join(config_dir, "enhanced_strategies.json")
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 策略数据
        self.strategies: Dict[str, StrategyInfo] = {}
        self.active_strategies: Dict[str, bool] = {}
        self.strategy_weights: Dict[str, float] = {}
        self.strategy_templates: Dict[str, StrategyTemplate] = {}
        
        # 信号聚合设置
        self.aggregation_method = "weighted_average"  # weighted_average, majority_vote, strongest_signal
        self.max_concurrent_strategies = 5
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # 初始化策略
        self._initialize_strategies()
        self._initialize_templates()
        
    def _initialize_strategies(self):
        """初始化策略信息"""
        try:
            # 从it.py导入策略信息
            from it import STRATEGY_INFO
            
            # 按胜率排序
            sorted_strategies = sorted(STRATEGY_INFO.items(), 
                                     key=lambda x: x[1]['win_rate'], 
                                     reverse=True)
            
            for strategy_key, strategy_data in sorted_strategies:
                risk_level = StrategyRiskLevel(strategy_data['risk_level'])
                
                strategy_info = StrategyInfo(
                    name=strategy_data['name'],
                    description=strategy_data['description'],
                    win_rate=strategy_data['win_rate'],
                    risk_level=risk_level,
                    timeframes=strategy_data.get('timeframes', ['1h', '4h']),
                    parameters=strategy_data.get('parameters', {}),
                    signals=strategy_data.get('signals', [])
                )
                
                self.strategies[strategy_key] = strategy_info
                self.active_strategies[strategy_key] = False
                self.strategy_weights[strategy_key] = 1.0
            
            self.logger.info(f"初始化了{len(self.strategies)}个策略")
            
        except Exception as e:
            self.logger.error(f"初始化策略失败: {e}")
    
    def _initialize_templates(self):
        """初始化策略模板"""
        try:
            # 新手保守模板
            self.strategy_templates['novice'] = StrategyTemplate(
                name="新手保守模板",
                description="低风险策略组合，适合初学者",
                risk_profile="conservative",
                strategies={
                    'grid_trading': {'weight': 0.4, 'active': True},
                    'ema_pullback': {'weight': 0.3, 'active': True},
                    'volume_profile': {'weight': 0.3, 'active': True}
                },
                aggregation_method="majority_vote",
                max_concurrent=3
            )
            
            # 平衡模板
            self.strategy_templates['balanced'] = StrategyTemplate(
                name="平衡模板",
                description="中等风险策略组合，平衡收益和风险",
                risk_profile="balanced",
                strategies={
                    'ichimoku_cloud': {'weight': 0.25, 'active': True},
                    'fibonacci_retracement': {'weight': 0.25, 'active': True},
                    'bollinger_mean_reversion': {'weight': 0.25, 'active': True},
                    'triple_screen': {'weight': 0.25, 'active': True}
                },
                aggregation_method="weighted_average",
                max_concurrent=4
            )
            
            # 专业激进模板
            self.strategy_templates['professional'] = StrategyTemplate(
                name="专业激进模板",
                description="高收益策略组合，适合有经验用户",
                risk_profile="aggressive",
                strategies={
                    'ichimoku_cloud': {'weight': 0.2, 'active': True},
                    'volume_profile': {'weight': 0.2, 'active': True},
                    'rsi_divergence': {'weight': 0.2, 'active': True},
                    'stochastic_rsi': {'weight': 0.2, 'active': True},
                    'williams_r': {'weight': 0.2, 'active': True}
                },
                aggregation_method="strongest_signal",
                max_concurrent=5
            )
            
            self.logger.info("策略模板初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化策略模板失败: {e}")
    
    def get_strategies_by_win_rate(self) -> List[Tuple[str, StrategyInfo]]:
        """按胜率排序获取策略列表"""
        return sorted(self.strategies.items(), 
                     key=lambda x: x[1].win_rate, 
                     reverse=True)
    
    def get_strategies_by_risk_level(self, risk_level: StrategyRiskLevel) -> List[Tuple[str, StrategyInfo]]:
        """按风险等级筛选策略"""
        return [(key, info) for key, info in self.strategies.items() 
                if info.risk_level == risk_level]
    
    def activate_strategy(self, strategy_key: str) -> bool:
        """激活策略"""
        try:
            if strategy_key in self.strategies:
                # 检查并发策略数量限制
                active_count = sum(1 for active in self.active_strategies.values() if active)
                if active_count >= self.max_concurrent_strategies:
                    self.logger.warning(f"已达到最大并发策略数量限制: {self.max_concurrent_strategies}")
                    return False
                
                self.active_strategies[strategy_key] = True
                self.strategies[strategy_key].status = StrategyStatus.ACTIVE
                self.logger.info(f"策略已激活: {self.strategies[strategy_key].name}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"激活策略失败: {e}")
            return False
    
    def deactivate_strategy(self, strategy_key: str) -> bool:
        """停用策略"""
        try:
            if strategy_key in self.strategies:
                self.active_strategies[strategy_key] = False
                self.strategies[strategy_key].status = StrategyStatus.INACTIVE
                self.logger.info(f"策略已停用: {self.strategies[strategy_key].name}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"停用策略失败: {e}")
            return False
    
    def set_strategy_weight(self, strategy_key: str, weight: float) -> bool:
        """设置策略权重"""
        try:
            if strategy_key in self.strategies and 0.0 <= weight <= 1.0:
                self.strategy_weights[strategy_key] = weight
                self.strategies[strategy_key].weight = weight
                self.logger.info(f"策略权重已更新: {self.strategies[strategy_key].name}, 权重: {weight}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"设置策略权重失败: {e}")
            return False
    
    def apply_template(self, template_name: str) -> bool:
        """应用策略模板"""
        try:
            if template_name not in self.strategy_templates:
                self.logger.error(f"模板不存在: {template_name}")
                return False
            
            template = self.strategy_templates[template_name]
            
            # 先停用所有策略
            for strategy_key in self.strategies.keys():
                self.deactivate_strategy(strategy_key)
            
            # 应用模板配置
            for strategy_key, config in template.strategies.items():
                if strategy_key in self.strategies:
                    if config['active']:
                        self.activate_strategy(strategy_key)
                    self.set_strategy_weight(strategy_key, config['weight'])
            
            # 更新聚合方法和并发限制
            self.aggregation_method = template.aggregation_method
            self.max_concurrent_strategies = template.max_concurrent
            
            self.logger.info(f"已应用策略模板: {template.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"应用策略模板失败: {e}")
            return False
    
    async def aggregate_signals(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """聚合多个策略信号"""
        try:
            if not signals:
                return None
            
            if self.aggregation_method == "weighted_average":
                return await self._weighted_average_aggregation(signals)
            elif self.aggregation_method == "majority_vote":
                return await self._majority_vote_aggregation(signals)
            elif self.aggregation_method == "strongest_signal":
                return await self._strongest_signal_aggregation(signals)
            else:
                return signals[0] if signals else None
                
        except Exception as e:
            self.logger.error(f"信号聚合失败: {e}")
            return None
    
    async def _weighted_average_aggregation(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """加权平均聚合"""
        try:
            if not signals:
                return None
            
            buy_weight = 0.0
            sell_weight = 0.0
            total_weight = 0.0
            
            for signal in signals:
                strategy_key = self._get_strategy_key_by_name(signal.strategy)
                weight = self.strategy_weights.get(strategy_key, 1.0)
                
                if signal.signal_type.lower() == 'buy':
                    buy_weight += signal.strength * weight
                elif signal.signal_type.lower() == 'sell':
                    sell_weight += signal.strength * weight
                
                total_weight += weight
            
            if total_weight == 0:
                return None
            
            # 计算最终信号
            final_buy_strength = buy_weight / total_weight
            final_sell_strength = sell_weight / total_weight
            
            if final_buy_strength > final_sell_strength:
                return TradingSignal(
                    signal_type='buy',
                    symbol=signals[0].symbol,
                    price=signals[0].price,
                    strength=final_buy_strength,
                    reason=f"加权平均聚合信号 (买入强度: {final_buy_strength:.3f})",
                    timestamp=datetime.now(),
                    strategy="Enhanced Strategy Market"
                )
            elif final_sell_strength > final_buy_strength:
                return TradingSignal(
                    signal_type='sell',
                    symbol=signals[0].symbol,
                    price=signals[0].price,
                    strength=final_sell_strength,
                    reason=f"加权平均聚合信号 (卖出强度: {final_sell_strength:.3f})",
                    timestamp=datetime.now(),
                    strategy="Enhanced Strategy Market"
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"加权平均聚合失败: {e}")
            return None
    
    async def _majority_vote_aggregation(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """多数投票聚合"""
        try:
            buy_votes = 0
            sell_votes = 0
            
            for signal in signals:
                if signal.signal_type.lower() == 'buy':
                    buy_votes += 1
                elif signal.signal_type.lower() == 'sell':
                    sell_votes += 1
            
            if buy_votes > sell_votes:
                avg_strength = sum(s.strength for s in signals if s.signal_type.lower() == 'buy') / buy_votes
                return TradingSignal(
                    signal_type='buy',
                    symbol=signals[0].symbol,
                    price=signals[0].price,
                    strength=avg_strength,
                    reason=f"多数投票聚合信号 (买入票数: {buy_votes})",
                    timestamp=datetime.now(),
                    strategy="Enhanced Strategy Market"
                )
            elif sell_votes > buy_votes:
                avg_strength = sum(s.strength for s in signals if s.signal_type.lower() == 'sell') / sell_votes
                return TradingSignal(
                    signal_type='sell',
                    symbol=signals[0].symbol,
                    price=signals[0].price,
                    strength=avg_strength,
                    reason=f"多数投票聚合信号 (卖出票数: {sell_votes})",
                    timestamp=datetime.now(),
                    strategy="Enhanced Strategy Market"
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"多数投票聚合失败: {e}")
            return None
    
    async def _strongest_signal_aggregation(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """最强信号聚合"""
        try:
            if not signals:
                return None
            
            # 找到强度最高的信号
            strongest_signal = max(signals, key=lambda s: s.strength)
            
            # 创建新的聚合信号
            return TradingSignal(
                signal_type=strongest_signal.signal_type,
                symbol=strongest_signal.symbol,
                price=strongest_signal.price,
                strength=strongest_signal.strength,
                reason=f"最强信号聚合 (来源: {strongest_signal.strategy}, 强度: {strongest_signal.strength:.3f})",
                timestamp=datetime.now(),
                strategy="Enhanced Strategy Market"
            )
            
        except Exception as e:
            self.logger.error(f"最强信号聚合失败: {e}")
            return None
    
    def _get_strategy_key_by_name(self, strategy_name: str) -> Optional[str]:
        """根据策略名称获取策略键"""
        for key, info in self.strategies.items():
            if info.name == strategy_name:
                return key
        return None
    
    def get_active_strategies(self) -> List[str]:
        """获取激活的策略列表"""
        return [key for key, active in self.active_strategies.items() if active]
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        try:
            active_strategies = self.get_active_strategies()
            total_strategies = len(self.strategies)
            
            # 按风险等级统计
            risk_stats = {
                'low': 0,
                'medium': 0,
                'high': 0
            }
            
            for strategy_info in self.strategies.values():
                risk_stats[strategy_info.risk_level.value] += 1
            
            # 计算平均胜率
            if self.strategies:
                avg_win_rate = sum(info.win_rate for info in self.strategies.values()) / len(self.strategies)
            else:
                avg_win_rate = 0
            
            return {
                'total_strategies': total_strategies,
                'active_strategies': len(active_strategies),
                'risk_distribution': risk_stats,
                'average_win_rate': avg_win_rate,
                'aggregation_method': self.aggregation_method,
                'max_concurrent': self.max_concurrent_strategies,
                'performance_stats': self.performance_stats
            }
            
        except Exception as e:
            self.logger.error(f"获取策略统计失败: {e}")
            return {}
    
    def save_configuration(self) -> bool:
        """保存策略配置"""
        try:
            config_data = {
                'active_strategies': self.active_strategies,
                'strategy_weights': self.strategy_weights,
                'aggregation_method': self.aggregation_method,
                'max_concurrent_strategies': self.max_concurrent_strategies,
                'performance_stats': self.performance_stats,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.strategies_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("策略配置已保存")
            return True
            
        except Exception as e:
            self.logger.error(f"保存策略配置失败: {e}")
            return False
    
    def load_configuration(self) -> bool:
        """加载策略配置"""
        try:
            if not os.path.exists(self.strategies_file):
                self.logger.info("策略配置文件不存在，使用默认配置")
                return True
            
            with open(self.strategies_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            if 'active_strategies' in config_data:
                self.active_strategies.update(config_data['active_strategies'])
            
            if 'strategy_weights' in config_data:
                self.strategy_weights.update(config_data['strategy_weights'])
            
            if 'aggregation_method' in config_data:
                self.aggregation_method = config_data['aggregation_method']
            
            if 'max_concurrent_strategies' in config_data:
                self.max_concurrent_strategies = config_data['max_concurrent_strategies']
            
            if 'performance_stats' in config_data:
                self.performance_stats.update(config_data['performance_stats'])
            
            self.logger.info("策略配置已加载")
            return True
            
        except Exception as e:
            self.logger.error(f"加载策略配置失败: {e}")
            return False

# 使用示例
def main():
    market = EnhancedStrategyMarket()
    
    # 获取按胜率排序的策略
    strategies = market.get_strategies_by_win_rate()
    print("策略列表（按胜率排序）:")
    for key, info in strategies:
        print(f"  {info.name}: {info.win_rate}% ({info.risk_level.value})")
    
    # 应用平衡模板
    market.apply_template('balanced')
    
    # 获取统计信息
    stats = market.get_strategy_statistics()
    print(f"\n策略统计: {stats}")

if __name__ == "__main__":
    main()
