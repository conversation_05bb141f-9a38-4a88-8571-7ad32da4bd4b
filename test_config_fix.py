#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置循环引用修复效果
"""

def test_config_constants_access():
    """测试ConfigConstants直接访问"""
    print("🔍 测试ConfigConstants直接访问...")
    
    try:
        # 导入并测试ConfigConstants
        from asp import ConfigConstants
        
        # 测试各种配置常量
        print(f"HTTP_TIMEOUT: {ConfigConstants.HTTP_TIMEOUT}")
        print(f"CACHE_MAX_SIZE: {ConfigConstants.CACHE_MAX_SIZE}")
        print(f"LOG_MAX_MEMORY_LOGS: {ConfigConstants.LOG_MAX_MEMORY_LOGS}")
        print(f"DATABASE_PATH: {ConfigConstants.DATABASE_PATH}")
        
        print("✅ ConfigConstants访问成功")
        return True
        
    except Exception as e:
        print(f"❌ ConfigConstants访问失败: {e}")
        return False

def test_config_inheritance():
    """测试Config类继承ConfigConstants"""
    print("🔍 测试Config类继承...")
    
    try:
        from asp import Config
        
        # 测试继承的常量
        print(f"Config.HTTP_TIMEOUT: {Config.HTTP_TIMEOUT}")
        print(f"Config.CACHE_MAX_SIZE: {Config.CACHE_MAX_SIZE}")
        print(f"Config.LOG_MAX_MEMORY_LOGS: {Config.LOG_MAX_MEMORY_LOGS}")
        
        # 验证继承关系
        from asp import ConfigConstants
        assert issubclass(Config, ConfigConstants), "Config应该继承ConfigConstants"
        
        print("✅ Config类继承验证成功")
        return True
        
    except Exception as e:
        print(f"❌ Config类继承验证失败: {e}")
        return False

def test_class_initialization():
    """测试类初始化时的配置访问"""
    print("🔍 测试类初始化...")
    
    try:
        # 测试MemoryCacheManager初始化
        from asp import MemoryCacheManager
        cache_manager = MemoryCacheManager()
        
        print(f"MemoryCacheManager.max_size: {cache_manager.max_size}")
        print(f"MemoryCacheManager.ttl_default: {cache_manager.ttl_default}")
        
        # 测试AsyncTaskManager初始化
        from asp import AsyncTaskManager
        task_manager = AsyncTaskManager()
        
        print(f"AsyncTaskManager.task_timeout: {task_manager.task_timeout}")
        
        print("✅ 类初始化测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 类初始化测试失败: {e}")
        return False

def test_no_circular_import():
    """测试是否还存在循环引用问题"""
    print("🔍 测试循环引用问题...")
    
    try:
        # 尝试导入主要类，看是否有循环引用错误
        from asp import LogManager, MemoryCacheManager, AsyncTaskManager, Config
        
        # 创建实例
        log_manager = LogManager()
        cache_manager = MemoryCacheManager()
        task_manager = AsyncTaskManager()
        
        print("✅ 无循环引用问题")
        return True
        
    except Exception as e:
        print(f"❌ 仍存在循环引用问题: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始测试配置循环引用修复效果...")
    
    # 测试ConfigConstants访问
    test1 = test_config_constants_access()
    
    print("\n" + "="*50 + "\n")
    
    # 测试Config继承
    test2 = test_config_inheritance()
    
    print("\n" + "="*50 + "\n")
    
    # 测试类初始化
    test3 = test_class_initialization()
    
    print("\n" + "="*50 + "\n")
    
    # 测试循环引用
    test4 = test_no_circular_import()
    
    print("\n" + "="*50 + "\n")
    
    if all([test1, test2, test3, test4]):
        print("🎉 所有测试通过！配置循环引用问题已修复！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
