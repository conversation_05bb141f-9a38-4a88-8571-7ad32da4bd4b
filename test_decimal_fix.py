#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Decimal类型转换修复
"""

import pandas as pd
import numpy as np
from decimal import Decimal

def test_decimal_operations():
    """测试Decimal类型在pandas中的操作"""
    print("🔍 测试Decimal类型操作...")
    
    try:
        # 创建包含Decimal类型的数据
        decimal_data = [Decimal('100.5'), Decimal('101.2'), Decimal('99.8'), Decimal('102.1')]
        float_data = [100.5, 101.2, 99.8, 102.1]
        
        print(f"📊 Decimal数据: {decimal_data}")
        print(f"📊 Float数据: {float_data}")
        
        # 测试Decimal类型的pandas Series操作
        try:
            decimal_series = pd.Series(decimal_data)
            print(f"✅ Decimal Series创建成功: {decimal_series.dtype}")
            
            # 尝试运算操作
            result = decimal_series.rolling(window=2).mean()
            print(f"✅ Decimal Series滚动平均成功")
            
        except Exception as e:
            print(f"❌ Decimal Series操作失败: {e}")
        
        # 测试Float类型的pandas Series操作
        try:
            float_series = pd.Series(float_data)
            print(f"✅ Float Series创建成功: {float_series.dtype}")
            
            # 尝试运算操作
            result = float_series.rolling(window=2).mean()
            print(f"✅ Float Series滚动平均成功")
            
        except Exception as e:
            print(f"❌ Float Series操作失败: {e}")
        
        # 测试Decimal转Float
        try:
            converted_data = [float(d) for d in decimal_data]
            converted_series = pd.Series(converted_data)
            print(f"✅ Decimal转Float成功: {converted_series.dtype}")
            
            # 测试KDJ计算中的关键操作
            high = pd.Series([float(d) * 1.01 for d in decimal_data])
            low = pd.Series([float(d) * 0.99 for d in decimal_data])
            close = pd.Series([float(d) for d in decimal_data])
            
            # 模拟KDJ计算的关键步骤
            lowest_low = low.rolling(window=2).min()
            highest_high = high.rolling(window=2).max()
            price_range = highest_high - lowest_low
            
            print(f"✅ KDJ关键计算步骤成功")
            print(f"📊 价格范围: {price_range.tolist()}")
            
        except Exception as e:
            print(f"❌ Decimal转Float操作失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Decimal操作测试失败: {e}")
        return False

def test_kdj_with_decimal_data():
    """测试KDJ计算处理Decimal数据"""
    print("🔍 测试KDJ计算处理Decimal数据...")
    
    try:
        # 模拟包含Decimal的KlineData
        from decimal import Decimal
        
        # 创建测试数据
        decimal_prices = [
            Decimal('100.5'), Decimal('101.2'), Decimal('99.8'), 
            Decimal('102.1'), Decimal('100.9'), Decimal('103.2')
        ]
        
        # 转换为float类型的pandas Series
        high = pd.Series([float(p) * 1.01 for p in decimal_prices])
        low = pd.Series([float(p) * 0.99 for p in decimal_prices])
        close = pd.Series([float(p) for p in decimal_prices])
        
        print(f"📊 High数据类型: {high.dtype}")
        print(f"📊 Low数据类型: {low.dtype}")
        print(f"📊 Close数据类型: {close.dtype}")
        
        # 模拟KDJ计算
        period = 3
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        price_range = highest_high - lowest_low
        
        # 使用numpy.where计算RSV
        rsv = pd.Series(
            np.where(price_range != 0,
                    (close - lowest_low) / price_range * 100,
                    50.0),
            index=close.index
        )
        
        k = rsv.ewm(alpha=1/3).mean()
        d = k.ewm(alpha=1/3).mean()
        j = 3 * k - 2 * d
        
        print(f"✅ KDJ计算成功")
        print(f"📊 K值: {k.iloc[-1]:.2f}")
        print(f"📊 D值: {d.iloc[-1]:.2f}")
        print(f"📊 J值: {j.iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ KDJ计算测试失败: {e}")
        return False

def test_dataframe_conversion():
    """测试DataFrame转换"""
    print("🔍 测试DataFrame转换...")
    
    try:
        from decimal import Decimal
        
        # 模拟KlineData对象
        class MockKlineData:
            def __init__(self, open_price, high_price, low_price, close_price, volume):
                self.open = Decimal(str(open_price))
                self.high = Decimal(str(high_price))
                self.low = Decimal(str(low_price))
                self.close = Decimal(str(close_price))
                self.volume = Decimal(str(volume))
        
        # 创建测试数据
        kline_data = [
            MockKlineData(100.0, 101.0, 99.0, 100.5, 1000),
            MockKlineData(100.5, 102.0, 100.0, 101.2, 1200),
            MockKlineData(101.2, 102.5, 100.8, 99.8, 900),
        ]
        
        # 测试原始方式（可能失败）
        try:
            df_original = pd.DataFrame([{
                'open': data.open,
                'high': data.high,
                'low': data.low,
                'close': data.close,
                'volume': data.volume
            } for data in kline_data])
            
            print(f"✅ 原始DataFrame创建成功")
            print(f"📊 数据类型: {df_original.dtypes.to_dict()}")
            
            # 尝试运算
            result = df_original['high'] - df_original['low']
            print(f"✅ 原始DataFrame运算成功")
            
        except Exception as e:
            print(f"❌ 原始DataFrame操作失败: {e}")
        
        # 测试修复后的方式（应该成功）
        try:
            df_fixed = pd.DataFrame([{
                'open': float(data.open),
                'high': float(data.high),
                'low': float(data.low),
                'close': float(data.close),
                'volume': float(data.volume)
            } for data in kline_data])
            
            print(f"✅ 修复后DataFrame创建成功")
            print(f"📊 数据类型: {df_fixed.dtypes.to_dict()}")
            
            # 尝试运算
            result = df_fixed['high'] - df_fixed['low']
            print(f"✅ 修复后DataFrame运算成功")
            print(f"📊 价格范围: {result.tolist()}")
            
        except Exception as e:
            print(f"❌ 修复后DataFrame操作失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ DataFrame转换测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Decimal类型转换修复...")
    print("=" * 60)
    
    all_passed = True
    
    # 测试基本Decimal操作
    if not test_decimal_operations():
        all_passed = False
    
    print("-" * 40)
    
    # 测试KDJ计算
    if not test_kdj_with_decimal_data():
        all_passed = False
    
    print("-" * 40)
    
    # 测试DataFrame转换
    if not test_dataframe_conversion():
        all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！Decimal类型问题已修复")
        print("\n✅ 修复总结:")
        print("• 将KlineData中的Decimal类型转换为float")
        print("• 修复了DataFrame构建中的数据类型问题")
        print("• 确保pandas运算操作正常工作")
        print("• KDJ计算不再出现类型错误")
        print("\n💡 建议:")
        print("• 重启应用程序以应用修复")
        print("• 观察日志确认KDJ计算错误已消失")
        print("• 验证技术指标显示正常")
    else:
        print("❌ 部分测试失败，请检查相关代码")
    
    return all_passed

if __name__ == "__main__":
    main()
