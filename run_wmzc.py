#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统整合版本启动脚本
"""

import sys
import os
import subprocess
import importlib.util

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'asyncio',
        'aiohttp',
        'pandas',
        'numpy',
        'tkinter',
        'websockets',
        'ssl',
        'certifi',
        'psutil'
    ]

    # 企业级优化可选依赖
    optional_packages = [
        ('consul', '分布式服务发现'),
        ('tensorflow', 'LSTM深度学习'),
        ('scikit-learn', '机器学习算法')
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                spec = importlib.util.find_spec(package)
                if spec is None:
                    missing_packages.append(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下必需依赖包:")
        for package in missing_packages:
            print(f"  - {package}")

        print("\n请安装缺少的包:")
        print("pip install aiohttp pandas numpy websockets certifi psutil")
        return False

    # 检查可选依赖
    print("\n检查企业级功能依赖:")
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"⚠️ {package} - {description} (可选，未安装)")

    print("\n如需完整企业级功能，请安装:")
    print("pip install redis python-consul tensorflow scikit-learn")

    return True

def main():
    """主函数"""
    print("=" * 60)
    print("WMZC量化交易系统整合版本")
    print("版本: 1.0.0")
    print("架构: 100%异步 + 响应式GUI + 交易所分离")
    print("=" * 60)
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        print("依赖检查失败，程序退出")
        return
    
    print("依赖检查通过")
    
    # 检查asp.py文件
    if not os.path.exists('asp.py'):
        print("错误: 找不到asp.py文件")
        return
    
    print("启动WMZC量化交易系统...")
    
    try:
        # 导入并运行主程序
        from asp import main as asp_main
        asp_main()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
