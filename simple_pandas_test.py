#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的pandas兼容性测试
"""

import pandas as pd
import numpy as np

def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试基本pandas功能...")
    
    try:
        # 创建测试数据
        data = pd.Series([1, -1, 2, -2, 3, -3])
        
        # 测试numpy.where (应该工作)
        result1 = pd.Series(np.where(data > 0, data, 0), index=data.index)
        print(f"✅ numpy.where 工作正常: {result1.tolist()}")
        
        # 测试Series.where (应该工作)
        result2 = data.where(data > 0, 0)
        print(f"✅ Series.where 工作正常: {result2.tolist()}")
        
        # 测试pd.where (可能不工作)
        try:
            result3 = pd.where(data > 0, data, 0)
            print(f"✅ pd.where 工作正常: {result3.tolist()}")
        except AttributeError:
            print("❌ pd.where 不可用 (这是预期的)")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_kdj_logic():
    """测试KDJ计算逻辑"""
    print("🔍 测试KDJ计算逻辑...")
    
    try:
        # 模拟KDJ计算的关键部分
        high = pd.Series([105, 107, 103, 108, 106])
        low = pd.Series([95, 97, 93, 98, 96])
        close = pd.Series([100, 102, 98, 105, 103])
        
        period = 3
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        price_range = highest_high - lowest_low
        
        # 使用numpy.where替代pandas.where
        rsv = pd.Series(
            np.where(price_range != 0,
                    (close - lowest_low) / price_range * 100,
                    50.0),
            index=close.index
        )
        
        print(f"✅ KDJ RSV计算成功: {rsv.tolist()}")
        
        # 计算K, D, J
        k = rsv.ewm(alpha=1/3).mean()
        d = k.ewm(alpha=1/3).mean()
        j = 3 * k - 2 * d
        
        print(f"✅ KDJ计算完成")
        print(f"📊 K值: {k.iloc[-1]:.2f}")
        print(f"📊 D值: {d.iloc[-1]:.2f}")
        print(f"📊 J值: {j.iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ KDJ计算测试失败: {e}")
        return False

def test_rsi_logic():
    """测试RSI计算逻辑"""
    print("🔍 测试RSI计算逻辑...")
    
    try:
        # 模拟RSI计算
        prices = pd.Series([100, 102, 98, 105, 103, 107, 104])
        period = 3
        
        delta = prices.diff()
        gain = pd.Series(np.where(delta > 0, delta, 0), index=delta.index).rolling(window=period).mean()
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=delta.index).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        print(f"✅ RSI计算成功")
        print(f"📊 最新RSI值: {rsi.iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ RSI计算测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简单pandas兼容性测试...")
    print("=" * 50)
    
    print(f"📦 pandas版本: {pd.__version__}")
    print(f"📦 numpy版本: {np.__version__}")
    print("-" * 30)
    
    all_passed = True
    
    if not test_basic_functionality():
        all_passed = False
    
    print("-" * 30)
    
    if not test_kdj_logic():
        all_passed = False
    
    print("-" * 30)
    
    if not test_rsi_logic():
        all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有测试通过！")
        print("✅ pandas兼容性问题已修复")
        print("💡 可以重启应用程序查看效果")
    else:
        print("❌ 部分测试失败")
    
    return all_passed

if __name__ == "__main__":
    main()
