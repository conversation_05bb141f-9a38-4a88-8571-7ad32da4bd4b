{"technical_terms": {"API调用": "数据获取", "HTTP请求": "网络请求", "Event loop": "系统连接", "WebSocket": "实时连接", "数据库连接": "数据存储连接", "策略执行": "交易策略运行", "K线数据": "价格走势数据", "订单提交": "交易指令发送", "风险控制": "安全检查", "止盈止损": "自动保护设置", "回测": "历史数据测试", "指标计算": "技术分析计算", "异步": "后台处理", "线程": "并行处理", "缓存": "临时存储", "超时": "等待时间过长", "异常": "意外情况", "初始化": "系统启动", "配置": "设置", "监控": "实时观察", "连接池": "连接管理", "负载均衡": "流量分配", "容错": "错误处理", "重试": "再次尝试", "熔断": "保护机制", "限流": "速度控制"}, "status_codes": {"200": "请求成功", "201": "创建成功", "400": "请求错误", "401": "未授权", "403": "禁止访问", "404": "资源未找到", "429": "请求过于频繁", "500": "服务器内部错误", "502": "网关错误", "503": "服务不可用", "504": "网关超时"}, "log_levels": {"DEBUG": {"icon": "🔧", "name": "调试", "description": "技术调试信息", "color": "gray"}, "INFO": {"icon": "🟢", "name": "正常", "description": "系统正常运行", "color": "green"}, "WARNING": {"icon": "🟡", "name": "提醒", "description": "需要注意的信息", "color": "orange"}, "ERROR": {"icon": "🔴", "name": "错误", "description": "出现了问题", "color": "red"}, "CRITICAL": {"icon": "🚨", "name": "严重", "description": "严重问题", "color": "darkred"}}, "modules": {"WMZC": {"friendly_name": "主系统", "description": "系统核心模块"}, "ExchangeManager": {"friendly_name": "交易所连接", "description": "管理与交易所的连接"}, "DatabasePool": {"friendly_name": "数据存储", "description": "管理数据库连接和存储"}, "LogManager": {"friendly_name": "日志系统", "description": "记录系统运行日志"}, "StrategyEngine": {"friendly_name": "策略引擎", "description": "执行交易策略"}, "RiskManager": {"friendly_name": "风险控制", "description": "管理交易风险"}, "OrderManager": {"friendly_name": "订单管理", "description": "处理交易订单"}, "DataProcessor": {"friendly_name": "数据处理", "description": "处理市场数据"}, "NotificationManager": {"friendly_name": "通知系统", "description": "发送系统通知"}, "ConfigManager": {"friendly_name": "配置管理", "description": "管理系统配置"}}, "common_messages": {"连接成功": {"friendly": "✅ 成功连接到服务器", "explanation": "系统已经成功建立网络连接"}, "连接失败": {"friendly": "❌ 无法连接到服务器", "explanation": "网络连接出现问题，请检查网络设置"}, "数据获取成功": {"friendly": "📊 成功获取市场数据", "explanation": "已经从交易所获取到最新的价格信息"}, "数据获取失败": {"friendly": "📊 获取市场数据失败", "explanation": "无法从交易所获取价格信息，可能是网络问题"}, "策略启动": {"friendly": "🎯 交易策略已启动", "explanation": "自动交易策略开始运行"}, "策略停止": {"friendly": "⏹️ 交易策略已停止", "explanation": "自动交易策略已经停止运行"}, "订单提交": {"friendly": "📝 交易订单已提交", "explanation": "买入或卖出指令已发送到交易所"}, "订单成功": {"friendly": "✅ 交易订单执行成功", "explanation": "买入或卖出操作已经完成"}, "订单失败": {"friendly": "❌ 交易订单执行失败", "explanation": "买入或卖出操作未能完成"}, "风险警告": {"friendly": "⚠️ 检测到交易风险", "explanation": "系统发现潜在的交易风险，请注意"}}, "quick_filters": {"normal": {"name": "正常信息", "description": "查看系统正常运行的记录", "level": "INFO", "keywords": ""}, "important": {"name": "重要提醒", "description": "查看需要注意的信息", "level": "WARNING", "keywords": ""}, "errors": {"name": "错误问题", "description": "查看出现的问题和错误", "level": "ERROR", "keywords": ""}, "trading": {"name": "交易相关", "description": "查看所有交易操作记录", "level": "ALL", "keywords": "交易|策略|买入|卖出|订单"}, "data": {"name": "数据获取", "description": "查看市场数据获取情况", "level": "ALL", "keywords": "API|数据获取|K线|价格"}, "all": {"name": "全部记录", "description": "显示所有记录", "level": "ALL", "keywords": ""}}, "help_tooltips": {"log_level": "选择要查看的信息重要程度", "time_range": "选择要查看的时间范围", "keyword_search": "输入关键词搜索特定内容", "auto_refresh": "自动更新最新的系统状态", "beginner_mode": "隐藏技术细节，只显示易懂的信息", "export_logs": "将日志记录保存到文件中", "clear_display": "清空当前显示的内容"}}