#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - AI助手模块
提供智能对话、交易咨询和市场分析功能
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import queue

class AIAssistant:
    """AI助手核心类"""
    
    def __init__(self):
        """初始化AI助手"""
        self.logger = logging.getLogger(__name__)
        
        # 对话历史
        self.conversation_history = []
        self.max_history_length = 50
        
        # AI配置
        self.ai_config = {
            'model': 'gpt-3.5-turbo',
            'response_style': 'professional',
            'expertise_level': 'expert',
            'language': 'chinese',
            'max_tokens': 1000,
            'temperature': 0.7
        }
        
        # 预设问题模板
        self.question_templates = {
            '市场分析': [
                "分析当前BTC市场趋势",
                "评估ETH价格走势",
                "分析主要加密货币的技术指标",
                "预测短期市场方向"
            ],
            '交易策略': [
                "推荐适合新手的交易策略",
                "分析RSI策略的优缺点",
                "MACD策略的最佳参数设置",
                "如何设置止损和止盈"
            ],
            '技术指标': [
                "解释RSI指标的含义",
                "MACD指标如何使用",
                "布林带的交易信号",
                "移动平均线的作用"
            ],
            '风险管理': [
                "如何控制交易风险",
                "仓位管理的重要性",
                "资金管理策略",
                "如何应对市场波动"
            ]
        }
        
        # 知识库
        self.knowledge_base = self._initialize_knowledge_base()
        
        # 响应队列
        self.response_queue = queue.Queue()
        self.is_processing = False
        
    def _initialize_knowledge_base(self) -> Dict:
        """初始化知识库"""
        return {
            'technical_indicators': {
                'RSI': {
                    'name': '相对强弱指数',
                    'description': 'RSI是衡量价格变动速度和变化的动量振荡器',
                    'usage': '当RSI > 70时表示超买，< 30时表示超卖',
                    'parameters': '通常使用14期作为默认参数'
                },
                'MACD': {
                    'name': '指数平滑移动平均线',
                    'description': 'MACD是趋势跟踪动量指标',
                    'usage': '金叉买入，死叉卖出',
                    'parameters': '快线12，慢线26，信号线9'
                },
                'Bollinger_Bands': {
                    'name': '布林带',
                    'description': '布林带由中轨、上轨和下轨组成',
                    'usage': '价格触及上轨考虑卖出，触及下轨考虑买入',
                    'parameters': '20期移动平均线，2倍标准差'
                }
            },
            'trading_strategies': {
                'trend_following': {
                    'name': '趋势跟踪策略',
                    'description': '跟随市场主要趋势进行交易',
                    'advantages': '在强趋势市场中表现优异',
                    'disadvantages': '在震荡市场中容易产生假信号'
                },
                'mean_reversion': {
                    'name': '均值回归策略',
                    'description': '基于价格会回归均值的理论',
                    'advantages': '在震荡市场中表现良好',
                    'disadvantages': '在强趋势市场中可能持续亏损'
                }
            },
            'risk_management': {
                'position_sizing': {
                    'name': '仓位管理',
                    'description': '控制每笔交易的资金比例',
                    'recommendation': '单笔交易不超过总资金的2-5%'
                },
                'stop_loss': {
                    'name': '止损设置',
                    'description': '限制单笔交易的最大损失',
                    'recommendation': '根据技术分析设置合理的止损位'
                }
            }
        }
    
    async def process_message(self, message: str, context: Dict = None) -> Dict:
        """
        处理用户消息
        
        Args:
            message: 用户输入的消息
            context: 上下文信息（交易数据、市场状态等）
            
        Returns:
            AI回复结果
        """
        try:
            self.is_processing = True
            
            # 添加到对话历史
            self.conversation_history.append({
                'role': 'user',
                'content': message,
                'timestamp': datetime.now()
            })
            
            # 分析消息类型和意图
            intent = self._analyze_intent(message)
            
            # 生成回复
            response = await self._generate_response(message, intent, context)
            
            # 添加回复到历史
            self.conversation_history.append({
                'role': 'assistant',
                'content': response['content'],
                'timestamp': datetime.now(),
                'intent': intent
            })
            
            # 限制历史长度
            if len(self.conversation_history) > self.max_history_length:
                self.conversation_history = self.conversation_history[-self.max_history_length:]
            
            self.is_processing = False
            return response
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            self.is_processing = False
            return {
                'content': f"抱歉，处理您的消息时出现错误: {e}",
                'type': 'error',
                'timestamp': datetime.now()
            }
    
    def _analyze_intent(self, message: str) -> str:
        """分析用户意图"""
        message_lower = message.lower()
        
        # 关键词映射
        intent_keywords = {
            'market_analysis': ['市场', '趋势', '分析', '走势', '预测', '行情'],
            'strategy_advice': ['策略', '交易', '买入', '卖出', '建议', '推荐'],
            'technical_indicator': ['rsi', 'macd', '布林带', '移动平均', '指标', '技术'],
            'risk_management': ['风险', '止损', '止盈', '仓位', '资金管理'],
            'general_question': ['什么', '如何', '怎么', '为什么', '解释']
        }
        
        # 计算匹配度
        intent_scores = {}
        for intent, keywords in intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                intent_scores[intent] = score
        
        # 返回得分最高的意图
        if intent_scores:
            return max(intent_scores, key=intent_scores.get)
        else:
            return 'general_question'
    
    async def _generate_response(self, message: str, intent: str, context: Dict = None) -> Dict:
        """生成AI回复"""
        try:
            # 根据意图生成不同类型的回复
            if intent == 'market_analysis':
                response = self._generate_market_analysis(message, context)
            elif intent == 'strategy_advice':
                response = self._generate_strategy_advice(message, context)
            elif intent == 'technical_indicator':
                response = self._generate_technical_explanation(message)
            elif intent == 'risk_management':
                response = self._generate_risk_advice(message)
            else:
                response = self._generate_general_response(message)
            
            return {
                'content': response,
                'type': intent,
                'timestamp': datetime.now(),
                'confidence': 0.8  # 简化的置信度
            }
            
        except Exception as e:
            self.logger.error(f"生成回复失败: {e}")
            return {
                'content': "抱歉，我现在无法回答这个问题。请稍后再试。",
                'type': 'error',
                'timestamp': datetime.now()
            }
    
    def _generate_market_analysis(self, message: str, context: Dict = None) -> str:
        """生成市场分析回复"""
        base_response = "📊 市场分析建议：\n\n"
        
        if context and 'market_data' in context:
            market_data = context['market_data']
            base_response += f"当前价格: {market_data.get('current_price', 'N/A')}\n"
            base_response += f"24小时变化: {market_data.get('price_change_24h', 'N/A')}\n\n"
        
        base_response += """基于技术分析的一般建议：

🔍 技术指标分析：
• RSI指标：关注超买超卖信号
• MACD：观察金叉死叉形态
• 布林带：注意价格突破情况

📈 趋势判断：
• 短期趋势：观察1小时和4小时图表
• 中期趋势：关注日线和周线走势
• 长期趋势：分析月线图表形态

⚠️ 风险提示：
• 市场有风险，投资需谨慎
• 建议结合多个指标综合判断
• 设置合理的止损和止盈位置"""

        return base_response
    
    def _generate_strategy_advice(self, message: str, context: Dict = None) -> str:
        """生成策略建议回复"""
        return """🎯 交易策略建议：

📋 新手推荐策略：
1. **定投策略**：定期定额投资，降低风险
2. **趋势跟踪**：跟随主要趋势方向交易
3. **网格交易**：在震荡区间内高抛低吸

🔧 进阶策略：
1. **RSI策略**：利用超买超卖信号
2. **MACD策略**：基于金叉死叉操作
3. **布林带策略**：利用价格回归特性

💡 策略要点：
• 严格执行交易计划
• 控制单笔交易风险
• 保持良好的心态
• 持续学习和优化

⚠️ 重要提醒：
任何策略都不能保证盈利，请根据自身情况选择合适的策略。"""
    
    def _generate_technical_explanation(self, message: str) -> str:
        """生成技术指标解释"""
        # 检测具体指标
        if 'rsi' in message.lower():
            return self._explain_rsi()
        elif 'macd' in message.lower():
            return self._explain_macd()
        elif '布林带' in message:
            return self._explain_bollinger_bands()
        else:
            return """📚 技术指标概述：

🔍 主要技术指标：

**RSI (相对强弱指数)**
• 衡量价格动量的振荡器
• 范围：0-100
• 超买：>70，超卖：<30

**MACD (指数平滑移动平均线)**
• 趋势跟踪指标
• 金叉：买入信号
• 死叉：卖出信号

**布林带 (Bollinger Bands)**
• 由上轨、中轨、下轨组成
• 价格通常在带内波动
• 突破上下轨有特殊意义

💡 使用建议：
• 结合多个指标使用
• 考虑市场环境
• 注意背离信号"""
    
    def _explain_rsi(self) -> str:
        """解释RSI指标"""
        indicator_info = self.knowledge_base['technical_indicators']['RSI']
        return f"""📈 RSI指标详解：

**基本概念：**
{indicator_info['description']}

**使用方法：**
{indicator_info['usage']}

**参数设置：**
{indicator_info['parameters']}

**实战技巧：**
• RSI > 80：强烈超买，考虑卖出
• RSI < 20：强烈超卖，考虑买入
• RSI背离：价格与RSI走势相反时的信号
• RSI中线50：多空分界线

**注意事项：**
• 在强趋势中RSI可能长期超买或超卖
• 建议结合其他指标确认信号
• 不同时间周期的RSI意义不同"""
    
    def _explain_macd(self) -> str:
        """解释MACD指标"""
        indicator_info = self.knowledge_base['technical_indicators']['MACD']
        return f"""📊 MACD指标详解：

**基本概念：**
{indicator_info['description']}

**使用方法：**
{indicator_info['usage']}

**参数设置：**
{indicator_info['parameters']}

**信号类型：**
• 金叉：MACD线上穿信号线，买入信号
• 死叉：MACD线下穿信号线，卖出信号
• 零轴突破：MACD线穿越零轴的意义
• 背离：价格与MACD走势背离

**实战应用：**
• 趋势确认：MACD方向与价格趋势一致
• 入场时机：金叉后的回调买入
• 出场时机：死叉或背离信号"""
    
    def _explain_bollinger_bands(self) -> str:
        """解释布林带指标"""
        indicator_info = self.knowledge_base['technical_indicators']['Bollinger_Bands']
        return f"""📏 布林带指标详解：

**基本概念：**
{indicator_info['description']}

**使用方法：**
{indicator_info['usage']}

**参数设置：**
{indicator_info['parameters']}

**交易信号：**
• 上轨压力：价格接近上轨时的阻力
• 下轨支撑：价格接近下轨时的支撑
• 带宽收窄：市场即将变盘的信号
• 带宽扩张：趋势加速的表现

**策略应用：**
• 均值回归：价格偏离中轨后的回归
• 突破交易：价格突破上下轨的跟进
• 波动率交易：根据带宽变化调整策略"""
    
    def _generate_risk_advice(self, message: str) -> str:
        """生成风险管理建议"""
        return """⚠️ 风险管理建议：

💰 资金管理：
• 单笔交易风险不超过总资金的2-5%
• 保持充足的现金储备
• 分散投资，不要把鸡蛋放在一个篮子里

🛡️ 止损策略：
• 技术止损：基于支撑阻力位设置
• 百分比止损：固定比例的损失限制
• 时间止损：超过预期时间未盈利则离场

📊 仓位控制：
• 根据市场波动调整仓位大小
• 趋势明确时可适当加仓
• 不确定时减少仓位或观望

🧠 心理管理：
• 制定交易计划并严格执行
• 不要因为情绪影响交易决策
• 接受亏损是交易的一部分
• 保持学习和改进的心态

📈 风险评估：
• 定期评估投资组合风险
• 关注市场整体风险水平
• 根据个人风险承受能力调整策略"""
    
    def _generate_general_response(self, message: str) -> str:
        """生成通用回复"""
        return """👋 您好！我是WMZC量化交易系统的AI助手。

我可以帮助您：
🔍 分析市场趋势和技术指标
📈 提供交易策略建议
📚 解释各种技术指标的含义
⚠️ 分享风险管理经验
💡 回答量化交易相关问题

请告诉我您想了解什么，我会尽力为您提供专业的建议！

💡 提示：您可以问我关于RSI、MACD、布林带等技术指标的问题，或者询问交易策略和风险管理的建议。"""
    
    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        self.logger.info("对话历史已清空")
    
    def update_config(self, config: Dict):
        """更新AI配置"""
        self.ai_config.update(config)
        self.logger.info(f"AI配置已更新: {config}")
    
    def get_question_templates(self) -> Dict:
        """获取问题模板"""
        return self.question_templates.copy()
    
    def add_to_knowledge_base(self, category: str, key: str, info: Dict):
        """添加到知识库"""
        if category not in self.knowledge_base:
            self.knowledge_base[category] = {}
        self.knowledge_base[category][key] = info
        self.logger.info(f"知识库已更新: {category}.{key}")
    
    def export_conversation(self, format_type: str = 'json') -> str:
        """导出对话记录"""
        try:
            if format_type == 'json':
                return json.dumps(self.conversation_history, 
                                default=str, indent=2, ensure_ascii=False)
            elif format_type == 'txt':
                lines = []
                for msg in self.conversation_history:
                    role = "用户" if msg['role'] == 'user' else "AI助手"
                    timestamp = msg['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                    lines.append(f"[{timestamp}] {role}: {msg['content']}\n")
                return '\n'.join(lines)
            else:
                return "不支持的导出格式"
                
        except Exception as e:
            self.logger.error(f"导出对话记录失败: {e}")
            return f"导出失败: {e}"
