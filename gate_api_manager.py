#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate API管理器 - 全面集成Gate交易所API
支持现货、合约、期权等多种交易产品
"""

import hmac
import hashlib
import json
import os
import time
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class GateProductType(Enum):
    """Gate产品类型"""
    SPOT = "spot"           # 现货
    MARGIN = "margin"       # 杠杆
    FUTURES = "futures"     # 合约
    DELIVERY = "delivery"   # 交割
    OPTIONS = "options"     # 期权

class GateOrderType(Enum):
    """Gate订单类型"""
    LIMIT = "limit"         # 限价单
    MARKET = "market"       # 市价单
    IOC = "ioc"            # 立即成交或取消
    FOK = "fok"            # 全部成交或取消

class GateOrderSide(Enum):
    """Gate订单方向"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class GateConfig:
    """Gate配置"""
    api_key: str
    secret_key: str
    is_sandbox: bool = False
    base_url: str = "https://api.gateio.ws"
    sandbox_url: str = "https://fx-api-testnet.gateio.ws"

class GateAPIManager:
    """Gate API管理器"""
    
    def __init__(self, config: GateConfig):
        self.config = config
        self.base_url = config.sandbox_url if config.is_sandbox else config.base_url
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _generate_signature(self, method: str, url: str, query_string: str, payload: str, timestamp: str) -> str:
        """生成Gate API签名"""
        # Gate签名算法: HASH(method + "\n" + uri + "\n" + query_string + "\n" + payload + "\n" + timestamp)
        message = f"{method}\n{url}\n{query_string}\n{payload}\n{timestamp}"
        signature = hmac.new(
            self.config.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()
        return signature
    
    def _get_headers(self, method: str, url: str, query_string: str = "", payload: str = "") -> Dict[str, str]:
        """获取请求头"""
        timestamp = str(int(time.time()))
        signature = self._generate_signature(method, url, query_string, payload, timestamp)
        
        headers = {
            'KEY': self.config.api_key,
            'SIGN': signature,
            'Timestamp': timestamp,
            'Content-Type': 'application/json'
        }
        
        return headers
    
    async def _request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        query_string = ""
        payload = ""
        
        if params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            
        if data:
            payload = json.dumps(data)
        
        headers = self._get_headers(method, endpoint, query_string, payload)
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                data=payload if payload else None
            ) as response:
                result = await response.json()
                
                # Gate API成功时没有统一的code字段，需要检查HTTP状态码
                if response.status >= 400:
                    raise Exception(f"Gate API Error: {result}")
                
                return result
                
        except Exception as e:
            raise Exception(f"Gate API Request Failed: {str(e)}")
    
    # ==================== 现货交易API ====================
    
    async def get_spot_accounts(self, currency: str = None) -> Dict:
        """获取现货账户余额"""
        endpoint = "/api/v4/spot/accounts"
        params = {}
        if currency:
            params['currency'] = currency
        
        return await self._request("GET", endpoint, params=params)
    
    async def place_spot_order(self, 
                              currency_pair: str,
                              side: GateOrderSide,
                              amount: str,
                              price: str = None,
                              order_type: GateOrderType = GateOrderType.LIMIT,
                              text: str = None) -> Dict:
        """现货下单"""
        endpoint = "/api/v4/spot/orders"
        
        data = {
            "currency_pair": currency_pair,
            "side": side.value,
            "amount": amount,
            "type": order_type.value
        }
        
        if price and order_type != GateOrderType.MARKET:
            data["price"] = price
            
        if text:
            data["text"] = text
        
        return await self._request("POST", endpoint, data=data)
    
    async def cancel_spot_order(self, order_id: str, currency_pair: str) -> Dict:
        """取消现货订单"""
        endpoint = f"/api/v4/spot/orders/{order_id}"
        params = {"currency_pair": currency_pair}
        
        return await self._request("DELETE", endpoint, params=params)
    
    async def get_spot_order(self, order_id: str, currency_pair: str) -> Dict:
        """获取现货订单详情"""
        endpoint = f"/api/v4/spot/orders/{order_id}"
        params = {"currency_pair": currency_pair}
        
        return await self._request("GET", endpoint, params=params)
    
    async def list_spot_orders(self, currency_pair: str, status: str = "open", limit: int = 100) -> Dict:
        """获取现货订单列表"""
        endpoint = "/api/v4/spot/orders"
        params = {
            "currency_pair": currency_pair,
            "status": status,  # open, closed
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    # ==================== 合约交易API ====================
    
    async def get_futures_accounts(self, settle: str = "usdt") -> Dict:
        """获取合约账户信息"""
        endpoint = f"/api/v4/futures/{settle}/accounts"
        return await self._request("GET", endpoint)
    
    async def get_futures_positions(self, settle: str = "usdt", contract: str = None) -> Dict:
        """获取合约持仓"""
        endpoint = f"/api/v4/futures/{settle}/positions"
        params = {}
        if contract:
            params['contract'] = contract
        
        return await self._request("GET", endpoint, params=params)
    
    async def place_futures_order(self, 
                                 settle: str,
                                 contract: str,
                                 size: int,
                                 price: str = None,
                                 tif: str = "gtc",
                                 text: str = None,
                                 reduce_only: bool = False) -> Dict:
        """合约下单"""
        endpoint = f"/api/v4/futures/{settle}/orders"
        
        data = {
            "contract": contract,
            "size": size,  # 正数为买入，负数为卖出
            "tif": tif,    # gtc, ioc, fok
            "reduce_only": reduce_only
        }
        
        if price:
            data["price"] = price
        else:
            data["price"] = "0"  # 市价单价格设为0
            
        if text:
            data["text"] = text
        
        return await self._request("POST", endpoint, data=data)
    
    async def cancel_futures_order(self, settle: str, order_id: str) -> Dict:
        """取消合约订单"""
        endpoint = f"/api/v4/futures/{settle}/orders/{order_id}"
        return await self._request("DELETE", endpoint)
    
    # ==================== 行情数据API ====================
    
    async def get_spot_ticker(self, currency_pair: str = None) -> Dict:
        """获取现货行情"""
        endpoint = "/api/v4/spot/tickers"
        params = {}
        if currency_pair:
            params['currency_pair'] = currency_pair
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_spot_orderbook(self, currency_pair: str, interval: str = "0", limit: int = 10) -> Dict:
        """获取现货订单簿"""
        endpoint = "/api/v4/spot/order_book"
        params = {
            "currency_pair": currency_pair,
            "interval": interval,  # 订单簿精度
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_spot_trades(self, currency_pair: str, limit: int = 100) -> Dict:
        """获取现货成交记录"""
        endpoint = "/api/v4/spot/trades"
        params = {
            "currency_pair": currency_pair,
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_spot_candlesticks(self, currency_pair: str, interval: str = "1m", limit: int = 100) -> Dict:
        """获取现货K线数据"""
        endpoint = "/api/v4/spot/candlesticks"
        params = {
            "currency_pair": currency_pair,
            "interval": interval,  # 1m, 5m, 15m, 30m, 1h, 4h, 8h, 1d, 7d
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_futures_ticker(self, settle: str = "usdt", contract: str = None) -> Dict:
        """获取合约行情"""
        endpoint = f"/api/v4/futures/{settle}/tickers"
        params = {}
        if contract:
            params['contract'] = contract
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_futures_orderbook(self, settle: str, contract: str, interval: str = "0", limit: int = 10) -> Dict:
        """获取合约订单簿"""
        endpoint = f"/api/v4/futures/{settle}/order_book"
        params = {
            "contract": contract,
            "interval": interval,
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_futures_candlesticks(self, settle: str, contract: str, interval: str = "1m", limit: int = 100) -> Dict:
        """获取合约K线数据"""
        endpoint = f"/api/v4/futures/{settle}/candlesticks"
        params = {
            "contract": contract,
            "interval": interval,
            "limit": limit
        }
        
        return await self._request("GET", endpoint, params=params)
    
    # ==================== 钱包API ====================
    
    async def get_wallet_balances(self, currency: str = None) -> Dict:
        """获取钱包余额"""
        endpoint = "/api/v4/wallet/total_balance"
        params = {}
        if currency:
            params['currency'] = currency
        
        return await self._request("GET", endpoint, params=params)
    
    async def transfer_between_accounts(self, 
                                      currency: str,
                                      from_account: str,
                                      to_account: str,
                                      amount: str) -> Dict:
        """账户间转账"""
        endpoint = "/api/v4/wallet/transfers"
        
        data = {
            "currency": currency,
            "from": from_account,  # spot, margin, futures, delivery, cross_margin
            "to": to_account,
            "amount": amount
        }
        
        return await self._request("POST", endpoint, data=data)

# 使用示例
async def example_usage():
    """使用示例"""
    config = GateConfig(
        api_key=os.getenv("GATE_API_KEY", ""),
        secret_key=os.getenv("GATE_SECRET_KEY", ""),
        is_sandbox=True
    )
    
    async with GateAPIManager(config) as gate:
        # 获取现货账户余额
        balance = await gate.get_spot_accounts()
        print("现货账户余额:", balance)
        
        # 获取BTC_USDT行情
        ticker = await gate.get_spot_ticker("BTC_USDT")
        print("BTC_USDT行情:", ticker)
        
        # 下限价买单
        order = await gate.place_spot_order(
            currency_pair="BTC_USDT",
            side=GateOrderSide.BUY,
            amount="0.001",
            price="50000",
            order_type=GateOrderType.LIMIT
        )
        print("下单结果:", order)

if __name__ == "__main__":
    asyncio.run(example_usage())
