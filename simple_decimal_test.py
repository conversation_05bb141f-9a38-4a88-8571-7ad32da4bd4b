#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Decimal类型测试
"""

import pandas as pd
import numpy as np
from decimal import Decimal

def main():
    """主测试函数"""
    print("🚀 测试Decimal类型转换...")
    
    try:
        # 创建Decimal数据
        decimal_values = [Decimal('100.5'), Decimal('101.2'), Decimal('99.8')]
        print(f"📊 Decimal数据: {decimal_values}")
        
        # 转换为float
        float_values = [float(d) for d in decimal_values]
        print(f"📊 Float数据: {float_values}")
        
        # 创建pandas Series
        series = pd.Series(float_values)
        print(f"✅ Series创建成功，类型: {series.dtype}")
        
        # 测试运算
        result = series.rolling(window=2).mean()
        print(f"✅ 滚动平均计算成功")
        
        # 测试KDJ关键操作
        high = pd.Series([f * 1.01 for f in float_values])
        low = pd.Series([f * 0.99 for f in float_values])
        close = pd.Series(float_values)
        
        price_range = high - low
        print(f"✅ 价格范围计算成功: {price_range.tolist()}")
        
        # 测试numpy.where
        rsv = pd.Series(
            np.where(price_range != 0,
                    (close - low) / price_range * 100,
                    50.0),
            index=close.index
        )
        print(f"✅ RSV计算成功: {rsv.tolist()}")
        
        print("🎉 所有测试通过！Decimal转换修复成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
