#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模拟数据功能完全禁用
"""

import os
import re
import logging

def test_mock_data_removal():
    """测试模拟数据功能移除"""
    print("🔍 测试模拟数据功能完全禁用...")
    
    # 要检查的文件列表
    files_to_check = [
        'asp.py',
        'backtest_engine.py'
    ]
    
    # 模拟数据相关的关键词
    mock_keywords = [
        'mock_data',
        'generate_mock',
        '_generate_mock_kline_data',
        'update_enhanced_indicators_with_mock_data',
        '模拟数据',
        '生成模拟',
        'mock_rsi',
        'mock_k',
        'mock_d',
        'mock_j',
        'mock_macd',
        'mock_signal',
        'mock_hist',
        'mock_bb_',
        'random.uniform',
        '使用模拟数据'
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            continue
            
        print(f"\n📁 检查文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            file_issues = []
            
            for i, line in enumerate(lines, 1):
                for keyword in mock_keywords:
                    if keyword in line and not line.strip().startswith('#'):
                        # 检查是否是注释或已禁用的代码
                        if '已禁用' in line or '已完全禁用' in line:
                            continue
                        if '禁用模拟数据版本' in line:
                            continue  # 这是说明已禁用的版本
                        if '替代模拟数据功能' in line:
                            continue  # 这是替代功能的说明
                        if 'def _generate_mock_kline_data' in line:
                            continue  # 这个方法已经被替换为注释
                        if 'def update_enhanced_indicators_with_mock_data' in line:
                            continue  # 这个方法已经被替换
                        if '"""' in line and ('禁用' in line or '替代' in line):
                            continue  # 文档字符串中的说明

                        file_issues.append({
                            'line': i,
                            'content': line.strip(),
                            'keyword': keyword
                        })
            
            if file_issues:
                issues_found.extend([(file_path, issue) for issue in file_issues])
                print(f"❌ 发现 {len(file_issues)} 个模拟数据相关问题")
                for issue in file_issues[:5]:  # 只显示前5个
                    print(f"   行 {issue['line']}: {issue['content'][:80]}...")
            else:
                print(f"✅ 未发现模拟数据相关代码")
                
        except Exception as e:
            print(f"❌ 检查文件失败: {e}")
    
    return len(issues_found) == 0

def test_no_data_status():
    """测试无数据状态功能"""
    print("\n🔍 测试无数据状态功能...")
    
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有无数据状态相关的方法
        required_methods = [
            'show_no_data_status',
            'clear_enhanced_indicators_display'
        ]
        
        missing_methods = []
        for method in required_methods:
            if f"def {method}" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少无数据状态方法: {missing_methods}")
            return False
        else:
            print("✅ 无数据状态功能已实现")
            
        # 检查是否有正确的无数据提示
        no_data_indicators = [
            '❌ 无数据',
            '请连接交易所获取真实数据',
            '❌ 无数据 - 请连接交易所'
        ]
        
        found_indicators = []
        for indicator in no_data_indicators:
            if indicator in content:
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"✅ 找到无数据提示: {len(found_indicators)}个")
            return True
        else:
            print("❌ 未找到无数据提示")
            return False
            
    except Exception as e:
        print(f"❌ 测试无数据状态功能失败: {e}")
        return False

def test_backtest_engine_disabled():
    """测试回测引擎模拟数据禁用"""
    print("\n🔍 测试回测引擎模拟数据禁用...")
    
    try:
        with open('backtest_engine.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有禁用提示
        if "模拟数据功能已完全禁用" in content:
            print("✅ 回测引擎模拟数据功能已禁用")
            
            # 检查是否返回空DataFrame
            if "return pd.DataFrame()" in content:
                print("✅ 回测引擎正确返回空DataFrame")
                return True
            else:
                print("❌ 回测引擎未正确返回空DataFrame")
                return False
        else:
            print("❌ 回测引擎模拟数据功能未禁用")
            return False
            
    except Exception as e:
        print(f"❌ 测试回测引擎失败: {e}")
        return False

def test_log_messages():
    """测试日志消息更新"""
    print("\n🔍 测试日志消息更新...")
    
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有正确的警告日志
        warning_messages = [
            '模拟数据功能已禁用',
            '无真实数据源，模拟数据功能已禁用'
        ]
        
        found_warnings = []
        for warning in warning_messages:
            if warning in content:
                found_warnings.append(warning)
        
        if found_warnings:
            print(f"✅ 找到正确的警告日志: {len(found_warnings)}个")
            return True
        else:
            print("❌ 未找到正确的警告日志")
            return False
            
    except Exception as e:
        print(f"❌ 测试日志消息失败: {e}")
        return False

def test_ui_text_updates():
    """测试UI文本更新"""
    print("\n🔍 测试UI文本更新...")
    
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了模拟环境相关的文本
        old_texts = [
            '建议先在模拟环境中测试',
            '建议先在模拟环境中练习',
            '新手建议从模拟交易开始练习'
        ]
        
        found_old_texts = []
        for text in old_texts:
            if text in content:
                found_old_texts.append(text)
        
        if found_old_texts:
            print(f"❌ 仍有模拟环境相关文本: {found_old_texts}")
            return False
        
        # 检查是否有新的替代文本
        new_texts = [
            '建议先小仓位测试',
            '建议先学习相关技术分析知识',
            '新手建议先学习技术分析基础知识'
        ]
        
        found_new_texts = []
        for text in new_texts:
            if text in content:
                found_new_texts.append(text)
        
        if found_new_texts:
            print(f"✅ 找到新的替代文本: {len(found_new_texts)}个")
            return True
        else:
            print("❌ 未找到新的替代文本")
            return False
            
    except Exception as e:
        print(f"❌ 测试UI文本更新失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚫 WMZC量化交易系统 - 模拟数据功能禁用测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1：模拟数据代码移除
    result1 = test_mock_data_removal()
    test_results.append(("模拟数据代码移除", result1))
    
    # 测试2：无数据状态功能
    result2 = test_no_data_status()
    test_results.append(("无数据状态功能", result2))
    
    # 测试3：回测引擎禁用
    result3 = test_backtest_engine_disabled()
    test_results.append(("回测引擎模拟数据禁用", result3))
    
    # 测试4：日志消息更新
    result4 = test_log_messages()
    test_results.append(("日志消息更新", result4))
    
    # 测试5：UI文本更新
    result5 = test_ui_text_updates()
    test_results.append(("UI文本更新", result5))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！模拟数据功能已完全禁用")
        print("\n✅ 禁用效果:")
        print("• 移除了所有模拟K线数据生成代码")
        print("• 移除了所有模拟技术指标数据生成")
        print("• 回测引擎不再使用模拟数据")
        print("• 无数据时显示'无数据'状态而非模拟数据")
        print("• 更新了所有相关的日志消息")
        print("• 更新了UI中的提示文本")
        print("\n🔧 系统行为:")
        print("• 无法获取真实数据时，显示'❌ 无数据'状态")
        print("• 日志中不再出现'使用模拟数据'信息")
        print("• 增强指标面板显示'请连接交易所获取真实数据'")
        print("• 回测引擎返回空DataFrame而非模拟数据")
    else:
        print("❌ 部分测试失败，请检查相关实现")
    
    return all_passed

if __name__ == "__main__":
    main()
