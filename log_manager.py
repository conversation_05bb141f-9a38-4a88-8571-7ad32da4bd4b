#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - 日志管理模块
提供完整的日志记录、过滤、搜索和分析功能
"""

import os
import json
import logging
import logging.handlers
import threading
import time
import psutil
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import csv
import gzip

class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs", db_path: str = "logs/logs.db"):
        """
        初始化日志管理器
        
        Args:
            log_dir: 日志文件目录
            db_path: 日志数据库路径
        """
        self.log_dir = log_dir
        self.db_path = db_path
        
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 日志配置
        self.log_config = {
            'level': logging.INFO,
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'auto_cleanup_days': 30,
            'enable_performance_log': True,
            'enable_trading_log': True
        }
        
        # 内存日志缓存
        self.log_buffer = deque(maxlen=1000)
        self.buffer_lock = threading.Lock()
        
        # 性能监控
        self.performance_data = deque(maxlen=1440)  # 24小时数据
        self.performance_lock = threading.Lock()
        
        # 交易日志
        self.trading_logs = deque(maxlen=10000)
        self.trading_lock = threading.Lock()
        
        # 初始化数据库
        self._init_database()
        
        # 设置日志处理器
        self._setup_loggers()
        
        # 启动后台任务
        self.monitoring_active = True

        # 延迟初始化CPU监控，避免阻塞
        def delayed_cpu_init():
            try:
                import time
                time.sleep(1)  # 延迟1秒
                psutil.cpu_percent(interval=None)  # 非阻塞初始化调用
            except Exception:
                pass

        cpu_init_thread = threading.Thread(target=delayed_cpu_init, daemon=True)
        cpu_init_thread.start()

        # 延迟启动性能监控线程
        def delayed_start_performance_monitor():
            import time
            time.sleep(2)  # 延迟2秒启动
            self.performance_thread = threading.Thread(target=self._performance_monitor, daemon=True)
            self.performance_thread.start()

        perf_start_thread = threading.Thread(target=delayed_start_performance_monitor, daemon=True)
        perf_start_thread.start()

        # 延迟启动清理线程
        def delayed_start_cleanup():
            import time
            time.sleep(3)  # 延迟3秒启动
            self.cleanup_thread = threading.Thread(target=self._auto_cleanup, daemon=True)
            self.cleanup_thread.start()

        cleanup_start_thread = threading.Thread(target=delayed_start_cleanup, daemon=True)
        cleanup_start_thread.start()

        self.logger = logging.getLogger(__name__)
        self.logger.info("日志管理器初始化完成（后台任务延迟启动）")
    
    def _init_database(self):
        """初始化日志数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建系统日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        level TEXT NOT NULL,
                        module TEXT NOT NULL,
                        message TEXT NOT NULL,
                        extra_data TEXT
                    )
                ''')
                
                # 创建性能监控表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        cpu_percent REAL,
                        memory_percent REAL,
                        memory_used INTEGER,
                        disk_usage REAL,
                        network_sent INTEGER,
                        network_recv INTEGER
                    )
                ''')
                
                # 创建交易日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trading_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        strategy TEXT NOT NULL,
                        action TEXT NOT NULL,
                        symbol TEXT,
                        price REAL,
                        quantity REAL,
                        pnl REAL,
                        status TEXT,
                        extra_data TEXT
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_timestamp ON system_logs(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_level ON system_logs(level)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_logs(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_timestamp ON trading_logs(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_strategy ON trading_logs(strategy)')
                
                conn.commit()
                
        except Exception as e:
            print(f"初始化日志数据库失败: {e}")
    
    def _setup_loggers(self):
        """设置日志处理器"""
        try:
            # 获取根日志器
            root_logger = logging.getLogger()
            root_logger.setLevel(self.log_config['level'])
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                os.path.join(self.log_dir, 'wmzc.log'),
                maxBytes=self.log_config['max_file_size'],
                backupCount=self.log_config['backup_count'],
                encoding='utf-8'
            )
            
            # 错误文件处理器
            error_handler = logging.handlers.RotatingFileHandler(
                os.path.join(self.log_dir, 'error.log'),
                maxBytes=self.log_config['max_file_size'],
                backupCount=self.log_config['backup_count'],
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            
            # 内存处理器
            memory_handler = MemoryLogHandler(self.log_buffer, self.buffer_lock)
            
            # 数据库处理器
            db_handler = DatabaseLogHandler(self.db_path)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            error_handler.setFormatter(formatter)
            memory_handler.setFormatter(formatter)
            db_handler.setFormatter(formatter)
            
            # 添加处理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(error_handler)
            root_logger.addHandler(memory_handler)
            root_logger.addHandler(db_handler)
            
        except Exception as e:
            print(f"设置日志处理器失败: {e}")
    
    def _performance_monitor(self):
        """性能监控后台任务"""
        while self.monitoring_active:
            try:
                if self.log_config['enable_performance_log']:
                    # 获取系统性能数据 - 优化版本，避免阻塞
                    cpu_percent = psutil.cpu_percent(interval=None)  # 非阻塞获取
                    memory = psutil.virtual_memory()
                    disk = psutil.disk_usage('/')
                    network = psutil.net_io_counters()
                    
                    perf_data = {
                        'timestamp': datetime.now(),
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'memory_used': memory.used,
                        'disk_usage': disk.percent,
                        'network_sent': network.bytes_sent,
                        'network_recv': network.bytes_recv
                    }
                    
                    # 添加到内存缓存
                    with self.performance_lock:
                        self.performance_data.append(perf_data)
                    
                    # 保存到数据库
                    self._save_performance_data(perf_data)
                    
                    # 检查异常情况
                    if cpu_percent > 90:
                        logging.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
                    if memory.percent > 90:
                        logging.warning(f"内存使用率过高: {memory.percent:.1f}%")
                    if disk.percent > 90:
                        logging.warning(f"磁盘使用率过高: {disk.percent:.1f}%")
                
                time.sleep(60)  # 每分钟监控一次
                
            except Exception as e:
                logging.error(f"性能监控失败: {e}")
                time.sleep(60)
    
    def _save_performance_data(self, perf_data: Dict):
        """保存性能数据到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO performance_logs 
                    (timestamp, cpu_percent, memory_percent, memory_used, 
                     disk_usage, network_sent, network_recv)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    perf_data['timestamp'],
                    perf_data['cpu_percent'],
                    perf_data['memory_percent'],
                    perf_data['memory_used'],
                    perf_data['disk_usage'],
                    perf_data['network_sent'],
                    perf_data['network_recv']
                ))
                conn.commit()
                
        except Exception as e:
            logging.error(f"保存性能数据失败: {e}")
    
    def _auto_cleanup(self):
        """自动清理过期日志"""
        while self.monitoring_active:
            try:
                cleanup_date = datetime.now() - timedelta(days=self.log_config['auto_cleanup_days'])
                
                # 清理数据库中的过期记录
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('DELETE FROM system_logs WHERE timestamp < ?', (cleanup_date,))
                    cursor.execute('DELETE FROM performance_logs WHERE timestamp < ?', (cleanup_date,))
                    cursor.execute('DELETE FROM trading_logs WHERE timestamp < ?', (cleanup_date,))
                    
                    conn.commit()
                
                # 压缩旧的日志文件
                self._compress_old_logs()
                
                logging.info(f"自动清理完成，删除{cleanup_date}之前的日志")
                
                # 每天执行一次清理
                time.sleep(24 * 3600)
                
            except Exception as e:
                logging.error(f"自动清理失败: {e}")
                time.sleep(3600)  # 出错后1小时后重试
    
    def _compress_old_logs(self):
        """压缩旧的日志文件"""
        try:
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log') and not filename.endswith('.gz'):
                    filepath = os.path.join(self.log_dir, filename)
                    
                    # 检查文件修改时间
                    mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    if datetime.now() - mtime > timedelta(days=7):
                        # 压缩7天前的日志文件
                        with open(filepath, 'rb') as f_in:
                            with gzip.open(f"{filepath}.gz", 'wb') as f_out:
                                f_out.writelines(f_in)
                        
                        os.remove(filepath)
                        logging.info(f"日志文件已压缩: {filename}")
                        
        except Exception as e:
            logging.error(f"压缩日志文件失败: {e}")
    
    def log_trading_action(self, strategy: str, action: str, symbol: str = None, 
                          price: float = None, quantity: float = None, 
                          pnl: float = None, status: str = None, extra_data: Dict = None):
        """记录交易操作"""
        try:
            if not self.log_config['enable_trading_log']:
                return
            
            trading_log = {
                'timestamp': datetime.now(),
                'strategy': strategy,
                'action': action,
                'symbol': symbol,
                'price': price,
                'quantity': quantity,
                'pnl': pnl,
                'status': status,
                'extra_data': json.dumps(extra_data) if extra_data else None
            }
            
            # 添加到内存缓存
            with self.trading_lock:
                self.trading_logs.append(trading_log)
            
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO trading_logs 
                    (timestamp, strategy, action, symbol, price, quantity, pnl, status, extra_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    trading_log['timestamp'],
                    trading_log['strategy'],
                    trading_log['action'],
                    trading_log['symbol'],
                    trading_log['price'],
                    trading_log['quantity'],
                    trading_log['pnl'],
                    trading_log['status'],
                    trading_log['extra_data']
                ))
                conn.commit()
            
            # 记录到系统日志
            log_message = f"交易操作 - 策略:{strategy}, 动作:{action}"
            if symbol:
                log_message += f", 交易对:{symbol}"
            if price:
                log_message += f", 价格:{price}"
            if quantity:
                log_message += f", 数量:{quantity}"
            if pnl:
                log_message += f", 盈亏:{pnl}"
            
            logging.info(log_message)
            
        except Exception as e:
            logging.error(f"记录交易操作失败: {e}")
    
    def get_logs(self, level: str = None, module: str = None, 
                start_time: datetime = None, end_time: datetime = None,
                keyword: str = None, limit: int = 1000) -> List[Dict]:
        """
        获取日志记录
        
        Args:
            level: 日志级别过滤
            module: 模块名过滤
            start_time: 开始时间
            end_time: 结束时间
            keyword: 关键词搜索
            limit: 返回记录数限制
            
        Returns:
            日志记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT timestamp, level, module, message, extra_data FROM system_logs WHERE 1=1"
                params = []
                
                if level:
                    query += " AND level = ?"
                    params.append(level)
                
                if module:
                    query += " AND module LIKE ?"
                    params.append(f"%{module}%")
                
                if start_time:
                    query += " AND timestamp >= ?"
                    params.append(start_time)
                
                if end_time:
                    query += " AND timestamp <= ?"
                    params.append(end_time)
                
                if keyword:
                    query += " AND message LIKE ?"
                    params.append(f"%{keyword}%")
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                logs = []
                for row in rows:
                    logs.append({
                        'timestamp': row[0],
                        'level': row[1],
                        'module': row[2],
                        'message': row[3],
                        'extra_data': json.loads(row[4]) if row[4] else None
                    })
                
                return logs
                
        except Exception as e:
            logging.error(f"获取日志记录失败: {e}")
            return []
    
    def get_performance_data(self, hours: int = 24) -> List[Dict]:
        """获取性能监控数据"""
        try:
            start_time = datetime.now() - timedelta(hours=hours)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT timestamp, cpu_percent, memory_percent, memory_used,
                           disk_usage, network_sent, network_recv
                    FROM performance_logs 
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (start_time,))
                
                rows = cursor.fetchall()
                
                performance_data = []
                for row in rows:
                    performance_data.append({
                        'timestamp': row[0],
                        'cpu_percent': row[1],
                        'memory_percent': row[2],
                        'memory_used': row[3],
                        'disk_usage': row[4],
                        'network_sent': row[5],
                        'network_recv': row[6]
                    })
                
                return performance_data
                
        except Exception as e:
            logging.error(f"获取性能数据失败: {e}")
            return []
    
    def get_trading_logs(self, strategy: str = None, start_time: datetime = None,
                        end_time: datetime = None, limit: int = 1000) -> List[Dict]:
        """获取交易日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT timestamp, strategy, action, symbol, price, quantity, 
                           pnl, status, extra_data
                    FROM trading_logs WHERE 1=1
                '''
                params = []
                
                if strategy:
                    query += " AND strategy = ?"
                    params.append(strategy)
                
                if start_time:
                    query += " AND timestamp >= ?"
                    params.append(start_time)
                
                if end_time:
                    query += " AND timestamp <= ?"
                    params.append(end_time)
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                trading_logs = []
                for row in rows:
                    trading_logs.append({
                        'timestamp': row[0],
                        'strategy': row[1],
                        'action': row[2],
                        'symbol': row[3],
                        'price': row[4],
                        'quantity': row[5],
                        'pnl': row[6],
                        'status': row[7],
                        'extra_data': json.loads(row[8]) if row[8] else None
                    })
                
                return trading_logs
                
        except Exception as e:
            logging.error(f"获取交易日志失败: {e}")
            return []

    def export_logs(self, format_type: str, filepath: str, **filters) -> bool:
        """
        导出日志

        Args:
            format_type: 导出格式 ('txt', 'csv', 'json')
            filepath: 导出文件路径
            **filters: 过滤条件

        Returns:
            导出是否成功
        """
        try:
            logs = self.get_logs(**filters)

            if format_type == 'txt':
                with open(filepath, 'w', encoding='utf-8') as f:
                    for log in logs:
                        f.write(f"[{log['timestamp']}] {log['level']} - {log['module']}: {log['message']}\n")

            elif format_type == 'csv':
                with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)
                    writer.writerow(['时间', '级别', '模块', '消息'])
                    for log in logs:
                        writer.writerow([log['timestamp'], log['level'], log['module'], log['message']])

            elif format_type == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(logs, f, indent=2, ensure_ascii=False, default=str)

            else:
                raise ValueError(f"不支持的导出格式: {format_type}")

            logging.info(f"日志导出成功: {filepath}")
            return True

        except Exception as e:
            logging.error(f"导出日志失败: {e}")
            return False

    def get_log_statistics(self, hours: int = 24) -> Dict:
        """获取日志统计信息"""
        try:
            start_time = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 按级别统计
                cursor.execute('''
                    SELECT level, COUNT(*) as count
                    FROM system_logs
                    WHERE timestamp >= ?
                    GROUP BY level
                ''', (start_time,))
                level_stats = dict(cursor.fetchall())

                # 按模块统计
                cursor.execute('''
                    SELECT module, COUNT(*) as count
                    FROM system_logs
                    WHERE timestamp >= ?
                    GROUP BY module
                    ORDER BY count DESC
                    LIMIT 10
                ''', (start_time,))
                module_stats = dict(cursor.fetchall())

                # 按小时统计
                cursor.execute('''
                    SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
                    FROM system_logs
                    WHERE timestamp >= ?
                    GROUP BY hour
                    ORDER BY hour
                ''', (start_time,))
                hourly_stats = dict(cursor.fetchall())

                # 错误趋势
                cursor.execute('''
                    SELECT DATE(timestamp) as date, COUNT(*) as count
                    FROM system_logs
                    WHERE level = 'ERROR' AND timestamp >= ?
                    GROUP BY date
                    ORDER BY date
                ''', (start_time,))
                error_trend = dict(cursor.fetchall())

                return {
                    'level_distribution': level_stats,
                    'top_modules': module_stats,
                    'hourly_distribution': hourly_stats,
                    'error_trend': error_trend,
                    'total_logs': sum(level_stats.values()),
                    'error_rate': level_stats.get('ERROR', 0) / max(sum(level_stats.values()), 1)
                }

        except Exception as e:
            logging.error(f"获取日志统计失败: {e}")
            return {}

    def get_trading_statistics(self, days: int = 7) -> Dict:
        """获取交易统计信息"""
        try:
            start_time = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 按策略统计
                cursor.execute('''
                    SELECT strategy, COUNT(*) as trades,
                           SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as wins,
                           SUM(pnl) as total_pnl
                    FROM trading_logs
                    WHERE timestamp >= ? AND pnl IS NOT NULL
                    GROUP BY strategy
                ''', (start_time,))

                strategy_stats = {}
                for row in cursor.fetchall():
                    strategy, trades, wins, total_pnl = row
                    win_rate = wins / trades if trades > 0 else 0
                    strategy_stats[strategy] = {
                        'trades': trades,
                        'wins': wins,
                        'win_rate': win_rate,
                        'total_pnl': total_pnl or 0
                    }

                # 按日期统计
                cursor.execute('''
                    SELECT DATE(timestamp) as date,
                           COUNT(*) as trades,
                           SUM(pnl) as daily_pnl
                    FROM trading_logs
                    WHERE timestamp >= ? AND pnl IS NOT NULL
                    GROUP BY date
                    ORDER BY date
                ''', (start_time,))
                daily_stats = {}
                for row in cursor.fetchall():
                    date, trades, daily_pnl = row
                    daily_stats[date] = {
                        'trades': trades,
                        'pnl': daily_pnl or 0
                    }

                # 总体统计
                cursor.execute('''
                    SELECT COUNT(*) as total_trades,
                           SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as total_wins,
                           SUM(pnl) as total_pnl,
                           AVG(pnl) as avg_pnl
                    FROM trading_logs
                    WHERE timestamp >= ? AND pnl IS NOT NULL
                ''', (start_time,))

                row = cursor.fetchone()
                total_trades, total_wins, total_pnl, avg_pnl = row
                overall_win_rate = total_wins / total_trades if total_trades > 0 else 0

                return {
                    'strategy_performance': strategy_stats,
                    'daily_performance': daily_stats,
                    'overall_stats': {
                        'total_trades': total_trades or 0,
                        'total_wins': total_wins or 0,
                        'win_rate': overall_win_rate,
                        'total_pnl': total_pnl or 0,
                        'avg_pnl': avg_pnl or 0
                    }
                }

        except Exception as e:
            logging.error(f"获取交易统计失败: {e}")
            return {}

    def update_config(self, config: Dict):
        """更新日志配置"""
        try:
            self.log_config.update(config)

            # 重新设置日志级别
            if 'level' in config:
                logging.getLogger().setLevel(config['level'])

            logging.info(f"日志配置已更新: {config}")

        except Exception as e:
            logging.error(f"更新日志配置失败: {e}")

    def get_config(self) -> Dict:
        """获取当前日志配置"""
        return self.log_config.copy()

    def clear_logs(self, log_type: str = 'all', days: int = None):
        """
        清理日志

        Args:
            log_type: 日志类型 ('all', 'system', 'performance', 'trading')
            days: 保留最近几天的日志，None表示全部清理
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if days is not None:
                    cutoff_date = datetime.now() - timedelta(days=days)
                    condition = "WHERE timestamp < ?"
                    params = (cutoff_date,)
                else:
                    condition = ""
                    params = ()

                if log_type in ['all', 'system']:
                    cursor.execute(f"DELETE FROM system_logs {condition}", params)

                if log_type in ['all', 'performance']:
                    cursor.execute(f"DELETE FROM performance_logs {condition}", params)

                if log_type in ['all', 'trading']:
                    cursor.execute(f"DELETE FROM trading_logs {condition}", params)

                conn.commit()

                # 清理内存缓存
                if log_type in ['all', 'system']:
                    with self.buffer_lock:
                        self.log_buffer.clear()

                if log_type in ['all', 'performance']:
                    with self.performance_lock:
                        self.performance_data.clear()

                if log_type in ['all', 'trading']:
                    with self.trading_lock:
                        self.trading_logs.clear()

                logging.info(f"日志清理完成: {log_type}, 保留天数: {days}")

        except Exception as e:
            logging.error(f"清理日志失败: {e}")

    def get_memory_logs(self, limit: int = 100) -> List[Dict]:
        """获取内存中的最新日志"""
        with self.buffer_lock:
            return list(self.log_buffer)[-limit:]

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        logging.info("日志监控已停止")

    def __del__(self):
        """析构函数"""
        self.stop_monitoring()


class MemoryLogHandler(logging.Handler):
    """内存日志处理器"""

    def __init__(self, log_buffer: deque, buffer_lock: threading.Lock):
        super().__init__()
        self.log_buffer = log_buffer
        self.buffer_lock = buffer_lock

    def emit(self, record):
        try:
            log_entry = {
                'timestamp': datetime.fromtimestamp(record.created),
                'level': record.levelname,
                'module': record.name,
                'message': self.format(record),
                'lineno': record.lineno,
                'funcName': record.funcName
            }

            with self.buffer_lock:
                self.log_buffer.append(log_entry)

        except Exception:
            self.handleError(record)


class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""

    def __init__(self, db_path: str):
        super().__init__()
        self.db_path = db_path

    def emit(self, record):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO system_logs (timestamp, level, module, message)
                    VALUES (?, ?, ?, ?)
                ''', (
                    datetime.fromtimestamp(record.created),
                    record.levelname,
                    record.name,
                    self.format(record)
                ))
                conn.commit()

        except Exception:
            self.handleError(record)
