#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate WebSocket管理器 - 实时数据流处理
支持现货、合约等多种产品的实时数据推送
"""

import json
import os
import asyncio
import websockets
import hmac
import hashlib
import time
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
from enum import Enum

class GateWSChannel(Enum):
    """Gate WebSocket频道"""
    # 现货频道
    SPOT_TICKERS = "spot.tickers"
    SPOT_TRADES = "spot.trades"
    SPOT_ORDER_BOOK = "spot.order_book"
    SPOT_ORDER_BOOK_UPDATE = "spot.order_book_update"
    SPOT_CANDLESTICKS = "spot.candlesticks"
    
    # 现货私有频道
    SPOT_ORDERS = "spot.orders"
    SPOT_USER_TRADES = "spot.usertrades"
    SPOT_BALANCES = "spot.balances"
    
    # 合约频道
    FUTURES_TICKERS = "futures.tickers"
    FUTURES_TRADES = "futures.trades"
    FUTURES_ORDER_BOOK = "futures.order_book"
    FUTURES_ORDER_BOOK_UPDATE = "futures.order_book_update"
    FUTURES_CANDLESTICKS = "futures.candlesticks"
    
    # 合约私有频道
    FUTURES_ORDERS = "futures.orders"
    FUTURES_USER_TRADES = "futures.usertrades"
    FUTURES_POSITIONS = "futures.positions"
    FUTURES_BALANCES = "futures.balances"

@dataclass
class GateWSConfig:
    """Gate WebSocket配置"""
    api_key: str
    secret_key: str
    is_sandbox: bool = False
    spot_url: str = "wss://api.gateio.ws/ws/v4/"
    futures_url: str = "wss://fx-ws.gateio.ws/v4/ws/usdt"
    testnet_spot_url: str = "wss://api.gateio.ws/ws/v4/"
    testnet_futures_url: str = "wss://fx-ws-testnet.gateio.ws/v4/ws/usdt"

class GateWebSocketManager:
    """Gate WebSocket管理器"""
    
    def __init__(self, config: GateWSConfig):
        self.config = config
        self.spot_ws = None
        self.futures_ws = None
        
        # 回调函数
        self.message_handlers: Dict[str, List[Callable]] = {}
        self.error_handlers: List[Callable] = []
        
        # 连接状态
        self.is_connected = False
        self.reconnect_interval = 5
        self.max_reconnect_attempts = 10
        
        # 订阅管理
        self.subscriptions: Dict[str, List[Dict]] = {
            'spot': [],
            'futures': []
        }
    
    def add_message_handler(self, channel: str, handler: Callable):
        """添加消息处理器"""
        if channel not in self.message_handlers:
            self.message_handlers[channel] = []
        self.message_handlers[channel].append(handler)
    
    def add_error_handler(self, handler: Callable):
        """添加错误处理器"""
        self.error_handlers.append(handler)
    
    def _generate_signature(self, channel: str, event: str, timestamp: int) -> str:
        """生成Gate API签名"""
        message = f'channel={channel}&event={event}&time={timestamp}'
        signature = hmac.new(
            self.config.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()
        return signature
    
    def _get_auth_message(self, channel: str, event: str, payload: List = None) -> Dict:
        """获取认证消息"""
        timestamp = int(time.time())
        signature = self._generate_signature(channel, event, timestamp)
        
        message = {
            "time": timestamp,
            "channel": channel,
            "event": event,
            "payload": payload or [],
            "auth": {
                "method": "api_key",
                "KEY": self.config.api_key,
                "SIGN": signature
            }
        }
        
        return message
    
    def _get_public_message(self, channel: str, event: str, payload: List = None) -> Dict:
        """获取公共消息"""
        return {
            "time": int(time.time()),
            "channel": channel,
            "event": event,
            "payload": payload or []
        }
    
    async def _handle_message(self, message: str, ws_type: str):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            
            # 处理不同类型的消息
            if 'error' in data and data['error']:
                # 错误消息
                await self._handle_error_message(data, ws_type)
            elif 'event' in data:
                # 事件消息
                await self._handle_event_message(data, ws_type)
            elif 'channel' in data and 'result' in data:
                # 数据推送消息
                await self._handle_data_message(data, ws_type)
            else:
                print(f"Unknown message format: {data}")
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse message: {e}")
        except Exception as e:
            print(f"Error handling message: {e}")
            for handler in self.error_handlers:
                try:
                    await handler(e, message)
                except:
                    pass
    
    async def _handle_error_message(self, data: Dict, ws_type: str):
        """处理错误消息"""
        error = data.get('error')
        print(f"Gate WebSocket Error: {error}")
        
        for handler in self.error_handlers:
            try:
                await handler(Exception(str(error)), data)
            except:
                pass
    
    async def _handle_event_message(self, data: Dict, ws_type: str):
        """处理事件消息"""
        event = data.get('event')
        channel = data.get('channel')
        
        if event == 'subscribe':
            print(f"Subscription confirmed: {channel}")
        elif event == 'unsubscribe':
            print(f"Unsubscription confirmed: {channel}")
        elif event == 'update':
            # 这是数据更新，转发到数据处理
            await self._handle_data_message(data, ws_type)
    
    async def _handle_data_message(self, data: Dict, ws_type: str):
        """处理数据推送消息"""
        channel = data.get('channel')
        
        if channel in self.message_handlers:
            for handler in self.message_handlers[channel]:
                try:
                    await handler(data)
                except Exception as e:
                    print(f"Error in message handler: {e}")
    
    async def _websocket_handler(self, websocket, ws_type: str):
        """WebSocket消息处理循环"""
        try:
            async for message in websocket:
                await self._handle_message(message, ws_type)
        except websockets.exceptions.ConnectionClosed:
            print(f"{ws_type} WebSocket connection closed")
        except Exception as e:
            print(f"Error in {ws_type} WebSocket handler: {e}")
    
    async def connect_spot(self):
        """连接现货WebSocket"""
        try:
            url = self.config.testnet_spot_url if self.config.is_sandbox else self.config.spot_url
            self.spot_ws = await websockets.connect(url)
            print("Spot WebSocket connected")
            
            # 重新订阅
            for sub in self.subscriptions['spot']:
                await self.spot_ws.send(json.dumps(sub))
            
            # 启动消息处理
            asyncio.create_task(self._websocket_handler(self.spot_ws, 'spot'))
            
        except Exception as e:
            print(f"Failed to connect spot WebSocket: {e}")
            raise
    
    async def connect_futures(self):
        """连接合约WebSocket"""
        try:
            url = self.config.testnet_futures_url if self.config.is_sandbox else self.config.futures_url
            self.futures_ws = await websockets.connect(url)
            print("Futures WebSocket connected")
            
            # 重新订阅
            for sub in self.subscriptions['futures']:
                await self.futures_ws.send(json.dumps(sub))
            
            # 启动消息处理
            asyncio.create_task(self._websocket_handler(self.futures_ws, 'futures'))
            
        except Exception as e:
            print(f"Failed to connect futures WebSocket: {e}")
            raise
    
    async def connect_all(self):
        """连接所有WebSocket"""
        await self.connect_spot()
        await self.connect_futures()
        self.is_connected = True
    
    # ==================== 现货订阅方法 ====================
    
    async def subscribe_spot_ticker(self, currency_pair: str = None):
        """订阅现货行情"""
        payload = [currency_pair] if currency_pair else []
        message = self._get_public_message("spot.tickers", "subscribe", payload)
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_trades(self, currency_pair: str):
        """订阅现货成交数据"""
        message = self._get_public_message("spot.trades", "subscribe", [currency_pair])
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_orderbook(self, currency_pair: str, limit: str = "20", interval: str = "1000ms"):
        """订阅现货订单簿"""
        message = self._get_public_message("spot.order_book_update", "subscribe", [currency_pair, interval, limit])
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_candlesticks(self, currency_pair: str, interval: str = "1m"):
        """订阅现货K线数据"""
        message = self._get_public_message("spot.candlesticks", "subscribe", [interval, currency_pair])
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_orders(self, currency_pair: str = "!all"):
        """订阅现货订单数据"""
        message = self._get_auth_message("spot.orders", "subscribe", [currency_pair])
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_user_trades(self, currency_pair: str = "!all"):
        """订阅现货用户成交数据"""
        message = self._get_auth_message("spot.usertrades", "subscribe", [currency_pair])
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    async def subscribe_spot_balances(self):
        """订阅现货余额数据"""
        message = self._get_auth_message("spot.balances", "subscribe")
        
        self.subscriptions['spot'].append(message)
        
        if self.spot_ws:
            await self.spot_ws.send(json.dumps(message))
    
    # ==================== 合约订阅方法 ====================
    
    async def subscribe_futures_ticker(self, contract: str = None):
        """订阅合约行情"""
        payload = [contract] if contract else []
        message = self._get_public_message("futures.tickers", "subscribe", payload)
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_trades(self, contract: str):
        """订阅合约成交数据"""
        message = self._get_public_message("futures.trades", "subscribe", [contract])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_orderbook(self, contract: str, interval: str = "1000ms", limit: str = "20"):
        """订阅合约订单簿"""
        message = self._get_public_message("futures.order_book_update", "subscribe", [contract, interval, limit])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_candlesticks(self, contract: str, interval: str = "1m"):
        """订阅合约K线数据"""
        message = self._get_public_message("futures.candlesticks", "subscribe", [interval, contract])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_orders(self, user_id: str, contract: str = "!all"):
        """订阅合约订单数据"""
        message = self._get_auth_message("futures.orders", "subscribe", [user_id, contract])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_positions(self, user_id: str, contract: str = "!all"):
        """订阅合约持仓数据"""
        message = self._get_auth_message("futures.positions", "subscribe", [user_id, contract])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def subscribe_futures_balances(self, user_id: str):
        """订阅合约余额数据"""
        message = self._get_auth_message("futures.balances", "subscribe", [user_id])
        
        self.subscriptions['futures'].append(message)
        
        if self.futures_ws:
            await self.futures_ws.send(json.dumps(message))
    
    async def unsubscribe(self, channel: str, payload: List = None, ws_type: str = "spot"):
        """取消订阅"""
        if ws_type == "spot":
            message = self._get_public_message(channel, "unsubscribe", payload)
            websocket = self.spot_ws
        else:
            message = self._get_public_message(channel, "unsubscribe", payload)
            websocket = self.futures_ws
        
        # 从订阅列表中移除
        self.subscriptions[ws_type] = [
            sub for sub in self.subscriptions[ws_type]
            if not (sub.get('channel') == channel and sub.get('payload') == payload)
        ]
        
        if websocket:
            await websocket.send(json.dumps(message))
    
    async def close(self):
        """关闭所有连接"""
        self.is_connected = False
        
        if self.spot_ws:
            await self.spot_ws.close()
        if self.futures_ws:
            await self.futures_ws.close()

# 使用示例
async def example_usage():
    """使用示例"""
    config = GateWSConfig(
        api_key=os.getenv("GATE_API_KEY", ""),
        secret_key=os.getenv("GATE_SECRET_KEY", ""),
        is_sandbox=True
    )
    
    ws_manager = GateWebSocketManager(config)
    
    # 添加消息处理器
    async def handle_spot_ticker(data):
        print(f"Spot ticker data: {data}")
    
    async def handle_futures_ticker(data):
        print(f"Futures ticker data: {data}")
    
    async def handle_error(error, data):
        print(f"Error: {error}, Data: {data}")
    
    ws_manager.add_message_handler("spot.tickers", handle_spot_ticker)
    ws_manager.add_message_handler("futures.tickers", handle_futures_ticker)
    ws_manager.add_error_handler(handle_error)
    
    try:
        # 连接WebSocket
        await ws_manager.connect_all()
        
        # 订阅数据
        await ws_manager.subscribe_spot_ticker("BTC_USDT")
        await ws_manager.subscribe_futures_ticker("BTC_USDT")
        await ws_manager.subscribe_spot_orders()
        await ws_manager.subscribe_futures_positions("your_user_id")
        
        # 保持连接
        await asyncio.sleep(60)
        
    finally:
        await ws_manager.close()

if __name__ == "__main__":
    asyncio.run(example_usage())
