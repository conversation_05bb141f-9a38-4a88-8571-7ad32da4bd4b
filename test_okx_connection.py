#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OKX连接和K线数据获取
"""

import asyncio
import aiohttp
import ssl
import certifi
from datetime import datetime

async def test_okx_public_api():
    """测试OKX公开API连接"""
    print("🔍 测试OKX公开API连接...")
    
    try:
        # 创建SSL上下文
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        
        # 创建HTTP会话
        timeout = aiohttp.ClientTimeout(total=30)
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        
        async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
            # 测试1: 服务器时间
            print("   测试1: 获取服务器时间...")
            async with session.get("https://www.okx.com/api/v5/public/time") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 服务器时间获取成功: {data}")
                else:
                    print(f"   ❌ 服务器时间获取失败: {response.status}")
                    return False
            
            # 测试2: 获取交易对信息
            print("   测试2: 获取BTC-USDT交易对信息...")
            params = {
                'instType': 'SPOT',
                'instId': 'BTC-USDT'
            }
            async with session.get("https://www.okx.com/api/v5/public/instruments", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == '0' and data.get('data'):
                        print(f"   ✅ 交易对信息获取成功: {len(data['data'])}个交易对")
                    else:
                        print(f"   ❌ 交易对信息获取失败: {data}")
                        return False
                else:
                    print(f"   ❌ 交易对信息获取失败: {response.status}")
                    return False
            
            # 测试3: 获取K线数据
            print("   测试3: 获取BTC-USDT K线数据...")
            params = {
                'instId': 'BTC-USDT',
                'bar': '1m',
                'limit': '10'
            }
            async with session.get("https://www.okx.com/api/v5/market/candles", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == '0' and data.get('data'):
                        klines = data['data']
                        print(f"   ✅ K线数据获取成功: {len(klines)}条")
                        if klines:
                            latest = klines[0]
                            print(f"   最新K线: 时间={latest[0]}, 开盘={latest[1]}, 最高={latest[2]}, 最低={latest[3]}, 收盘={latest[4]}, 成交量={latest[5]}")
                    else:
                        print(f"   ❌ K线数据获取失败: {data}")
                        return False
                else:
                    print(f"   ❌ K线数据获取失败: {response.status}")
                    return False
            
            # 测试4: 获取行情数据
            print("   测试4: 获取BTC-USDT行情数据...")
            params = {
                'instId': 'BTC-USDT'
            }
            async with session.get("https://www.okx.com/api/v5/market/ticker", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == '0' and data.get('data'):
                        ticker = data['data'][0]
                        print(f"   ✅ 行情数据获取成功: 价格={ticker.get('last')}, 24h涨跌幅={ticker.get('sodUtc8')}")
                    else:
                        print(f"   ❌ 行情数据获取失败: {data}")
                        return False
                else:
                    print(f"   ❌ 行情数据获取失败: {response.status}")
                    return False
            
            print("   ✅ 所有OKX公开API测试通过！")
            return True
            
    except Exception as e:
        print(f"   ❌ OKX API测试异常: {e}")
        return False

async def test_network_connectivity():
    """测试网络连接"""
    print("\n🔍 测试网络连接...")
    
    try:
        # 测试基本网络连接
        async with aiohttp.ClientSession() as session:
            # 测试1: 百度
            try:
                async with session.get("https://www.baidu.com", timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        print("   ✅ 百度连接正常")
                    else:
                        print(f"   ⚠️ 百度连接异常: {response.status}")
            except Exception as e:
                print(f"   ❌ 百度连接失败: {e}")
            
            # 测试2: OKX主站
            try:
                async with session.get("https://www.okx.com", timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        print("   ✅ OKX主站连接正常")
                    else:
                        print(f"   ⚠️ OKX主站连接异常: {response.status}")
            except Exception as e:
                print(f"   ❌ OKX主站连接失败: {e}")
            
            # 测试3: DNS解析
            try:
                import socket
                ip = socket.gethostbyname('www.okx.com')
                print(f"   ✅ OKX DNS解析成功: {ip}")
            except Exception as e:
                print(f"   ❌ OKX DNS解析失败: {e}")
                
    except Exception as e:
        print(f"   ❌ 网络连接测试异常: {e}")

def test_ssl_certificates():
    """测试SSL证书"""
    print("\n🔍 测试SSL证书...")
    
    try:
        import ssl
        import socket
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        # 测试OKX SSL证书
        with socket.create_connection(('www.okx.com', 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname='www.okx.com') as ssock:
                cert = ssock.getpeercert()
                print(f"   ✅ OKX SSL证书验证成功")
                print(f"   证书主题: {cert.get('subject')}")
                print(f"   证书颁发者: {cert.get('issuer')}")
                print(f"   证书有效期: {cert.get('notAfter')}")
                
    except Exception as e:
        print(f"   ❌ SSL证书测试失败: {e}")

async def test_kline_data_parsing():
    """测试K线数据解析"""
    print("\n🔍 测试K线数据解析...")
    
    try:
        # 模拟OKX K线数据格式
        mock_okx_data = {
            "code": "0",
            "msg": "",
            "data": [
                ["1640995200000", "46000.1", "46100.5", "45900.0", "46050.2", "123.45", "5678901.23", "5678901.23", "1"],
                ["1640995140000", "45950.3", "46020.8", "45920.1", "46000.1", "98.76", "4567890.12", "4567890.12", "1"]
            ]
        }
        
        # 解析数据
        if mock_okx_data.get('code') == '0' and mock_okx_data.get('data'):
            klines = mock_okx_data['data']
            print(f"   ✅ 成功解析{len(klines)}条K线数据")
            
            for i, kline in enumerate(klines):
                timestamp = int(kline[0])
                dt = datetime.fromtimestamp(timestamp / 1000)
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                volume = float(kline[5])
                
                print(f"   K线{i+1}: {dt.strftime('%Y-%m-%d %H:%M:%S')}, O={open_price}, H={high_price}, L={low_price}, C={close_price}, V={volume}")
            
            return True
        else:
            print(f"   ❌ K线数据格式错误: {mock_okx_data}")
            return False
            
    except Exception as e:
        print(f"   ❌ K线数据解析异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 OKX连接和数据获取测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 网络连接
    await test_network_connectivity()
    
    # 测试2: SSL证书
    test_ssl_certificates()
    
    # 测试3: OKX公开API
    result1 = await test_okx_public_api()
    test_results.append(("OKX公开API", result1))
    
    # 测试4: K线数据解析
    result2 = await test_kline_data_parsing()
    test_results.append(("K线数据解析", result2))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！OKX连接正常")
        print("\n✅ 测试结果:")
        print("• OKX公开API连接正常")
        print("• K线数据获取正常")
        print("• 数据解析功能正常")
        print("\n🔧 建议:")
        print("• 可以正常使用OKX交易所功能")
        print("• 系统应该能够获取真实K线数据")
    else:
        print("❌ 部分测试失败，请检查网络连接和API配置")
        print("\n🔧 故障排除建议:")
        print("• 检查网络连接是否正常")
        print("• 检查防火墙设置")
        print("• 检查DNS解析")
        print("• 检查SSL证书配置")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
