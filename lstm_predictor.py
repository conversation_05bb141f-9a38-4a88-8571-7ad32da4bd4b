#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - LSTM价格预测器
基于深度学习的加密货币价格预测模型
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Bidirectional, Attention, Input
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("警告: TensorFlow未安装，将使用简化的预测模型")
    # 创建占位符类以避免NameError
    class Model:
        pass
    class Sequential:
        pass

try:
    from sklearn.preprocessing import MinMaxScaler, StandardScaler
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: scikit-learn未安装，将使用基础功能")
    # 创建简化的MinMaxScaler
    class MinMaxScaler:
        def __init__(self, feature_range=(0, 1)):
            self.feature_range = feature_range
            self.data_min_ = None
            self.data_max_ = None

        def fit_transform(self, X):
            self.data_min_ = np.min(X, axis=0)
            self.data_max_ = np.max(X, axis=0)
            return self.transform(X)

        def transform(self, X):
            X_scaled = (X - self.data_min_) / (self.data_max_ - self.data_min_)
            return X_scaled * (self.feature_range[1] - self.feature_range[0]) + self.feature_range[0]

    def mean_squared_error(y_true, y_pred):
        return np.mean((y_true - y_pred) ** 2)

    def mean_absolute_error(y_true, y_pred):
        return np.mean(np.abs(y_true - y_pred))

class LSTMPredictor:
    """LSTM价格预测器"""
    
    def __init__(self, 
                 sequence_length: int = 60,
                 prediction_steps: int = 1,
                 lstm_units: List[int] = [50, 50],
                 dropout_rate: float = 0.2,
                 learning_rate: float = 0.001,
                 batch_size: int = 32,
                 epochs: int = 100,
                 use_bidirectional: bool = True,
                 use_attention: bool = False):
        """
        初始化LSTM预测器
        
        Args:
            sequence_length: 输入序列长度
            prediction_steps: 预测步数
            lstm_units: LSTM层单元数列表
            dropout_rate: Dropout比率
            learning_rate: 学习率
            batch_size: 批次大小
            epochs: 训练轮数
            use_bidirectional: 是否使用双向LSTM
            use_attention: 是否使用注意力机制
        """
        self.sequence_length = sequence_length
        self.prediction_steps = prediction_steps
        self.lstm_units = lstm_units
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs
        self.use_bidirectional = use_bidirectional
        self.use_attention = use_attention
        
        self.logger = logging.getLogger(__name__)
        
        # 模型和数据处理器
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.feature_scalers = {}
        
        # 训练历史和性能指标
        self.training_history = None
        self.performance_metrics = {}
        self.feature_importance = {}
        
        # 预测结果缓存
        self.prediction_cache = {}
        self.last_prediction_time = None
        
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow不可用，将使用简化预测模型")
    
    def create_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        创建技术指标特征
        
        Args:
            data: 原始OHLCV数据
            
        Returns:
            包含技术指标的特征数据
        """
        try:
            features = data.copy()
            
            # 价格特征
            features['price_change'] = features['close'].pct_change()
            features['high_low_ratio'] = features['high'] / features['low']
            features['volume_change'] = features['volume'].pct_change()
            
            # RSI指标
            features['rsi'] = self.calculate_rsi(features['close'], period=14)
            features['rsi_6'] = self.calculate_rsi(features['close'], period=6)
            features['rsi_24'] = self.calculate_rsi(features['close'], period=24)
            
            # MACD指标
            macd_data = self.calculate_macd(features['close'])
            features['macd'] = macd_data['macd']
            features['macd_signal'] = macd_data['signal']
            features['macd_histogram'] = macd_data['histogram']
            
            # 布林带指标
            bollinger_data = self.calculate_bollinger_bands(features['close'])
            features['bb_upper'] = bollinger_data['upper']
            features['bb_middle'] = bollinger_data['middle']
            features['bb_lower'] = bollinger_data['lower']
            features['bb_width'] = (bollinger_data['upper'] - bollinger_data['lower']) / bollinger_data['middle']
            features['bb_position'] = (features['close'] - bollinger_data['lower']) / (bollinger_data['upper'] - bollinger_data['lower'])
            
            # 移动平均线
            features['sma_5'] = features['close'].rolling(window=5).mean()
            features['sma_10'] = features['close'].rolling(window=10).mean()
            features['sma_20'] = features['close'].rolling(window=20).mean()
            features['sma_50'] = features['close'].rolling(window=50).mean()
            
            # EMA指标
            features['ema_12'] = features['close'].ewm(span=12).mean()
            features['ema_26'] = features['close'].ewm(span=26).mean()
            
            # 成交量指标
            features['volume_sma'] = features['volume'].rolling(window=20).mean()
            features['volume_ratio'] = features['volume'] / features['volume_sma']
            
            # 波动率指标
            features['volatility'] = features['close'].rolling(window=20).std()
            features['atr'] = self.calculate_atr(features)
            
            # 动量指标
            features['momentum'] = features['close'] / features['close'].shift(10) - 1
            features['roc'] = features['close'].pct_change(periods=10)
            
            # 时间特征
            features['hour'] = pd.to_datetime(features.index).hour
            features['day_of_week'] = pd.to_datetime(features.index).dayofweek
            features['month'] = pd.to_datetime(features.index).month
            
            # 删除无限值和NaN
            features = features.replace([np.inf, -np.inf], np.nan)
            features = features.fillna(method='ffill').fillna(method='bfill')
            
            return features
            
        except Exception as e:
            self.logger.error(f"创建特征失败: {e}")
            return data
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        
        return {
            'macd': macd,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict:
        """计算布林带指标"""
        middle = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }
    
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR指标"""
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = np.max(ranges, axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def prepare_data(self, features: pd.DataFrame, target_column: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            features: 特征数据
            target_column: 目标列名
            
        Returns:
            X, y训练数据
        """
        try:
            # 选择特征列
            feature_columns = [col for col in features.columns if col not in ['open', 'high', 'low', 'close', 'volume']]
            feature_data = features[feature_columns].values
            target_data = features[target_column].values
            
            # 数据归一化
            scaled_features = self.scaler.fit_transform(feature_data)
            
            # 创建序列数据
            X, y = [], []
            for i in range(self.sequence_length, len(scaled_features) - self.prediction_steps + 1):
                X.append(scaled_features[i-self.sequence_length:i])
                
                if self.prediction_steps == 1:
                    y.append(target_data[i])
                else:
                    y.append(target_data[i:i+self.prediction_steps])
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            self.logger.error(f"准备数据失败: {e}")
            return np.array([]), np.array([])
    
    def build_model(self, input_shape: Tuple[int, int]) -> Optional[Model]:
        """
        构建LSTM模型
        
        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            
        Returns:
            构建的模型
        """
        try:
            if not TENSORFLOW_AVAILABLE:
                return None
            
            model = Sequential()
            
            # 第一层LSTM
            if self.use_bidirectional:
                model.add(Bidirectional(LSTM(self.lstm_units[0], 
                                           return_sequences=len(self.lstm_units) > 1,
                                           input_shape=input_shape)))
            else:
                model.add(LSTM(self.lstm_units[0], 
                              return_sequences=len(self.lstm_units) > 1,
                              input_shape=input_shape))
            
            model.add(Dropout(self.dropout_rate))
            
            # 额外的LSTM层
            for i in range(1, len(self.lstm_units)):
                return_sequences = i < len(self.lstm_units) - 1
                
                if self.use_bidirectional:
                    model.add(Bidirectional(LSTM(self.lstm_units[i], 
                                               return_sequences=return_sequences)))
                else:
                    model.add(LSTM(self.lstm_units[i], 
                                  return_sequences=return_sequences))
                
                model.add(Dropout(self.dropout_rate))
            
            # 输出层
            if self.prediction_steps == 1:
                model.add(Dense(1))
            else:
                model.add(Dense(self.prediction_steps))
            
            # 编译模型
            optimizer = Adam(learning_rate=self.learning_rate)
            model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
            
            return model
            
        except Exception as e:
            self.logger.error(f"构建模型失败: {e}")
            return None
    
    def train_model(self, X_train: np.ndarray, y_train: np.ndarray, 
                   X_val: np.ndarray = None, y_val: np.ndarray = None,
                   progress_callback=None) -> bool:
        """
        训练LSTM模型
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
            X_val: 验证特征
            y_val: 验证目标
            progress_callback: 进度回调函数
            
        Returns:
            训练是否成功
        """
        try:
            if not TENSORFLOW_AVAILABLE:
                self.logger.warning("TensorFlow不可用，使用简化模型")
                return self.train_simple_model(X_train, y_train)
            
            # 构建模型
            input_shape = (X_train.shape[1], X_train.shape[2])
            self.model = self.build_model(input_shape)
            
            if self.model is None:
                return False
            
            # 设置回调函数
            callbacks = []
            
            # 早停
            early_stopping = EarlyStopping(
                monitor='val_loss' if X_val is not None else 'loss',
                patience=10,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
            
            # 学习率调度
            lr_scheduler = ReduceLROnPlateau(
                monitor='val_loss' if X_val is not None else 'loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
            callbacks.append(lr_scheduler)
            
            # 模型检查点
            checkpoint = ModelCheckpoint(
                'lstm_model_best.h5',
                monitor='val_loss' if X_val is not None else 'loss',
                save_best_only=True,
                save_weights_only=True
            )
            callbacks.append(checkpoint)
            
            # 训练模型
            validation_data = (X_val, y_val) if X_val is not None and y_val is not None else None
            
            self.training_history = self.model.fit(
                X_train, y_train,
                batch_size=self.batch_size,
                epochs=self.epochs,
                validation_data=validation_data,
                callbacks=callbacks,
                verbose=0
            )
            
            # 计算性能指标
            self.calculate_performance_metrics(X_train, y_train, X_val, y_val)
            
            self.logger.info("LSTM模型训练完成")
            return True
            
        except Exception as e:
            self.logger.error(f"训练模型失败: {e}")
            return False
    
    def train_simple_model(self, X_train: np.ndarray, y_train: np.ndarray) -> bool:
        """训练简化模型（当TensorFlow不可用时）"""
        try:
            # 使用简单的线性回归作为备选
            from sklearn.linear_model import LinearRegression
            from sklearn.ensemble import RandomForestRegressor
            
            # 将3D数据展平为2D
            X_train_flat = X_train.reshape(X_train.shape[0], -1)
            
            # 使用随机森林作为简化模型
            self.model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.model.fit(X_train_flat, y_train)
            
            self.logger.info("简化模型训练完成")
            return True
            
        except Exception as e:
            self.logger.error(f"训练简化模型失败: {e}")
            return False

    def predict(self, X: np.ndarray, return_confidence: bool = False) -> Dict:
        """
        进行价格预测

        Args:
            X: 输入特征
            return_confidence: 是否返回置信区间

        Returns:
            预测结果字典
        """
        try:
            if self.model is None:
                raise ValueError("模型未训练")

            if TENSORFLOW_AVAILABLE and hasattr(self.model, 'predict'):
                # TensorFlow模型预测
                predictions = self.model.predict(X, verbose=0)
            else:
                # 简化模型预测
                X_flat = X.reshape(X.shape[0], -1)
                predictions = self.model.predict(X_flat)

            # 计算置信区间（简化实现）
            confidence_intervals = None
            if return_confidence:
                # 使用预测方差估计置信区间
                std_dev = np.std(predictions, axis=0) if len(predictions.shape) > 1 else np.std(predictions)
                confidence_intervals = {
                    'lower': predictions - 1.96 * std_dev,
                    'upper': predictions + 1.96 * std_dev
                }

            # 计算趋势方向概率
            direction_probs = self.calculate_direction_probability(predictions)

            result = {
                'predictions': predictions,
                'confidence_intervals': confidence_intervals,
                'direction_probabilities': direction_probs,
                'prediction_time': datetime.now(),
                'model_type': 'LSTM' if TENSORFLOW_AVAILABLE else 'RandomForest'
            }

            return result

        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            return {}

    def calculate_direction_probability(self, predictions: np.ndarray) -> Dict:
        """计算价格方向概率"""
        try:
            if len(predictions.shape) == 1:
                # 单步预测
                current_price = predictions[0] if len(predictions) > 0 else 0
                return {
                    'up_probability': 0.6 if current_price > 0 else 0.4,
                    'down_probability': 0.4 if current_price > 0 else 0.6
                }
            else:
                # 多步预测
                up_count = np.sum(np.diff(predictions, axis=1) > 0)
                total_count = predictions.shape[0] * (predictions.shape[1] - 1)
                up_prob = up_count / total_count if total_count > 0 else 0.5

                return {
                    'up_probability': up_prob,
                    'down_probability': 1 - up_prob
                }

        except Exception as e:
            self.logger.error(f"计算方向概率失败: {e}")
            return {'up_probability': 0.5, 'down_probability': 0.5}

    def calculate_performance_metrics(self, X_train: np.ndarray, y_train: np.ndarray,
                                    X_val: np.ndarray = None, y_val: np.ndarray = None):
        """计算模型性能指标"""
        try:
            metrics = {}

            # 训练集性能
            train_pred = self.predict(X_train)['predictions']
            if len(train_pred) > 0:
                train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
                train_mae = mean_absolute_error(y_train, train_pred)
                train_mape = np.mean(np.abs((y_train - train_pred) / y_train)) * 100

                metrics['train_rmse'] = train_rmse
                metrics['train_mae'] = train_mae
                metrics['train_mape'] = train_mape

                # 方向准确率
                if len(y_train) > 1:
                    actual_direction = np.diff(y_train) > 0
                    pred_direction = np.diff(train_pred.flatten()) > 0
                    direction_accuracy = np.mean(actual_direction == pred_direction)
                    metrics['train_direction_accuracy'] = direction_accuracy

            # 验证集性能
            if X_val is not None and y_val is not None:
                val_pred = self.predict(X_val)['predictions']
                if len(val_pred) > 0:
                    val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
                    val_mae = mean_absolute_error(y_val, val_pred)
                    val_mape = np.mean(np.abs((y_val - val_pred) / y_val)) * 100

                    metrics['val_rmse'] = val_rmse
                    metrics['val_mae'] = val_mae
                    metrics['val_mape'] = val_mape

                    # 验证集方向准确率
                    if len(y_val) > 1:
                        actual_direction = np.diff(y_val) > 0
                        pred_direction = np.diff(val_pred.flatten()) > 0
                        direction_accuracy = np.mean(actual_direction == pred_direction)
                        metrics['val_direction_accuracy'] = direction_accuracy

            self.performance_metrics = metrics

        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")

    def predict_realtime(self, latest_data: pd.DataFrame) -> Dict:
        """
        实时价格预测

        Args:
            latest_data: 最新市场数据

        Returns:
            实时预测结果
        """
        try:
            # 检查缓存
            current_time = datetime.now()
            if (self.last_prediction_time and
                (current_time - self.last_prediction_time).seconds < 60):
                return self.prediction_cache

            # 创建特征
            features = self.create_features(latest_data)

            # 准备预测数据
            if len(features) < self.sequence_length:
                raise ValueError(f"数据不足，需要至少{self.sequence_length}条记录")

            # 获取最后一个序列
            feature_columns = [col for col in features.columns if col not in ['open', 'high', 'low', 'close', 'volume']]
            feature_data = features[feature_columns].values

            # 使用已训练的scaler进行归一化
            scaled_features = self.scaler.transform(feature_data)

            # 创建预测输入
            X_pred = scaled_features[-self.sequence_length:].reshape(1, self.sequence_length, -1)

            # 进行预测
            prediction_result = self.predict(X_pred, return_confidence=True)

            # 添加额外信息
            current_price = latest_data['close'].iloc[-1]
            prediction_result['current_price'] = current_price
            prediction_result['symbol'] = getattr(latest_data, 'symbol', 'UNKNOWN')

            # 计算预测价格变化
            if len(prediction_result['predictions']) > 0:
                pred_price = prediction_result['predictions'][0]
                if isinstance(pred_price, np.ndarray):
                    pred_price = pred_price[0]

                price_change = (pred_price - current_price) / current_price * 100
                prediction_result['price_change_percent'] = price_change

            # 缓存结果
            self.prediction_cache = prediction_result
            self.last_prediction_time = current_time

            return prediction_result

        except Exception as e:
            self.logger.error(f"实时预测失败: {e}")
            return {}

    def get_model_summary(self) -> Dict:
        """获取模型摘要信息"""
        try:
            summary = {
                'model_type': 'LSTM' if TENSORFLOW_AVAILABLE else 'RandomForest',
                'sequence_length': self.sequence_length,
                'prediction_steps': self.prediction_steps,
                'lstm_units': self.lstm_units,
                'dropout_rate': self.dropout_rate,
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size,
                'epochs': self.epochs,
                'use_bidirectional': self.use_bidirectional,
                'use_attention': self.use_attention,
                'performance_metrics': self.performance_metrics,
                'training_completed': self.model is not None
            }

            if TENSORFLOW_AVAILABLE and self.model and hasattr(self.model, 'count_params'):
                summary['total_parameters'] = self.model.count_params()

            return summary

        except Exception as e:
            self.logger.error(f"获取模型摘要失败: {e}")
            return {}

    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        try:
            if self.model is None:
                return False

            model_data = {
                'model_config': self.get_model_summary(),
                'scaler': self.scaler,
                'performance_metrics': self.performance_metrics,
                'feature_importance': self.feature_importance
            }

            if TENSORFLOW_AVAILABLE and hasattr(self.model, 'save'):
                # 保存TensorFlow模型
                self.model.save(f"{filepath}_model.h5")

                # 保存其他数据
                with open(f"{filepath}_data.pkl", 'wb') as f:
                    pickle.dump(model_data, f)
            else:
                # 保存简化模型
                model_data['model'] = self.model
                with open(f"{filepath}.pkl", 'wb') as f:
                    pickle.dump(model_data, f)

            self.logger.info(f"模型已保存到: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            return False

    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            if TENSORFLOW_AVAILABLE:
                # 加载TensorFlow模型
                try:
                    self.model = tf.keras.models.load_model(f"{filepath}_model.h5")

                    # 加载其他数据
                    with open(f"{filepath}_data.pkl", 'rb') as f:
                        model_data = pickle.load(f)

                    self.scaler = model_data['scaler']
                    self.performance_metrics = model_data['performance_metrics']
                    self.feature_importance = model_data['feature_importance']

                except FileNotFoundError:
                    # 尝试加载简化模型
                    with open(f"{filepath}.pkl", 'rb') as f:
                        model_data = pickle.load(f)

                    self.model = model_data['model']
                    self.scaler = model_data['scaler']
                    self.performance_metrics = model_data['performance_metrics']
                    self.feature_importance = model_data['feature_importance']
            else:
                # 加载简化模型
                with open(f"{filepath}.pkl", 'rb') as f:
                    model_data = pickle.load(f)

                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.performance_metrics = model_data['performance_metrics']
                self.feature_importance = model_data['feature_importance']

            self.logger.info(f"模型已从 {filepath} 加载")
            return True

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False
