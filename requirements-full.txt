# WMZC量化交易系统 - 完整功能依赖
# 包含所有可选功能的完整依赖包

# ==================== 核心依赖 ====================
aiohttp>=3.8.0
pandas>=1.5.0
numpy>=1.21.0
websockets>=10.0
certifi>=2022.12.7
psutil>=5.9.0
aiosqlite>=0.17.0

# ==================== 企业级功能 ====================
# 分布式服务
python-consul>=1.1.0

# 深度学习
tensorflow>=2.10.0
keras>=2.10.0

# 机器学习
scikit-learn>=1.1.0

# 高性能计算
numba>=0.56.0

# 分布式缓存
redis>=4.3.0

# 数据可视化
matplotlib>=3.5.0
plotly>=5.10.0

# 技术指标
TA-Lib>=0.4.0

# 加密货币数据
ccxt>=2.0.0

# ==================== 开发工具 ====================
black>=22.0.0
pylint>=2.15.0
pytest>=7.0.0
pytest-asyncio>=0.19.0
mypy>=0.991
