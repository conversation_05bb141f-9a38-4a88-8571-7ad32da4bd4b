#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pandas兼容性修复效果
"""

import pandas as pd
import numpy as np
import logging

def test_kdj_calculation():
    """测试KDJ计算修复"""
    print("🔍 测试KDJ计算修复...")
    
    try:
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 100
        price_changes = np.random.normal(0, 2, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] + change
            prices.append(max(new_price, 1))  # 确保价格为正
        
        # 创建OHLC数据
        high = pd.Series([p * (1 + np.random.uniform(0, 0.02)) for p in prices], index=dates)
        low = pd.Series([p * (1 - np.random.uniform(0, 0.02)) for p in prices], index=dates)
        close = pd.Series(prices, index=dates)
        
        # 测试KDJ计算
        from asp import TechnicalIndicators
        
        k, d, j = TechnicalIndicators.calculate_kdj(high, low, close)
        
        print(f"✅ KDJ计算成功")
        print(f"📊 K值范围: {k.min():.2f} - {k.max():.2f}")
        print(f"📊 D值范围: {d.min():.2f} - {d.max():.2f}")
        print(f"📊 J值范围: {j.min():.2f} - {j.max():.2f}")
        
        # 检查是否有NaN值
        if k.isna().any() or d.isna().any() or j.isna().any():
            print("⚠️ 警告: KDJ计算结果包含NaN值")
        else:
            print("✅ KDJ计算结果无NaN值")
        
        return True
        
    except Exception as e:
        print(f"❌ KDJ计算测试失败: {e}")
        return False

def test_rsi_calculation():
    """测试RSI计算修复"""
    print("🔍 测试RSI计算修复...")
    
    try:
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 100
        price_changes = np.random.normal(0, 1, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] + change
            prices.append(max(new_price, 1))
        
        close = pd.Series(prices, index=dates)
        
        # 测试RSI计算
        from asp import TechnicalIndicators
        
        rsi = TechnicalIndicators.calculate_rsi(close)
        
        print(f"✅ RSI计算成功")
        print(f"📊 RSI值范围: {rsi.min():.2f} - {rsi.max():.2f}")
        
        # 检查RSI值是否在合理范围内
        if rsi.min() >= 0 and rsi.max() <= 100:
            print("✅ RSI值在合理范围内 (0-100)")
        else:
            print("⚠️ 警告: RSI值超出合理范围")
        
        # 检查是否有NaN值
        if rsi.isna().any():
            print("⚠️ 警告: RSI计算结果包含NaN值")
        else:
            print("✅ RSI计算结果无NaN值")
        
        return True
        
    except Exception as e:
        print(f"❌ RSI计算测试失败: {e}")
        return False

def test_financial_data_provider():
    """测试financial_data_provider中的RSI计算"""
    print("🔍 测试financial_data_provider RSI计算...")
    
    try:
        from financial_data_provider import FinancialDataProvider
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        prices = pd.Series(np.random.uniform(90, 110, 50), index=dates)
        
        provider = FinancialDataProvider()
        rsi = provider._calculate_rsi(prices)
        
        print(f"✅ FinancialDataProvider RSI计算成功")
        print(f"📊 RSI值范围: {rsi.min():.2f} - {rsi.max():.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ FinancialDataProvider RSI计算测试失败: {e}")
        return False

def test_lstm_predictor():
    """测试lstm_predictor中的RSI计算"""
    print("🔍 测试lstm_predictor RSI计算...")
    
    try:
        from lstm_predictor import LSTMPredictor
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        prices = pd.Series(np.random.uniform(90, 110, 50), index=dates)
        
        predictor = LSTMPredictor()
        rsi = predictor.calculate_rsi(prices)
        
        print(f"✅ LSTMPredictor RSI计算成功")
        print(f"📊 RSI值范围: {rsi.min():.2f} - {rsi.max():.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ LSTMPredictor RSI计算测试失败: {e}")
        return False

def test_backtest_engine():
    """测试backtest_engine中的RSI计算"""
    print("🔍 测试backtest_engine RSI计算...")
    
    try:
        from backtest_engine import BacktestEngine
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        data = pd.DataFrame({
            'close': np.random.uniform(90, 110, 50),
            'high': np.random.uniform(95, 115, 50),
            'low': np.random.uniform(85, 105, 50),
            'volume': np.random.uniform(1000, 10000, 50)
        }, index=dates)
        
        engine = BacktestEngine()
        result = engine.calculate_rsi(data)
        
        print(f"✅ BacktestEngine RSI计算成功")
        print(f"📊 RSI值范围: {result['rsi'].min():.2f} - {result['rsi'].max():.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ BacktestEngine RSI计算测试失败: {e}")
        return False

def test_pandas_version():
    """检查pandas版本"""
    print("🔍 检查pandas版本...")
    
    try:
        print(f"📦 pandas版本: {pd.__version__}")
        print(f"📦 numpy版本: {np.__version__}")
        
        # 测试pandas.where是否存在
        try:
            test_series = pd.Series([1, -1, 2, -2])
            result = test_series.where(test_series > 0, 0)
            print("✅ pandas.Series.where 方法可用")
        except AttributeError:
            print("❌ pandas.Series.where 方法不可用")
        
        # 测试pd.where是否存在
        try:
            test_series = pd.Series([1, -1, 2, -2])
            result = pd.where(test_series > 0, test_series, 0)
            print("✅ pd.where 函数可用")
        except AttributeError:
            print("❌ pd.where 函数不可用 - 这是预期的，因为新版本pandas移除了此函数")
        
        return True
        
    except Exception as e:
        print(f"❌ pandas版本检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试pandas兼容性修复...")
    print("=" * 60)
    
    all_passed = True
    
    # 检查pandas版本
    if not test_pandas_version():
        all_passed = False
    
    print("-" * 40)
    
    # 测试KDJ计算
    if not test_kdj_calculation():
        all_passed = False
    
    print("-" * 40)
    
    # 测试RSI计算
    if not test_rsi_calculation():
        all_passed = False
    
    print("-" * 40)
    
    # 测试各个模块的RSI计算
    if not test_financial_data_provider():
        all_passed = False
    
    print("-" * 40)
    
    if not test_lstm_predictor():
        all_passed = False
    
    print("-" * 40)
    
    if not test_backtest_engine():
        all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！pandas兼容性问题已修复")
        print("\n✅ 修复总结:")
        print("• 将 pandas.where 替换为 numpy.where")
        print("• 修复了KDJ指标计算中的兼容性问题")
        print("• 修复了RSI指标计算中的兼容性问题")
        print("• 更新了所有相关模块的计算方法")
        print("\n💡 建议:")
        print("• 重启应用程序以应用修复")
        print("• 观察日志确认KDJ计算错误已消失")
    else:
        print("❌ 部分测试失败，请检查相关代码")
    
    return all_passed

if __name__ == "__main__":
    main()
