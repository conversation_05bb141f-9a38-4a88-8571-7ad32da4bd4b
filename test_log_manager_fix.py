#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志管理器修复效果
"""

import time
import threading

def test_external_log_manager_init():
    """测试外部日志管理器初始化"""
    print("🔍 测试外部日志管理器初始化...")
    
    start_time = time.time()
    
    try:
        from log_manager import LogManager as ExternalLogManager
        log_manager = ExternalLogManager()
        
        init_time = time.time() - start_time
        print(f"✅ 外部日志管理器初始化成功，耗时: {init_time:.2f}秒")
        
        if init_time > 1.0:
            print(f"⚠️ 初始化时间较长: {init_time:.2f}秒，可能会阻塞GUI")
        else:
            print(f"✅ 初始化时间正常: {init_time:.2f}秒")
            
        return True
        
    except Exception as e:
        print(f"❌ 外部日志管理器初始化失败: {e}")
        return False

def test_async_init():
    """测试异步初始化"""
    print("🔍 测试异步初始化...")
    
    init_completed = threading.Event()
    init_result = {"success": False, "error": None}
    
    def async_init():
        try:
            from log_manager import LogManager as ExternalLogManager
            log_manager = ExternalLogManager()
            init_result["success"] = True
            print("✅ 异步初始化完成")
        except Exception as e:
            init_result["error"] = str(e)
            print(f"❌ 异步初始化失败: {e}")
        finally:
            init_completed.set()
    
    # 启动异步初始化
    start_time = time.time()
    thread = threading.Thread(target=async_init, daemon=True)
    thread.start()
    
    # 模拟GUI继续运行
    gui_blocked = False
    for i in range(10):
        if init_completed.is_set():
            break
        time.sleep(0.1)
        print(f"GUI继续运行... {i+1}/10")
    else:
        gui_blocked = True
        print("⚠️ GUI可能被阻塞")
    
    # 等待初始化完成
    init_completed.wait(timeout=10)
    total_time = time.time() - start_time
    
    if init_result["success"]:
        print(f"✅ 异步初始化测试成功，总耗时: {total_time:.2f}秒")
        if not gui_blocked:
            print("✅ GUI未被阻塞")
        return True
    else:
        print(f"❌ 异步初始化测试失败: {init_result['error']}")
        return False

if __name__ == "__main__":
    print("🔍 开始测试日志管理器修复效果...")
    
    # 测试同步初始化
    sync_test = test_external_log_manager_init()
    
    print("\n" + "="*50 + "\n")
    
    # 测试异步初始化
    async_test = test_async_init()
    
    print("\n" + "="*50 + "\n")
    
    if sync_test and async_test:
        print("🎉 所有测试通过！日志管理器修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
