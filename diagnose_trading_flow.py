#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断交易启动后的数据流问题
"""

import sys
import os
import json
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_trading_flow():
    """分析交易流程"""
    print("🔍 分析交易启动后的数据流问题...")
    print("=" * 60)
    
    print("📋 交易启动流程分析:")
    print("1. 用户点击'开始交易'按钮")
    print("2. 显示确认对话框")
    print("3. 用户点击'是(Y)'")
    print("4. 调用 start_trading_async() 方法")
    print("5. 调用 activate_all_strategies() 方法")
    print("6. 激活 RSI/MACD 策略")
    print("7. 策略调用 _start_data_processing_loop()")
    print("8. 获取K线数据 _fetch_kline_data_common()")
    print("9. 计算技术指标")
    print("10. 启动定时更新循环 _schedule_next_update()")
    print()
    
    print("🔍 可能的问题点:")
    print()
    
    print("问题1: 策略未正确激活")
    print("   • 检查 rsi_enabled_var 和 macd_enabled_var 是否为 True")
    print("   • 检查策略实例是否存在")
    print()
    
    print("问题2: ExchangeManager 状态问题")
    print("   • HTTP会话可能已关闭")
    print("   • 事件循环状态异常")
    print("   • API调用失败")
    print()
    
    print("问题3: 异步任务管理问题")
    print("   • asyncio.create_task() 创建的任务没有被正确等待")
    print("   • 任务可能被垃圾回收")
    print("   • 事件循环中的异常被忽略")
    print()
    
    print("问题4: 定时器调度问题")
    print("   • _schedule_next_update() 中的异步任务可能失败")
    print("   • 30秒的更新间隔太长，用户看不到立即效果")
    print("   • 异步调度器中的异常处理")

def check_strategy_activation():
    """检查策略激活状态"""
    print("\n🔍 检查策略激活状态...")
    
    try:
        config_file = "wmzc_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查策略配置
            strategy_configs = config.get("strategy_configs", {})
            rsi_config = strategy_configs.get("rsi_strategy", {})
            macd_config = strategy_configs.get("macd_strategy", {})
            
            print(f"📊 策略配置状态:")
            print(f"   RSI策略启用: {rsi_config.get('enabled', False)}")
            print(f"   MACD策略启用: {macd_config.get('enabled', False)}")
            
            if not rsi_config.get('enabled', False) and not macd_config.get('enabled', False):
                print("⚠️ 没有启用任何策略！这可能是问题的根源。")
                return False
            
            return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查策略激活状态失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案:")
    print("=" * 60)
    
    print("解决方案1: 启用交易策略")
    print("   1. 切换到 'RSI策略' 或 'MACD策略' 标签页")
    print("   2. 勾选 '启用RSI策略' 或 '启用MACD策略' 复选框")
    print("   3. 设置合适的参数")
    print("   4. 保存配置")
    print()
    
    print("解决方案2: 检查日志输出")
    print("   • 查看控制台是否有以下日志:")
    print("     - '🚀 RSI策略已激活，开始数据获取和指标计算'")
    print("     - '📈 开始获取K线数据...'")
    print("     - '📡 API调用: ...'")
    print("     - '✅ API响应成功: 200'")
    print()
    
    print("解决方案3: 修复异步任务管理")
    print("   • 问题: asyncio.create_task() 创建的任务可能被垃圾回收")
    print("   • 解决: 需要保持任务引用或使用 asyncio.ensure_future()")
    print()
    
    print("解决方案4: 缩短更新间隔")
    print("   • 当前30秒更新间隔太长")
    print("   • 建议改为5-10秒，让用户看到立即效果")
    print()
    
    print("解决方案5: 添加立即执行")
    print("   • 在策略激活后立即执行一次数据获取")
    print("   • 不要等待30秒才开始第一次更新")

def create_fix_suggestions():
    """创建修复建议"""
    print("\n💡 具体修复建议:")
    print("=" * 60)
    
    print("修复1: 立即执行数据获取")
    print("```python")
    print("async def activate(self):")
    print("    self.is_active = True")
    print("    # 立即执行一次数据获取")
    print("    await self._start_data_processing_loop()")
    print("    # 然后启动定时循环")
    print("    self._schedule_next_update()")
    print("```")
    print()
    
    print("修复2: 保持任务引用")
    print("```python")
    print("def _schedule_next_update(self):")
    print("    if not hasattr(self, 'update_tasks'):")
    print("        self.update_tasks = []")
    print("    task = asyncio.create_task(self._async_update_scheduler())")
    print("    self.update_tasks.append(task)  # 保持引用")
    print("```")
    print()
    
    print("修复3: 缩短更新间隔")
    print("```python")
    print("async def _async_update_scheduler(self):")
    print("    while self.is_active:")
    print("        await asyncio.sleep(10.0)  # 改为10秒")
    print("        # ... 执行更新")
    print("```")
    print()
    
    print("修复4: 添加错误重试")
    print("```python")
    print("async def _fetch_kline_data_common(self, strategy_name):")
    print("    max_retries = 3")
    print("    for attempt in range(max_retries):")
    print("        try:")
    print("            # ... 获取数据")
    print("            break")
    print("        except Exception as e:")
    print("            if attempt == max_retries - 1:")
    print("                raise")
    print("            await asyncio.sleep(2 ** attempt)")
    print("```")

def main():
    """主函数"""
    print("🎯 WMZC量化交易系统 - 交易数据流诊断")
    print("=" * 60)
    
    try:
        # 分析交易流程
        analyze_trading_flow()
        
        # 检查策略激活状态
        strategy_ok = check_strategy_activation()
        
        # 提供解决方案
        provide_solutions()
        
        # 创建修复建议
        create_fix_suggestions()
        
        print("\n" + "=" * 60)
        print("📊 诊断结果总结:")
        
        if strategy_ok:
            print("✅ 策略配置检查通过")
        else:
            print("❌ 策略配置有问题")
        
        print("\n🎯 最可能的原因:")
        print("1. 策略未启用（最常见）")
        print("2. 异步任务被垃圾回收")
        print("3. 更新间隔太长（30秒）")
        print("4. API调用失败但错误被忽略")
        
        print("\n🚀 立即行动:")
        print("1. 检查策略标签页，确保至少启用一个策略")
        print("2. 查看控制台日志，寻找错误信息")
        print("3. 如果没有日志输出，说明策略未激活")
        print("4. 应用下面的代码修复")
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")

if __name__ == "__main__":
    main()
