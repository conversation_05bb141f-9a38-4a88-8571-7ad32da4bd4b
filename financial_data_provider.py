#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC量化交易系统 - 传统金融市场数据提供者
支持黄金、纳斯达克、标普500、道琼斯等传统金融市场数据获取
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor
import time

@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    name: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    high_24h: float = 0.0
    low_24h: float = 0.0
    open_price: float = 0.0

@dataclass
class PredictionResult:
    """预测结果结构"""
    symbol: str
    current_price: float
    predicted_price: float
    prediction_change: float
    prediction_change_percent: float
    confidence: float
    trend_direction: str  # 'up', 'down', 'sideways'
    technical_signals: Dict
    timestamp: datetime

class FinancialDataProvider:
    """传统金融市场数据提供者"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.cache = {}
        self.cache_timeout = 60  # 缓存60秒
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 支持的金融产品配置
        self.symbols_config = {
            'GOLD': {
                'yahoo_symbol': 'GC=F',
                'name': '黄金期货',
                'currency': 'USD',
                'multiplier': 1
            },
            'NASDAQ': {
                'yahoo_symbol': '^IXIC',
                'name': '纳斯达克综合指数',
                'currency': 'USD',
                'multiplier': 1
            },
            'SP500': {
                'yahoo_symbol': '^GSPC',
                'name': '标普500指数',
                'currency': 'USD',
                'multiplier': 1
            },
            'DJIA': {
                'yahoo_symbol': '^DJI',
                'name': '道琼斯工业指数',
                'currency': 'USD',
                'multiplier': 1
            }
        }
        
        # 技术指标计算参数
        self.technical_params = {
            'sma_short': 5,
            'sma_long': 20,
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9
        }

    async def initialize(self):
        """初始化数据提供者"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'WMZC Trading System 1.0'}
            )
            self.logger.info("金融数据提供者初始化完成")
        except Exception as e:
            self.logger.error(f"初始化金融数据提供者失败: {e}")
            raise

    async def close(self):
        """关闭数据提供者"""
        try:
            if self.session:
                await self.session.close()
            self.executor.shutdown(wait=True)
            self.logger.info("金融数据提供者已关闭")
        except Exception as e:
            self.logger.error(f"关闭金融数据提供者失败: {e}")

    def _is_cache_valid(self, symbol: str) -> bool:
        """检查缓存是否有效"""
        if symbol not in self.cache:
            return False
        
        cache_time = self.cache[symbol].get('timestamp', 0)
        return time.time() - cache_time < self.cache_timeout

    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """获取市场数据"""
        try:
            # 检查缓存
            if self._is_cache_valid(symbol):
                cached_data = self.cache[symbol]['data']
                self.logger.debug(f"使用缓存数据: {symbol}")
                return cached_data
            
            # 获取新数据
            if symbol not in self.symbols_config:
                raise ValueError(f"不支持的交易品种: {symbol}")
            
            config = self.symbols_config[symbol]
            yahoo_symbol = config['yahoo_symbol']
            
            # 使用线程池执行同步的yfinance调用
            loop = asyncio.get_event_loop()
            ticker_data = await loop.run_in_executor(
                self.executor, 
                self._fetch_yahoo_data, 
                yahoo_symbol
            )
            
            if not ticker_data:
                return None
            
            # 构建MarketData对象
            market_data = MarketData(
                symbol=symbol,
                name=config['name'],
                price=ticker_data['price'],
                change=ticker_data['change'],
                change_percent=ticker_data['change_percent'],
                volume=ticker_data['volume'],
                high_24h=ticker_data['high'],
                low_24h=ticker_data['low'],
                open_price=ticker_data['open'],
                timestamp=datetime.now()
            )
            
            # 更新缓存
            self.cache[symbol] = {
                'data': market_data,
                'timestamp': time.time()
            }
            
            self.logger.debug(f"获取市场数据成功: {symbol} = {market_data.price}")
            return market_data
            
        except Exception as e:
            self.logger.error(f"获取市场数据失败 {symbol}: {e}")
            return None

    def _fetch_yahoo_data(self, yahoo_symbol: str) -> Optional[Dict]:
        """获取Yahoo Finance数据（同步方法）"""
        try:
            ticker = yf.Ticker(yahoo_symbol)
            
            # 获取当前价格信息
            info = ticker.info
            hist = ticker.history(period="2d")
            
            if hist.empty:
                return None
            
            current_price = hist['Close'].iloc[-1]
            previous_close = info.get('previousClose', hist['Close'].iloc[-2] if len(hist) > 1 else current_price)
            
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close != 0 else 0
            
            return {
                'price': float(current_price),
                'change': float(change),
                'change_percent': float(change_percent),
                'volume': int(hist['Volume'].iloc[-1]) if not pd.isna(hist['Volume'].iloc[-1]) else 0,
                'high': float(hist['High'].iloc[-1]),
                'low': float(hist['Low'].iloc[-1]),
                'open': float(hist['Open'].iloc[-1])
            }
            
        except Exception as e:
            self.logger.error(f"获取Yahoo数据失败 {yahoo_symbol}: {e}")
            return None

    async def get_historical_data(self, symbol: str, period: str = "1mo") -> Optional[pd.DataFrame]:
        """获取历史数据"""
        try:
            if symbol not in self.symbols_config:
                raise ValueError(f"不支持的交易品种: {symbol}")
            
            config = self.symbols_config[symbol]
            yahoo_symbol = config['yahoo_symbol']
            
            # 使用线程池执行同步调用
            loop = asyncio.get_event_loop()
            hist_data = await loop.run_in_executor(
                self.executor,
                self._fetch_historical_data,
                yahoo_symbol,
                period
            )
            
            return hist_data
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败 {symbol}: {e}")
            return None

    def _fetch_historical_data(self, yahoo_symbol: str, period: str) -> Optional[pd.DataFrame]:
        """获取历史数据（同步方法）"""
        try:
            ticker = yf.Ticker(yahoo_symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                return None
            
            # 重命名列以符合标准格式
            hist = hist.rename(columns={
                'Open': 'open',
                'High': 'high', 
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })
            
            return hist
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败 {yahoo_symbol}: {e}")
            return None

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            if df.empty or 'close' not in df.columns:
                return df
            
            # 移动平均线
            df['sma_short'] = df['close'].rolling(window=self.technical_params['sma_short']).mean()
            df['sma_long'] = df['close'].rolling(window=self.technical_params['sma_long']).mean()
            
            # RSI
            df['rsi'] = self._calculate_rsi(df['close'], self.technical_params['rsi_period'])
            
            # MACD
            macd_line, signal_line, histogram = self._calculate_macd(
                df['close'],
                self.technical_params['macd_fast'],
                self.technical_params['macd_slow'],
                self.technical_params['macd_signal']
            )
            df['macd'] = macd_line
            df['macd_signal'] = signal_line
            df['macd_histogram'] = histogram
            
            # 布林带
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
            return df
            
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return df

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            import numpy as np
            delta = prices.diff()
            gain = pd.Series(np.where(delta > 0, delta, 0), index=delta.index).rolling(window=period).mean()
            loss = pd.Series(np.where(delta < 0, -delta, 0), index=delta.index).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series(index=prices.index, dtype=float)

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line
            return macd_line, signal_line, histogram
        except Exception:
            empty_series = pd.Series(index=prices.index, dtype=float)
            return empty_series, empty_series, empty_series

    async def predict_price_movement(self, symbol: str) -> Optional[PredictionResult]:
        """预测价格走势"""
        try:
            # 获取历史数据
            hist_data = await self.get_historical_data(symbol, "3mo")
            if hist_data is None or hist_data.empty:
                return None
            
            # 计算技术指标
            hist_data = self.calculate_technical_indicators(hist_data)
            
            # 获取当前市场数据
            current_data = await self.get_market_data(symbol)
            if not current_data:
                return None
            
            # 简单的技术分析预测
            prediction = self._analyze_technical_signals(hist_data, current_data)
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"预测价格走势失败 {symbol}: {e}")
            return None

    def _analyze_technical_signals(self, hist_data: pd.DataFrame, current_data: MarketData) -> PredictionResult:
        """分析技术信号"""
        try:
            latest = hist_data.iloc[-1]
            prev = hist_data.iloc[-2] if len(hist_data) > 1 else latest
            
            signals = {}
            confidence_scores = []
            
            # RSI信号
            rsi = latest.get('rsi', 50)
            if rsi < 30:
                signals['rsi'] = 'oversold_bullish'
                confidence_scores.append(0.7)
            elif rsi > 70:
                signals['rsi'] = 'overbought_bearish'
                confidence_scores.append(0.7)
            else:
                signals['rsi'] = 'neutral'
                confidence_scores.append(0.3)
            
            # MACD信号
            macd = latest.get('macd', 0)
            macd_signal = latest.get('macd_signal', 0)
            prev_macd = prev.get('macd', 0)
            prev_macd_signal = prev.get('macd_signal', 0)
            
            if macd > macd_signal and prev_macd <= prev_macd_signal:
                signals['macd'] = 'bullish_crossover'
                confidence_scores.append(0.8)
            elif macd < macd_signal and prev_macd >= prev_macd_signal:
                signals['macd'] = 'bearish_crossover'
                confidence_scores.append(0.8)
            else:
                signals['macd'] = 'no_crossover'
                confidence_scores.append(0.4)
            
            # 移动平均线信号
            sma_short = latest.get('sma_short', current_data.price)
            sma_long = latest.get('sma_long', current_data.price)
            
            if sma_short > sma_long:
                signals['sma'] = 'bullish'
                confidence_scores.append(0.6)
            else:
                signals['sma'] = 'bearish'
                confidence_scores.append(0.6)
            
            # 综合预测
            bullish_signals = sum(1 for s in signals.values() if 'bullish' in s)
            bearish_signals = sum(1 for s in signals.values() if 'bearish' in s)
            
            if bullish_signals > bearish_signals:
                trend_direction = 'up'
                predicted_change_percent = np.random.uniform(0.5, 3.0)  # 简化预测
            elif bearish_signals > bullish_signals:
                trend_direction = 'down'
                predicted_change_percent = np.random.uniform(-3.0, -0.5)
            else:
                trend_direction = 'sideways'
                predicted_change_percent = np.random.uniform(-0.5, 0.5)
            
            predicted_price = current_data.price * (1 + predicted_change_percent / 100)
            predicted_change = predicted_price - current_data.price
            
            # 计算置信度
            avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.5
            
            return PredictionResult(
                symbol=current_data.symbol,
                current_price=current_data.price,
                predicted_price=predicted_price,
                prediction_change=predicted_change,
                prediction_change_percent=predicted_change_percent,
                confidence=avg_confidence,
                trend_direction=trend_direction,
                technical_signals=signals,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"分析技术信号失败: {e}")
            # 返回默认预测
            return PredictionResult(
                symbol=current_data.symbol,
                current_price=current_data.price,
                predicted_price=current_data.price,
                prediction_change=0,
                prediction_change_percent=0,
                confidence=0.5,
                trend_direction='sideways',
                technical_signals={},
                timestamp=datetime.now()
            )

    async def get_all_market_data(self) -> Dict[str, MarketData]:
        """获取所有支持的市场数据"""
        try:
            tasks = []
            for symbol in self.symbols_config.keys():
                tasks.append(self.get_market_data(symbol))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            market_data = {}
            for symbol, result in zip(self.symbols_config.keys(), results):
                if isinstance(result, MarketData):
                    market_data[symbol] = result
                else:
                    self.logger.warning(f"获取 {symbol} 数据失败: {result}")
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"获取所有市场数据失败: {e}")
            return {}

    def get_supported_symbols(self) -> List[str]:
        """获取支持的交易品种列表"""
        return list(self.symbols_config.keys())

    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """获取交易品种信息"""
        return self.symbols_config.get(symbol)
