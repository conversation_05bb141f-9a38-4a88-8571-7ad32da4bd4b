#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示配置持久化功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_api_credentials_persistence():
    """演示API凭证持久化"""
    print("🔐 演示API凭证持久化功能...")
    
    try:
        from asp import JSONConfigManager
        
        # 创建配置管理器
        config = JSONConfigManager("demo_wmzc_config.json")
        
        print("1. 保存OKX API凭证...")
        success = config.save_api_credentials(
            exchange="OKX",
            api_key="demo_okx_api_key_12345",
            secret_key="demo_okx_secret_key_67890",
            passphrase="demo_okx_passphrase_abc"
        )
        print(f"   保存结果: {'✅ 成功' if success else '❌ 失败'}")
        
        print("2. 保存Gate.io API凭证...")
        success = config.save_api_credentials(
            exchange="Gate",
            api_key="demo_gate_api_key_54321",
            secret_key="demo_gate_secret_key_09876",
            passphrase=""  # Gate.io不需要passphrase
        )
        print(f"   保存结果: {'✅ 成功' if success else '❌ 失败'}")
        
        print("3. 加载OKX API凭证...")
        okx_creds = config.load_api_credentials("OKX")
        if okx_creds:
            print(f"   API Key: {okx_creds['api_key'][:10]}...")
            print(f"   Secret Key: {okx_creds['secret_key'][:10]}...")
            print(f"   Passphrase: {okx_creds['passphrase'][:5]}...")
            print(f"   状态: {'启用' if okx_creds['enabled'] else '禁用'}")
        
        print("4. 加载Gate.io API凭证...")
        gate_creds = config.load_api_credentials("Gate")
        if gate_creds:
            print(f"   API Key: {gate_creds['api_key'][:10]}...")
            print(f"   Secret Key: {gate_creds['secret_key'][:10]}...")
            print(f"   状态: {'启用' if gate_creds['enabled'] else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ API凭证持久化演示失败: {e}")
        return False

def demo_trading_parameters_persistence():
    """演示交易参数持久化"""
    print("\n📊 演示交易参数持久化功能...")
    
    try:
        from asp import JSONConfigManager
        
        config = JSONConfigManager("demo_wmzc_config.json")
        
        print("1. 设置交易参数...")
        trading_params = {
            "position_size": 0.2,
            "risk_level": "high",
            "stop_loss_percentage": 3.0,
            "take_profit_percentage": 6.0,
            "max_open_positions": 10
        }
        
        for key, value in trading_params.items():
            config.set_config("trading_parameters", key, value)
            print(f"   {key}: {value}")
        
        print("2. 设置策略配置...")
        config.set_config("strategy_configs", "rsi_strategy", {
            "enabled": True,
            "period": 21,
            "overbought": 75,
            "oversold": 25,
            "trend_filter": True
        })
        
        config.set_config("strategy_configs", "macd_strategy", {
            "enabled": True,
            "fast_period": 8,
            "slow_period": 21,
            "signal_period": 5,
            "trend_filter": False
        })
        
        print("3. 读取保存的配置...")
        saved_position_size = config.get_config("trading_parameters", "position_size")
        saved_rsi_config = config.get_config("strategy_configs", "rsi_strategy")
        
        print(f"   仓位大小: {saved_position_size}")
        print(f"   RSI策略: {saved_rsi_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易参数持久化演示失败: {e}")
        return False

def demo_config_export_import():
    """演示配置导入导出功能"""
    print("\n📁 演示配置导入导出功能...")
    
    try:
        from asp import JSONConfigManager
        
        # 原始配置
        config1 = JSONConfigManager("demo_wmzc_config.json")
        
        print("1. 导出配置（不包含敏感信息）...")
        export_file = "exported_config_demo.json"
        success = config1.export_config(export_file, include_sensitive=False)
        print(f"   导出结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success and os.path.exists(export_file):
            print(f"   导出文件大小: {os.path.getsize(export_file)} 字节")
        
        print("2. 创建新的配置管理器并导入配置...")
        config2 = JSONConfigManager("imported_config_demo.json")
        success = config2.import_config(export_file, merge=True)
        print(f"   导入结果: {'✅ 成功' if success else '❌ 失败'}")
        
        print("3. 验证导入的配置...")
        imported_position_size = config2.get_config("trading_parameters", "position_size")
        imported_rsi_config = config2.get_config("strategy_configs", "rsi_strategy")
        
        print(f"   导入的仓位大小: {imported_position_size}")
        print(f"   导入的RSI配置: {imported_rsi_config}")
        
        # 检查敏感信息是否被正确处理
        okx_creds = config2.load_api_credentials("OKX")
        print(f"   API凭证状态: {'有凭证' if okx_creds.get('api_key') else '无凭证（已过滤）'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入导出演示失败: {e}")
        return False

def demo_config_file_structure():
    """演示配置文件结构"""
    print("\n📋 演示配置文件结构...")
    
    try:
        config_file = "demo_wmzc_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            print("配置文件结构:")
            for section, data in config_data.items():
                if section == "api_credentials":
                    print(f"  📁 {section}:")
                    for exchange, creds in data.items():
                        encrypted_status = "已加密" if creds.get("api_key_encrypted") else "未设置"
                        print(f"    🔑 {exchange}: {encrypted_status}")
                elif isinstance(data, dict):
                    print(f"  📁 {section}: {len(data)} 项配置")
                else:
                    print(f"  📄 {section}: {data}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件结构演示失败: {e}")
        return False

def cleanup_demo_files():
    """清理演示文件"""
    demo_files = [
        "demo_wmzc_config.json",
        "demo_wmzc_config.json.backup",
        "exported_config_demo.json",
        "imported_config_demo.json"
    ]
    
    for file in demo_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️ 已清理: {file}")
        except:
            pass

if __name__ == "__main__":
    print("🎯 WMZC量化交易系统 - 配置持久化功能演示")
    print("=" * 60)
    
    try:
        # 演示API凭证持久化
        demo1 = demo_api_credentials_persistence()
        
        # 演示交易参数持久化
        demo2 = demo_trading_parameters_persistence()
        
        # 演示配置导入导出
        demo3 = demo_config_export_import()
        
        # 演示配置文件结构
        demo4 = demo_config_file_structure()
        
        print("\n" + "=" * 60)
        
        if all([demo1, demo2, demo3, demo4]):
            print("🎉 所有演示完成！配置持久化功能正常工作！")
            print("\n✨ 主要特性:")
            print("  ✅ API凭证加密存储")
            print("  ✅ 交易参数自动保存")
            print("  ✅ 配置导入导出")
            print("  ✅ JSON格式可读性")
            print("  ✅ 程序重启后自动加载")
        else:
            print("⚠️ 部分演示失败")
        
        # 询问是否清理演示文件
        print(f"\n📁 演示文件已创建，是否清理？(y/n): ", end="")
        try:
            choice = input().lower()
            if choice == 'y':
                cleanup_demo_files()
                print("✅ 演示文件已清理")
        except:
            pass
            
    except KeyboardInterrupt:
        print("\n\n👋 演示已中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
