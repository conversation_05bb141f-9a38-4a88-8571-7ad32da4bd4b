﻿2025-07-30 13:33:13,853 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 13:33:13,864 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 13:33:13,868 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 13:33:13,869 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 13:33:13,869 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 13:33:13,870 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 13:33:13,871 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 13:33:13,871 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 13:33:13,876 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 13:33:13,877 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 13:33:15,360 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 13:33:15,361 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 13:33:15,586 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 13:33:15,589 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 13:33:15,589 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 13:33:15,590 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 13:33:15,590 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 13:33:15,590 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 13:33:15,599 - WMZC - INFO - 数据库索引创建完成
2025-07-30 13:33:15,599 - WMZC - INFO - 数据库初始化完成
2025-07-30 13:33:15,601 - WMZC - INFO - 主题设置表创建成功
2025-07-30 13:33:15,603 - WMZC - INFO - 策略配置表创建成功
2025-07-30 13:33:15,605 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 13:33:15,605 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 13:33:15,608 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 13:33:15,611 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 13:33:15,612 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 13:33:15,613 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 13:33:15,643 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 13:33:16,800 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 13:33:16,832 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 13:33:17,920 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 13:33:17,920 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 13:33:17,920 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 13:33:18,223 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 13:33:18,504 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 13:33:18,506 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 13:33:18,603 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 13:33:18,789 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 13:33:18,934 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 13:33:18,973 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 13:33:18,990 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 13:33:18,990 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 13:33:19,089 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 13:33:19,102 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 13:33:19,140 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 13:33:19,199 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 13:33:19,391 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 13:33:19,470 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 13:33:19,558 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 13:33:19,636 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 13:33:19,637 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 13:33:19,638 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 13:33:19,639 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 13:33:19,947 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 13:33:20,037 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 13:33:20,037 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 13:33:20,098 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 13:33:20,535 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 13:33:20,564 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 13:33:20,874 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 13:33:20,925 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 13:33:20,939 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 13:33:21,494 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 13:33:21,590 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 13:33:21,591 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 13:33:21,597 - WMZC - INFO - 异步任务管理器初始化完成 - 100%异步架构（线程安全）
2025-07-30 13:33:21,600 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 13:33:21,921 - WMZC - INFO - AI助手初始化成功
2025-07-30 13:33:21,922 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 13:33:22,041 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 13:33:22,381 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 13:33:22,385 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 13:33:22,386 - WMZC - INFO - ✅ 日志管理标签页创建完成
2025-07-30 13:33:22,386 - WMZC - INFO - ✅ 日志管理标签页创建完成
2025-07-30 14:16:38,685 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 14:16:38,728 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 14:16:43,230 - lstm_predictor - WARNING - TensorFlow不可用，将使用简化预测模型
2025-07-30 14:24:49,969 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 14:24:49,992 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 14:24:50,015 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 14:24:50,027 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 14:24:50,040 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 14:24:50,062 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 14:24:50,078 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 14:24:50,085 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 14:24:50,093 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 14:24:50,107 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 14:24:51,682 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 14:24:51,734 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 14:24:52,008 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 14:24:52,057 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 14:24:52,058 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 14:24:52,097 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 14:24:52,122 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 14:24:52,157 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 14:24:52,182 - WMZC - INFO - 数据库索引创建完成
2025-07-30 14:24:52,216 - WMZC - INFO - 数据库初始化完成
2025-07-30 14:24:52,249 - WMZC - INFO - 主题设置表创建成功
2025-07-30 14:24:52,285 - WMZC - INFO - 策略配置表创建成功
2025-07-30 14:24:52,335 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 14:24:52,381 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 14:24:52,432 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 14:24:52,481 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 14:24:52,522 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 14:24:52,563 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 14:24:52,964 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 14:24:53,974 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 14:24:54,095 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 14:24:55,268 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 14:24:55,297 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 14:24:55,329 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 14:24:55,625 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 14:24:55,930 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 14:24:55,966 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 14:24:56,029 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 14:24:56,296 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 14:24:56,527 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 14:24:56,599 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 14:24:56,676 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 14:24:56,708 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 14:24:56,792 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 14:24:56,866 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 14:24:56,925 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 14:24:56,996 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 14:24:57,153 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 14:24:57,219 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 14:24:57,305 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 14:24:57,414 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 14:24:57,479 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 14:24:57,501 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 14:24:57,538 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 14:24:57,882 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 14:24:57,943 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 14:24:57,970 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 14:24:58,074 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 14:24:58,642 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 14:24:58,687 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 14:24:58,941 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 14:24:59,001 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 14:24:59,036 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 14:24:59,649 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 14:24:59,792 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 14:24:59,815 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 14:24:59,845 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 14:25:00,270 - WMZC - INFO - AI助手初始化成功
2025-07-30 14:25:00,317 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 14:25:00,445 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 14:25:01,095 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 14:25:01,126 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 14:49:14,570 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 14:49:14,648 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 14:49:14,664 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 14:49:14,672 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 14:49:14,680 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 14:49:14,693 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 14:49:14,701 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 14:49:14,706 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 14:49:14,717 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 14:49:14,726 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 14:49:16,442 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 14:49:16,482 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 14:49:16,751 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 14:49:16,810 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 14:49:16,810 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 14:49:16,828 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 14:49:16,865 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 14:49:16,898 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 14:49:16,933 - WMZC - INFO - 数据库索引创建完成
2025-07-30 14:49:16,991 - WMZC - INFO - 数据库初始化完成
2025-07-30 14:49:17,055 - WMZC - INFO - 主题设置表创建成功
2025-07-30 14:49:17,088 - WMZC - INFO - 策略配置表创建成功
2025-07-30 14:49:17,121 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 14:49:17,166 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 14:49:17,226 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 14:49:17,298 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 14:49:17,423 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 14:49:17,463 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 14:49:17,526 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 14:49:18,703 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 14:49:18,808 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 14:49:20,452 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 14:49:20,480 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 14:49:20,503 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 14:49:20,766 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 14:49:21,062 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 14:49:21,123 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 14:49:21,163 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 14:49:21,484 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 14:49:21,678 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 14:49:21,781 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 14:49:21,810 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 14:49:21,837 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 14:49:21,932 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 14:49:22,000 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 14:49:22,066 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 14:49:22,177 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 14:49:22,620 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 14:49:22,713 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 14:49:22,886 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 14:49:23,077 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 14:49:23,152 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 14:49:23,181 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 14:49:23,204 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 14:49:23,683 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 14:49:23,871 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 14:49:23,939 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 14:49:24,048 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 14:49:24,819 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 14:49:24,895 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 14:49:25,224 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 14:49:25,340 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 14:49:25,419 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 14:49:26,266 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 14:49:26,429 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 14:49:26,461 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 14:49:26,561 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 14:49:27,008 - WMZC - INFO - AI助手初始化成功
2025-07-30 14:49:27,047 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 14:49:27,200 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 14:49:28,471 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 14:49:28,522 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 15:33:57,591 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 15:33:57,649 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 15:33:57,664 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 15:33:57,673 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 15:33:57,683 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 15:33:57,695 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 15:33:57,706 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 15:33:57,713 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 15:33:57,727 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 15:33:57,738 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 15:34:00,202 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 15:34:00,210 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 15:34:00,436 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 15:34:00,449 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 15:34:00,449 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 15:34:00,467 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 15:34:00,496 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 15:34:00,529 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 15:34:00,550 - WMZC - INFO - 数据库索引创建完成
2025-07-30 15:34:00,585 - WMZC - INFO - 数据库初始化完成
2025-07-30 15:34:00,596 - WMZC - INFO - 主题设置表创建成功
2025-07-30 15:34:00,632 - WMZC - INFO - 策略配置表创建成功
2025-07-30 15:34:00,663 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 15:34:00,690 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 15:34:00,714 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 15:34:00,744 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 15:34:00,795 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 15:34:00,857 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 15:34:00,904 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 15:34:02,217 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 15:34:02,348 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 15:34:03,522 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 15:34:03,594 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 15:34:03,619 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 15:34:03,878 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 15:34:04,127 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 15:34:04,156 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 15:34:04,226 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 15:34:04,450 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 15:34:04,689 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 15:34:04,753 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 15:34:04,786 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 15:34:04,813 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 15:34:04,917 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 15:34:04,961 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 15:34:05,007 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 15:34:05,073 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 15:34:05,248 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 15:34:05,314 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 15:34:05,404 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 15:34:05,533 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 15:34:05,578 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 15:34:05,600 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 15:34:05,624 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 15:34:06,025 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 15:34:06,133 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 15:34:06,173 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 15:34:06,273 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 15:34:07,216 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 15:34:07,267 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 15:34:07,539 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 15:34:07,622 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 15:34:07,704 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 15:34:08,223 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 15:34:08,396 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 15:34:08,434 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 15:34:08,464 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 15:34:08,782 - WMZC - INFO - AI助手初始化成功
2025-07-30 15:34:08,820 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 15:34:08,936 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 15:34:09,360 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 15:34:09,401 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 15:55:31,383 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 15:55:31,429 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 15:55:31,444 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 15:55:31,453 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 15:55:31,463 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 15:55:31,477 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 15:55:31,492 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 15:55:31,501 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 15:55:31,523 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 15:55:31,537 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 15:55:34,156 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 15:55:34,165 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 15:55:34,494 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 15:55:34,505 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 15:55:34,505 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 15:55:34,525 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 15:55:34,554 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 15:55:34,656 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 15:55:34,728 - WMZC - INFO - 数据库索引创建完成
2025-07-30 15:55:34,774 - WMZC - INFO - 数据库初始化完成
2025-07-30 15:55:34,814 - WMZC - INFO - 主题设置表创建成功
2025-07-30 15:55:34,844 - WMZC - INFO - 策略配置表创建成功
2025-07-30 15:55:34,926 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 15:55:34,952 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 15:55:34,977 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 15:55:35,006 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 15:55:35,049 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 15:55:35,078 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 15:55:35,130 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 15:55:36,241 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 15:55:36,336 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 15:55:37,053 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 15:55:37,121 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 15:55:37,166 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 15:55:37,488 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 15:55:37,815 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 15:55:37,873 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 15:55:37,926 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 15:55:38,118 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 15:55:38,317 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 15:55:38,395 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 15:55:38,476 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 15:55:38,504 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 15:55:38,627 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 15:55:38,673 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 15:55:38,733 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 15:55:38,827 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 15:55:39,117 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 15:55:39,222 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 15:55:39,360 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 15:55:39,482 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 15:55:39,514 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 15:55:39,535 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 15:55:39,553 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 15:55:39,987 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 15:55:40,100 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 15:55:40,144 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 15:55:40,223 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 15:55:40,754 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 15:55:40,809 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 15:55:41,086 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 15:55:41,155 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 15:55:41,193 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 15:55:41,705 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 15:55:41,859 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 15:55:41,917 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 15:55:41,954 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 15:55:42,303 - WMZC - INFO - AI助手初始化成功
2025-07-30 15:55:42,337 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 15:55:42,482 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 15:55:43,026 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 15:55:43,046 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 16:13:04,185 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 16:13:04,200 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 16:13:04,216 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 16:13:04,231 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 16:13:04,231 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 16:13:04,231 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 16:13:04,248 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 16:13:04,248 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 16:13:04,263 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 16:13:04,263 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 16:13:05,826 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 16:13:05,888 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 16:13:06,091 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 16:13:06,138 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 16:13:06,138 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 16:13:06,153 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 16:13:06,185 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 16:13:06,224 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 16:13:06,264 - WMZC - INFO - 数据库索引创建完成
2025-07-30 16:13:06,292 - WMZC - INFO - 数据库初始化完成
2025-07-30 16:13:06,313 - WMZC - INFO - 主题设置表创建成功
2025-07-30 16:13:06,344 - WMZC - INFO - 策略配置表创建成功
2025-07-30 16:13:06,406 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 16:13:06,469 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 16:13:06,498 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:13:06,529 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:13:06,591 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 16:13:06,624 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 16:13:06,687 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 16:13:07,734 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 16:13:07,810 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 16:13:08,716 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:13:08,747 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 16:13:08,780 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:13:09,044 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 16:13:09,247 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 16:13:09,278 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 16:13:09,356 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 16:13:09,513 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 16:13:09,669 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 16:13:09,731 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 16:13:09,763 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 16:13:09,778 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 16:13:09,856 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 16:13:09,872 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 16:13:09,920 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 16:13:10,032 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 16:13:10,247 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 16:13:10,310 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 16:13:10,419 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 16:13:10,559 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 16:13:10,622 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 16:13:10,638 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 16:13:10,653 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 16:13:10,950 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 16:13:11,013 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 16:13:11,029 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 16:13:11,107 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 16:13:11,528 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 16:13:11,560 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 16:13:11,794 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 16:13:11,906 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 16:13:11,934 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 16:13:12,374 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 16:13:12,481 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 16:13:12,528 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 16:13:12,592 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 16:13:12,984 - WMZC - INFO - AI助手初始化成功
2025-07-30 16:13:13,029 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 16:13:13,154 - WMZC - INFO - 异步任务管理器初始化完成 - 100%异步架构（线程安全）
2025-07-30 16:13:13,218 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 16:13:13,748 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 16:13:13,763 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 16:25:18,050 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 16:25:18,078 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 16:25:18,091 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 16:25:18,101 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 16:25:18,112 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 16:25:18,125 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 16:25:18,133 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 16:25:18,151 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 16:25:18,162 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 16:25:18,172 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 16:25:19,860 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 16:25:19,867 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 16:25:20,029 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 16:25:20,083 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 16:25:20,083 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 16:25:20,101 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 16:25:20,127 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 16:25:20,175 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 16:25:20,241 - WMZC - INFO - 数据库索引创建完成
2025-07-30 16:25:20,266 - WMZC - INFO - 数据库初始化完成
2025-07-30 16:25:20,290 - WMZC - INFO - 主题设置表创建成功
2025-07-30 16:25:20,312 - WMZC - INFO - 策略配置表创建成功
2025-07-30 16:25:20,337 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 16:25:20,359 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 16:25:20,383 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:25:20,416 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:25:20,449 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 16:25:20,476 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 16:25:20,517 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 16:25:21,469 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 16:25:21,528 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 16:25:22,504 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:25:22,562 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 16:25:22,590 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:25:22,884 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 16:25:23,190 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 16:25:23,221 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 16:25:23,290 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 16:25:23,638 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 16:25:23,865 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 16:25:23,944 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 16:25:24,059 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 16:25:24,091 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 16:25:24,201 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 16:25:24,250 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 16:25:24,313 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 16:25:24,453 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 16:25:24,722 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 16:25:24,815 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 16:25:24,962 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 16:25:25,052 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 16:25:25,106 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 16:25:25,127 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 16:25:25,151 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 16:25:25,472 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 16:25:25,537 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 16:25:25,555 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 16:25:25,613 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 16:25:26,055 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 16:25:26,107 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 16:25:26,358 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 16:25:26,441 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 16:25:26,540 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 16:25:27,199 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 16:25:27,325 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 16:25:27,351 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 16:25:27,389 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 16:25:27,928 - WMZC - INFO - AI助手初始化成功
2025-07-30 16:25:27,969 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 16:25:28,136 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 16:25:28,845 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 16:25:28,873 - WMZC - INFO - 日志管理器异步初始化已启动
2025-07-30 16:26:43,861 - WMZC - INFO - SafeFormatter日志系统初始化完成
2025-07-30 16:26:43,901 - WMZC - INFO - 性能监控已启动（同步模式）
2025-07-30 16:26:43,917 - WMZC - INFO - === WMZC系统模块导入状态 ===
2025-07-30 16:26:43,931 - WMZC - INFO - 所有核心依赖已满足
2025-07-30 16:26:43,931 - WMZC - WARNING - ⚠️ 传统金融市场数据提供者 - 不可用 (传统金融市场功能将不可用)
2025-07-30 16:26:43,949 - WMZC - INFO - ✅ 微信推送通知管理器 - 可用
2025-07-30 16:26:43,949 - WMZC - INFO - ✅ 等量加仓管理器 - 可用
2025-07-30 16:26:43,993 - WMZC - INFO - === 模块导入状态检查完成 ===
2025-07-30 16:26:43,993 - WMZC - INFO - ✅ 配置已从 wmzc_config.json 加载
2025-07-30 16:26:44,015 - WMZC - INFO - ✅ JSON配置管理器已初始化
2025-07-30 16:26:46,392 - WMZC - INFO - ✅ 所有必需模块检查通过
2025-07-30 16:26:46,441 - WMZC - INFO - 可选模块未安装: 1个
2025-07-30 16:26:46,720 - DatabasePool - INFO - 数据库连接池初始化完成，初始连接数: 2
2025-07-30 16:26:46,720 - DatabasePool - INFO - 连接池统计 - 总连接: 2, 活跃: 0, 空闲: 2, 命中率: 0.0%, 总请求: 0, 错误: 0
2025-07-30 16:26:46,720 - DatabasePool - INFO - 连接池监控线程已启动
2025-07-30 16:26:46,739 - WMZC - INFO - 数据库连接池初始化成功
2025-07-30 16:26:46,781 - WMZC - INFO - 内存缓存管理器初始化完成 - 支持LRU和分层缓存
2025-07-30 16:26:46,812 - WMZC - INFO - 内存缓存初始化成功
2025-07-30 16:26:46,844 - WMZC - INFO - 数据库索引创建完成
2025-07-30 16:26:46,863 - WMZC - INFO - 数据库初始化完成
2025-07-30 16:26:46,907 - WMZC - INFO - 主题设置表创建成功
2025-07-30 16:26:46,939 - WMZC - INFO - 策略配置表创建成功
2025-07-30 16:26:46,975 - WMZC - INFO - 用户偏好设置表创建成功
2025-07-30 16:26:47,003 - WMZC - INFO - 所有数据库表创建完成
2025-07-30 16:26:47,030 - WMZC - INFO - 策略已注册: Professional RSI Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:26:47,078 - WMZC - INFO - 策略已注册: Advanced MACD Strategy, 激活状态: False, 权重: 1.0
2025-07-30 16:26:47,113 - WMZC - INFO - 系统设置变量初始化完成
2025-07-30 16:26:47,156 - WMZC - INFO - 🚀 开始创建GUI界面...
2025-07-30 16:26:47,283 - WMZC - INFO - ✅ 菜单栏创建完成
2025-07-30 16:26:48,406 - WMZC - INFO - 交易控制按钮创建完成
2025-07-30 16:26:48,469 - WMZC - INFO - ✅ 状态栏创建完成
2025-07-30 16:26:49,673 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:26:49,722 - WMZC - INFO - ✅ 已自动加载 OKX 的API凭证
2025-07-30 16:26:49,749 - WMZC - INFO - ✅ OKX API凭证已从JSON文件加载
2025-07-30 16:26:50,017 - WMZC - INFO - ✅ 主配置标签页创建完成
2025-07-30 16:26:50,282 - WMZC - INFO - 未找到保存的主题设置，使用默认主题
2025-07-30 16:26:50,327 - WMZC - INFO - ✅ GUI界面基础结构创建完成
2025-07-30 16:26:50,390 - WMZC - INFO - 🔄 开始创建RSI策略标签页...
2025-07-30 16:26:50,712 - WMZC - INFO - 当前选中的RSI策略类型: ['区间策略']
2025-07-30 16:26:50,957 - WMZC - INFO - ✅ RSI策略标签页创建完成
2025-07-30 16:26:51,074 - WMZC - INFO - 🔄 开始延迟初始化阶段1...
2025-07-30 16:26:51,113 - WMZC - INFO - ✅ 延迟初始化阶段1完成
2025-07-30 16:26:51,146 - WMZC - INFO - 🔄 开始创建MACD策略标签页...
2025-07-30 16:26:51,267 - WMZC - INFO - 当前选中的MACD策略类型: ['交叉策略']
2025-07-30 16:26:51,298 - WMZC - INFO - 主策略类型设置为: 交叉策略
2025-07-30 16:26:51,378 - WMZC - INFO - ✅ MACD策略标签页创建完成
2025-07-30 16:26:51,472 - WMZC - INFO - 🔄 开始创建订单管理标签页...
2025-07-30 16:26:51,810 - WMZC - INFO - 订单操作控制区域创建完成
2025-07-30 16:26:51,935 - WMZC - INFO - 活跃订单监控面板创建完成
2025-07-30 16:26:52,094 - WMZC - INFO - 订单历史记录区域创建完成
2025-07-30 16:26:52,233 - WMZC - INFO - 订单统计面板创建完成
2025-07-30 16:26:52,255 - WMZC - INFO - 订单管理变量初始化完成
2025-07-30 16:26:52,281 - WMZC - INFO - 订单管理标签页创建完成
2025-07-30 16:26:52,308 - WMZC - INFO - ✅ 订单管理标签页创建完成
2025-07-30 16:26:52,792 - WMZC - INFO - 🔄 开始创建策略赶集标签页...
2025-07-30 16:26:52,901 - WMZC - INFO - 获取到策略信息数量: 18
2025-07-30 16:26:52,966 - WMZC - INFO - 策略名称列表: ['专业RSI策略', '高级MACD策略', '布林带策略', 'KDJ随机指标策略', '均线交叉策略', '量价趋势策略', '动量策略', '均值回归策略', '突破策略', '网格交易策略', '套利策略', '剥头皮策略', '波段交易策略', '配对交易策略', '新闻情绪策略', '机器学习策略', '期权策略', '加密货币动量策略']
2025-07-30 16:26:53,104 - WMZC - INFO - 策略复选框已创建，共18个策略
2025-07-30 16:26:53,655 - WMZC - INFO - ✅ 策略赶集标签页创建完成
2025-07-30 16:26:53,713 - WMZC - INFO - 🔄 开始创建等量加仓标签页...
2025-07-30 16:26:54,025 - WMZC - INFO - 获取等量加仓策略信息: 18个策略
2025-07-30 16:26:54,088 - WMZC - INFO - 等量加仓标签页成功加载 18 个策略
2025-07-30 16:26:54,145 - WMZC - INFO - 未找到等量加仓策略选择配置，使用默认设置
2025-07-30 16:26:54,744 - WMZC - INFO - ✅ 等量加仓标签页创建完成
2025-07-30 16:26:54,884 - WMZC - INFO - 🔄 开始延迟初始化阶段2...
2025-07-30 16:26:54,946 - WMZC - INFO - ✅ 延迟初始化阶段2完成
2025-07-30 16:26:54,978 - WMZC - INFO - 🔄 开始创建AI助手标签页...
2025-07-30 16:26:55,321 - WMZC - INFO - AI助手初始化成功
2025-07-30 16:26:55,400 - WMZC - INFO - ✅ AI助手标签页创建完成
2025-07-30 16:26:55,494 - WMZC - INFO - 🔄 开始创建日志管理标签页...
2025-07-30 16:26:55,853 - WMZC - INFO - 开始异步初始化日志管理器...
2025-07-30 16:26:55,868 - WMZC - INFO - 日志管理器异步初始化已启动
