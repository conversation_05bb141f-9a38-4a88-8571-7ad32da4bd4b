#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志界面优化效果
"""

import tkinter as tk
from tkinter import ttk
import logging
from datetime import datetime
import json

def test_log_translations():
    """测试日志翻译功能"""
    print("🔍 测试日志翻译功能...")
    
    try:
        with open('log_translations.json', 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        print("✅ 日志翻译配置加载成功")
        
        # 测试技术术语翻译
        tech_terms = translations['technical_terms']
        print(f"📝 技术术语翻译数量: {len(tech_terms)}")
        
        # 测试状态码翻译
        status_codes = translations['status_codes']
        print(f"📊 状态码翻译数量: {len(status_codes)}")
        
        # 测试日志级别配置
        log_levels = translations['log_levels']
        print(f"🏷️ 日志级别配置数量: {len(log_levels)}")
        
        # 测试模块翻译
        modules = translations['modules']
        print(f"🔧 模块翻译数量: {len(modules)}")
        
        # 测试快速过滤配置
        quick_filters = translations['quick_filters']
        print(f"🔍 快速过滤配置数量: {len(quick_filters)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志翻译配置测试失败: {e}")
        return False

def create_demo_log_interface():
    """创建演示日志界面"""
    print("🎨 创建演示日志界面...")
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("📋 用户友好日志界面演示")
        root.geometry("1000x700")
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建新手模式切换
        mode_frame = ttk.LabelFrame(left_frame, text="🔰 显示模式")
        mode_frame.pack(fill=tk.X, pady=5)
        
        beginner_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(mode_frame, text="新手模式 (隐藏技术细节)", 
                       variable=beginner_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # 创建快速过滤
        filter_frame = ttk.LabelFrame(left_frame, text="🔍 快速查看")
        filter_frame.pack(fill=tk.X, pady=5)
        
        quick_buttons = [
            ("🟢 正常信息", "normal"),
            ("🟡 重要提醒", "important"), 
            ("🔴 错误问题", "errors"),
            ("💰 交易相关", "trading"),
            ("📊 数据获取", "data"),
            ("📈 查看全部", "all")
        ]
        
        for text, filter_type in quick_buttons:
            ttk.Button(filter_frame, text=text, width=15).pack(pady=2, padx=5)
        
        # 创建详细筛选
        detail_frame = ttk.LabelFrame(left_frame, text="🔧 详细筛选")
        detail_frame.pack(fill=tk.X, pady=5)
        
        # 重要程度
        ttk.Label(detail_frame, text="重要程度:").pack(anchor=tk.W, padx=5)
        level_var = tk.StringVar(value="全部")
        level_combo = ttk.Combobox(detail_frame, textvariable=level_var,
                                  values=["全部", "🟢 正常", "🟡 提醒", "🟠 警告", "🔴 错误", "🚨 严重"],
                                  state="readonly", width=18)
        level_combo.pack(padx=5, pady=2)
        
        # 时间范围
        ttk.Label(detail_frame, text="查看时间:").pack(anchor=tk.W, padx=5)
        time_var = tk.StringVar(value="最近1天")
        time_combo = ttk.Combobox(detail_frame, textvariable=time_var,
                                 values=["最近1小时", "最近6小时", "最近1天", "最近7天", "最近30天", "全部时间"],
                                 state="readonly", width=18)
        time_combo.pack(padx=5, pady=2)
        
        # 搜索框
        ttk.Label(detail_frame, text="搜索内容:").pack(anchor=tk.W, padx=5)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(detail_frame, textvariable=search_var, width=20)
        search_entry.pack(padx=5, pady=2)
        
        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="🔍 查看").pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="🔄 刷新").pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="🧹 清空筛选").pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="❓ 帮助说明").pack(fill=tk.X, pady=2)
        
        # 右侧日志显示区域
        display_frame = ttk.LabelFrame(right_frame, text="📋 系统运行记录")
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 帮助说明
        help_text = "💡 绿色=正常运行，黄色=需要注意，红色=出现问题。双击查看详情"
        ttk.Label(display_frame, text=help_text, font=("Arial", 9), 
                 foreground="gray").pack(anchor=tk.W, padx=5, pady=2)
        
        # 创建表格
        columns = ("时间", "状态", "说明", "详细信息")
        tree = ttk.Treeview(display_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        column_config = {
            "时间": {"text": "⏰ 时间", "width": 120},
            "状态": {"text": "📊 状态", "width": 100},
            "说明": {"text": "📝 说明", "width": 150},
            "详细信息": {"text": "💬 详细信息", "width": 400}
        }
        
        for col in columns:
            config = column_config[col]
            tree.heading(col, text=config["text"])
            tree.column(col, width=config["width"])
        
        # 配置颜色标签
        tree.tag_configure("normal", foreground="green")
        tree.tag_configure("warning", foreground="orange")
        tree.tag_configure("error", foreground="red")
        tree.tag_configure("info", foreground="blue")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 添加示例数据
        sample_data = [
            ("13:35:00", "🟢 正常", "主系统", "✅ 成功连接到交易所服务器", "normal"),
            ("13:35:05", "🟢 正常", "数据获取", "📊 成功获取BTC价格数据: $117,944", "normal"),
            ("13:35:10", "🟡 提醒", "策略引擎", "🎯 RSI策略检测到买入信号", "warning"),
            ("13:35:15", "🟢 正常", "订单管理", "📝 交易订单已提交 (买入 0.001 BTC)", "normal"),
            ("13:35:20", "🟢 正常", "订单管理", "✅ 交易订单执行成功", "normal"),
            ("13:35:25", "🔴 错误", "网络连接", "❌ 网络请求超时，正在重试...", "error"),
            ("13:35:30", "🟢 正常", "网络连接", "✅ 网络连接已恢复", "normal"),
            ("13:35:35", "🟡 提醒", "风险控制", "⚠️ 当前持仓接近风险限额", "warning"),
        ]
        
        for data in sample_data:
            time_str, status, module, message, tag = data
            item = tree.insert("", tk.END, values=(time_str, status, module, message))
            tree.item(item, tags=(tag,))
        
        # 控制按钮
        control_frame = ttk.Frame(display_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="💾 保存记录").pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="🧹 清空显示").pack(side=tk.LEFT, padx=5)
        
        auto_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="🔄 自动更新", 
                       variable=auto_var).pack(side=tk.LEFT, padx=10)
        
        pause_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(control_frame, text="⏸️ 暂停", 
                       variable=pause_var).pack(side=tk.LEFT, padx=5)
        
        print("✅ 演示界面创建成功")
        print("💡 请查看演示窗口，体验用户友好的日志界面")
        
        # 运行界面
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建演示界面失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试日志界面优化...")
    print("=" * 50)
    
    # 测试翻译配置
    if test_log_translations():
        print("✅ 翻译配置测试通过")
    else:
        print("❌ 翻译配置测试失败")
        return
    
    print("-" * 50)
    
    # 创建演示界面
    if create_demo_log_interface():
        print("✅ 演示界面测试完成")
    else:
        print("❌ 演示界面测试失败")
    
    print("=" * 50)
    print("🎉 日志界面优化测试完成")

if __name__ == "__main__":
    main()
