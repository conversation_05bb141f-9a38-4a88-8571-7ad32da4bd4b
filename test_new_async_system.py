#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的异步事件循环管理系统
"""

import asyncio
import threading
import time
import aiohttp
import ssl
import certifi

# 模拟新的AsyncLoopManager
class TestAsyncLoopManager:
    """测试版本的异步事件循环管理器"""
    _instance = None
    _loop = None
    _thread = None
    _initialized = False
    _shutdown = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize_background_loop()
        return cls._instance

    def _initialize_background_loop(self):
        """在后台线程中初始化事件循环"""
        import queue
        
        self._task_queue = queue.Queue()
        self._result_futures = {}
        
        def run_background_loop():
            """后台线程运行事件循环"""
            try:
                # 创建新的事件循环
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
                
                # 启动任务处理协程
                self._loop.create_task(self._process_tasks())
                
                # 运行事件循环直到被停止
                self._loop.run_forever()
                
            except Exception as e:
                print(f"后台事件循环异常: {e}")
            finally:
                if self._loop and not self._loop.is_closed():
                    self._loop.close()
        
        self._thread = threading.Thread(target=run_background_loop, daemon=True)
        self._thread.start()
        
        # 等待事件循环启动
        for _ in range(50):  # 最多等待5秒
            if self._loop and self._loop.is_running():
                break
            time.sleep(0.1)
        
        self._initialized = True

    async def _process_tasks(self):
        """处理任务队列"""
        while not self._shutdown:
            try:
                # 检查是否有新任务
                if not self._task_queue.empty():
                    task_id, coro = self._task_queue.get_nowait()
                    try:
                        result = await coro
                        if task_id in self._result_futures:
                            self._result_futures[task_id].set_result(result)
                    except Exception as e:
                        if task_id in self._result_futures:
                            self._result_futures[task_id].set_exception(e)
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(0.01)
                
            except Exception as e:
                print(f"任务处理异常: {e}")
                await asyncio.sleep(0.1)

    def run_async_safely(self, coro):
        """安全运行异步协程"""
        try:
            # 检查协程是否有效
            if not asyncio.iscoroutine(coro):
                print(f"传入的不是协程对象: {type(coro)}")
                return None

            # 检查后台事件循环是否可用
            if not self._initialized or not self._loop or self._loop.is_closed():
                print("后台事件循环不可用，重新初始化")
                self._initialize_background_loop()
            
            # 使用后台事件循环执行协程
            import uuid
            import concurrent.futures
            
            task_id = str(uuid.uuid4())
            future = concurrent.futures.Future()
            self._result_futures[task_id] = future
            
            # 将任务添加到队列
            self._task_queue.put((task_id, coro))
            
            # 等待结果（设置超时避免无限等待）
            try:
                result = future.result(timeout=30)  # 30秒超时
                return result
            except concurrent.futures.TimeoutError:
                print("异步任务执行超时")
                return None
            finally:
                # 清理
                if task_id in self._result_futures:
                    del self._result_futures[task_id]

        except Exception as e:
            print(f"异步执行异常: {e}")
            return None

def test_background_event_loop():
    """测试后台事件循环"""
    print("🔍 测试后台事件循环...")
    
    try:
        # 创建管理器实例
        manager = TestAsyncLoopManager()
        
        # 测试异步任务
        async def test_task():
            await asyncio.sleep(0.1)
            return "后台任务完成"
        
        # 执行任务
        result = manager.run_async_safely(test_task())
        
        if result == "后台任务完成":
            print("   ✅ 后台事件循环测试通过")
            return True
        else:
            print(f"   ❌ 后台事件循环测试失败: {result}")
            return False
            
    except Exception as e:
        print(f"   ❌ 后台事件循环测试异常: {e}")
        return False

def test_http_session_management():
    """测试HTTP会话管理"""
    print("\n🔍 测试HTTP会话管理...")
    
    try:
        manager = TestAsyncLoopManager()
        
        async def test_http_request():
            """测试HTTP请求"""
            try:
                # 创建HTTP会话
                timeout = aiohttp.ClientTimeout(total=10)
                connector = aiohttp.TCPConnector(ssl=ssl.create_default_context(cafile=certifi.where()))
                
                async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                    # 测试OKX API
                    async with session.get("https://www.okx.com/api/v5/public/time") as response:
                        if response.status == 200:
                            data = await response.json()
                            return f"HTTP请求成功: {data}"
                        else:
                            return f"HTTP请求失败: {response.status}"
                            
            except Exception as e:
                return f"HTTP请求异常: {e}"
        
        # 执行HTTP请求
        result = manager.run_async_safely(test_http_request())
        
        if result and "HTTP请求成功" in str(result):
            print("   ✅ HTTP会话管理测试通过")
            return True
        else:
            print(f"   ❌ HTTP会话管理测试失败: {result}")
            return False
            
    except Exception as e:
        print(f"   ❌ HTTP会话管理测试异常: {e}")
        return False

def test_concurrent_tasks():
    """测试并发任务处理"""
    print("\n🔍 测试并发任务处理...")
    
    try:
        manager = TestAsyncLoopManager()
        
        async def worker_task(worker_id):
            await asyncio.sleep(0.1)
            return f"Worker {worker_id} 完成"
        
        # 并发执行多个任务
        results = []
        import concurrent.futures
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = []
            for i in range(3):
                future = executor.submit(manager.run_async_safely, worker_task(i))
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures, timeout=10):
                result = future.result()
                results.append(result)
        
        print(f"   并发任务结果: {results}")
        
        if len(results) == 3 and all("完成" in str(r) for r in results):
            print("   ✅ 并发任务处理测试通过")
            return True
        else:
            print("   ❌ 并发任务处理测试失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 并发任务处理测试异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    try:
        manager = TestAsyncLoopManager()
        
        async def error_task():
            await asyncio.sleep(0.1)
            raise ValueError("测试错误")
        
        # 执行会出错的任务
        result = manager.run_async_safely(error_task())
        
        # 应该返回None（因为出错了）
        if result is None:
            print("   ✅ 错误处理测试通过")
            return True
        else:
            print(f"   ❌ 错误处理测试失败: {result}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误处理测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 新异步事件循环管理系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 后台事件循环
    result1 = test_background_event_loop()
    test_results.append(("后台事件循环", result1))
    
    # 测试2: HTTP会话管理
    result2 = test_http_session_management()
    test_results.append(("HTTP会话管理", result2))
    
    # 测试3: 并发任务处理
    result3 = test_concurrent_tasks()
    test_results.append(("并发任务处理", result3))
    
    # 测试4: 错误处理
    result4 = test_error_handling()
    test_results.append(("错误处理", result4))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 所有新异步系统测试通过！")
        print("\n✅ 新系统特性:")
        print("• 专用后台事件循环线程")
        print("• 安全的HTTP会话管理")
        print("• 可靠的并发任务处理")
        print("• 完善的错误处理机制")
        print("\n🔧 预期解决的问题:")
        print("• 彻底消除 'Event loop is closed' 错误")
        print("• 解决 Tkinter 与 asyncio 的冲突")
        print("• 确保 aiohttp session 在正确的事件循环中运行")
        print("• 提供稳定的异步任务执行环境")
    else:
        print("❌ 部分新异步系统测试失败")
        print("\n🔧 建议:")
        print("• 检查网络连接")
        print("• 检查依赖库版本")
        print("• 重新启动测试")
    
    return all_passed

if __name__ == "__main__":
    main()
