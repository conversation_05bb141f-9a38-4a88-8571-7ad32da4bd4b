#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - Pin Bar策略实现
Pin Bar（针形K线）形态识别和交易策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

class PinBarStrategy:
    """Pin Bar策略类 - 针形K线形态识别和交易策略"""
    
    def __init__(self, 
                 min_shadow_ratio: float = 2.0,
                 max_body_ratio: float = 0.3,
                 confirmation_candles: int = 1,
                 stop_loss_ratio: float = 2.0,
                 take_profit_ratio: float = 3.0,
                 timeframe: str = "15m"):
        """
        初始化Pin Bar策略
        
        Args:
            min_shadow_ratio: 最小影线比例（影线/实体）
            max_body_ratio: 最大实体比例（实体/整根K线）
            confirmation_candles: 确认K线数量
            stop_loss_ratio: 止损比例
            take_profit_ratio: 止盈比例
            timeframe: 时间框架
        """
        self.min_shadow_ratio = min_shadow_ratio
        self.max_body_ratio = max_body_ratio
        self.confirmation_candles = confirmation_candles
        self.stop_loss_ratio = stop_loss_ratio
        self.take_profit_ratio = take_profit_ratio
        self.timeframe = timeframe
        
        self.logger = logging.getLogger(__name__)
        self.signals = []
        self.pin_bars = []
        
    def identify_pin_bar(self, candle: Dict) -> Optional[Dict]:
        """
        识别Pin Bar形态
        
        Args:
            candle: K线数据 {'open', 'high', 'low', 'close', 'volume', 'timestamp'}
            
        Returns:
            Pin Bar信息字典或None
        """
        try:
            open_price = float(candle['open'])
            high_price = float(candle['high'])
            low_price = float(candle['low'])
            close_price = float(candle['close'])
            
            # 计算实体和影线
            body_size = abs(close_price - open_price)
            upper_shadow = high_price - max(open_price, close_price)
            lower_shadow = min(open_price, close_price) - low_price
            total_range = high_price - low_price
            
            # 避免除零错误
            if total_range == 0 or body_size == 0:
                return None
            
            # 计算比例
            body_ratio = body_size / total_range
            upper_shadow_ratio = upper_shadow / body_size if body_size > 0 else 0
            lower_shadow_ratio = lower_shadow / body_size if body_size > 0 else 0
            
            # Pin Bar识别条件
            pin_bar_info = None
            
            # 看涨Pin Bar（长下影线）
            if (lower_shadow_ratio >= self.min_shadow_ratio and 
                body_ratio <= self.max_body_ratio and
                upper_shadow <= body_size):
                
                strength = self.calculate_pin_bar_strength(lower_shadow_ratio, body_ratio)
                pin_bar_info = {
                    'type': 'bullish',
                    'strength': strength,
                    'shadow_ratio': lower_shadow_ratio,
                    'body_ratio': body_ratio,
                    'entry_price': high_price,
                    'stop_loss': low_price,
                    'take_profit': high_price + (high_price - low_price) * self.take_profit_ratio,
                    'timestamp': candle.get('timestamp', datetime.now()),
                    'candle_data': candle
                }
            
            # 看跌Pin Bar（长上影线）
            elif (upper_shadow_ratio >= self.min_shadow_ratio and 
                  body_ratio <= self.max_body_ratio and
                  lower_shadow <= body_size):
                
                strength = self.calculate_pin_bar_strength(upper_shadow_ratio, body_ratio)
                pin_bar_info = {
                    'type': 'bearish',
                    'strength': strength,
                    'shadow_ratio': upper_shadow_ratio,
                    'body_ratio': body_ratio,
                    'entry_price': low_price,
                    'stop_loss': high_price,
                    'take_profit': low_price - (high_price - low_price) * self.take_profit_ratio,
                    'timestamp': candle.get('timestamp', datetime.now()),
                    'candle_data': candle
                }
            
            return pin_bar_info
            
        except Exception as e:
            self.logger.error(f"Pin Bar识别失败: {e}")
            return None
    
    def calculate_pin_bar_strength(self, shadow_ratio: float, body_ratio: float) -> str:
        """
        计算Pin Bar强度
        
        Args:
            shadow_ratio: 影线比例
            body_ratio: 实体比例
            
        Returns:
            强度等级：'weak', 'medium', 'strong'
        """
        # 综合评分
        score = shadow_ratio * 2 - body_ratio * 5
        
        if score >= 6:
            return 'strong'
        elif score >= 4:
            return 'medium'
        else:
            return 'weak'
    
    def analyze_market_data(self, data: pd.DataFrame) -> List[Dict]:
        """
        分析市场数据，识别Pin Bar形态
        
        Args:
            data: 市场数据DataFrame
            
        Returns:
            Pin Bar信号列表
        """
        try:
            pin_bars = []
            
            if len(data) < 2:
                return pin_bars
            
            for i in range(1, len(data)):
                candle = {
                    'open': data.iloc[i]['open'],
                    'high': data.iloc[i]['high'],
                    'low': data.iloc[i]['low'],
                    'close': data.iloc[i]['close'],
                    'volume': data.iloc[i].get('volume', 0),
                    'timestamp': data.index[i] if hasattr(data.index[i], 'strftime') else datetime.now()
                }
                
                pin_bar = self.identify_pin_bar(candle)
                if pin_bar:
                    # 添加确认逻辑
                    if self.confirm_pin_bar_signal(data, i, pin_bar):
                        pin_bars.append(pin_bar)
            
            self.pin_bars = pin_bars
            return pin_bars
            
        except Exception as e:
            self.logger.error(f"市场数据分析失败: {e}")
            return []
    
    def confirm_pin_bar_signal(self, data: pd.DataFrame, pin_bar_index: int, pin_bar: Dict) -> bool:
        """
        确认Pin Bar信号
        
        Args:
            data: 市场数据
            pin_bar_index: Pin Bar位置
            pin_bar: Pin Bar信息
            
        Returns:
            是否确认信号
        """
        try:
            # 检查是否有足够的确认K线
            if pin_bar_index + self.confirmation_candles >= len(data):
                return False
            
            # 看涨Pin Bar确认
            if pin_bar['type'] == 'bullish':
                for i in range(1, self.confirmation_candles + 1):
                    confirm_candle = data.iloc[pin_bar_index + i]
                    if confirm_candle['close'] <= pin_bar['entry_price']:
                        return False
                return True
            
            # 看跌Pin Bar确认
            elif pin_bar['type'] == 'bearish':
                for i in range(1, self.confirmation_candles + 1):
                    confirm_candle = data.iloc[pin_bar_index + i]
                    if confirm_candle['close'] >= pin_bar['entry_price']:
                        return False
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Pin Bar信号确认失败: {e}")
            return False
    
    def generate_trading_signal(self, pin_bar: Dict) -> Dict:
        """
        生成交易信号
        
        Args:
            pin_bar: Pin Bar信息
            
        Returns:
            交易信号
        """
        signal_strength = {
            'weak': 0.3,
            'medium': 0.6,
            'strong': 0.9
        }.get(pin_bar['strength'], 0.5)
        
        return {
            'type': 'BUY' if pin_bar['type'] == 'bullish' else 'SELL',
            'strength': signal_strength,
            'entry_price': pin_bar['entry_price'],
            'stop_loss': pin_bar['stop_loss'],
            'take_profit': pin_bar['take_profit'],
            'timestamp': pin_bar['timestamp'],
            'reason': f"Pin Bar {pin_bar['type']} signal - {pin_bar['strength']} strength",
            'strategy': 'Pin Bar',
            'timeframe': self.timeframe,
            'pin_bar_data': pin_bar
        }
    
    def get_parameters(self) -> Dict:
        """获取策略参数"""
        return {
            'min_shadow_ratio': self.min_shadow_ratio,
            'max_body_ratio': self.max_body_ratio,
            'confirmation_candles': self.confirmation_candles,
            'stop_loss_ratio': self.stop_loss_ratio,
            'take_profit_ratio': self.take_profit_ratio,
            'timeframe': self.timeframe
        }
    
    def set_parameters(self, params: Dict):
        """设置策略参数"""
        self.min_shadow_ratio = params.get('min_shadow_ratio', self.min_shadow_ratio)
        self.max_body_ratio = params.get('max_body_ratio', self.max_body_ratio)
        self.confirmation_candles = params.get('confirmation_candles', self.confirmation_candles)
        self.stop_loss_ratio = params.get('stop_loss_ratio', self.stop_loss_ratio)
        self.take_profit_ratio = params.get('take_profit_ratio', self.take_profit_ratio)
        self.timeframe = params.get('timeframe', self.timeframe)
    
    def get_strategy_info(self) -> Dict:
        """获取策略信息"""
        return {
            'name': 'Pin Bar策略',
            'type': '价格行为分析策略',
            'risk_level': 'medium',
            'win_rate': 68,
            'description': 'Pin Bar（针形K线）形态识别策略，通过识别长影线短实体的K线形态来捕捉反转信号',
            'market_environment': '趋势反转点、支撑阻力位附近',
            'recommended_scenario': '关键价位的反转信号确认',
            'timeframes': ['1m', '5m', '15m', '1h', '1d'],
            'parameters': self.get_parameters()
        }
    
    def calculate_statistics(self) -> Dict:
        """计算策略统计信息"""
        if not self.pin_bars:
            return {
                'total_signals': 0,
                'bullish_signals': 0,
                'bearish_signals': 0,
                'strong_signals': 0,
                'medium_signals': 0,
                'weak_signals': 0
            }
        
        stats = {
            'total_signals': len(self.pin_bars),
            'bullish_signals': len([pb for pb in self.pin_bars if pb['type'] == 'bullish']),
            'bearish_signals': len([pb for pb in self.pin_bars if pb['type'] == 'bearish']),
            'strong_signals': len([pb for pb in self.pin_bars if pb['strength'] == 'strong']),
            'medium_signals': len([pb for pb in self.pin_bars if pb['strength'] == 'medium']),
            'weak_signals': len([pb for pb in self.pin_bars if pb['strength'] == 'weak'])
        }
        
        return stats
