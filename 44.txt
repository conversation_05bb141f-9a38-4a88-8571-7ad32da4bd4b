# WMZC量化交易系统 - 完整依赖清单
# 生成时间: 2024-12-19
# Python版本要求: Python 3.8+

# ==================== 核心必需依赖 ====================
aiohttp>=3.8.0
pandas>=1.5.0
numpy>=1.21.0
websockets>=10.0
certifi>=2022.12.7
psutil>=5.9.0
aiosqlite>=0.17.0

# ==================== 安装命令 ====================
# 基础安装：
# pip install aiohttp pandas numpy websockets certifi psutil aiosqlite

# 完整安装（包含可选功能）：
# pip install aiohttp pandas numpy websockets certifi psutil aiosqlite tensorflow scikit-learn matplotlib

# ==================== 系统要求 ====================
# Python 3.8+
# Windows 10/11, macOS 10.15+, Ubuntu 18.04+
# 4GB+ RAM (推荐8GB)
# 稳定的网络连接

# ==================== 可选依赖 ====================
# tensorflow>=2.10.0  # LSTM深度学习
# scikit-learn>=1.1.0  # 机器学习
# matplotlib>=3.5.0   # 数据可视化
# redis>=4.3.0        # 分布式缓存
# python-consul>=1.1.0 # 服务发现

# ==================== 验证安装 ====================
# 运行以下代码验证：
# import aiohttp, pandas, numpy, websockets, certifi, psutil
# print("✅ 核心依赖安装成功！")

# ==================== 故障排除 ====================
# 1. 升级pip: python -m pip install --upgrade pip
# 2. 使用镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/
# 3. Linux安装tkinter: sudo apt-get install python3-tk
# 4. 使用虚拟环境: python -m venv wmzc_env

# 系统版本：WMZC v1.0.0
# 架构：100%异步 + 响应式GUI + 交易所分离
