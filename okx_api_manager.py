#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX API管理器 - 全面集成OKX交易所API
支持现货、合约、期权等多种交易产品
"""

import hmac
import hashlib
import base64
import json
import os
import time
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class OKXProductType(Enum):
    """OKX产品类型"""
    SPOT = "SPOT"           # 币币
    MARGIN = "MARGIN"       # 币币杠杆
    SWAP = "SWAP"           # 永续合约
    FUTURES = "FUTURES"     # 交割合约
    OPTION = "OPTION"       # 期权

class OKXOrderType(Enum):
    """OKX订单类型"""
    MARKET = "market"       # 市价单
    LIMIT = "limit"         # 限价单
    POST_ONLY = "post_only" # 只做maker
    FOK = "fok"            # 全部成交或立即取消
    IOC = "ioc"            # 立即成交并取消剩余

class OKXOrderSide(Enum):
    """OKX订单方向"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class OKXConfig:
    """OKX配置"""
    api_key: str
    secret_key: str
    passphrase: str
    is_sandbox: bool = False
    base_url: str = "https://www.okx.com"
    sandbox_url: str = "https://www.okx.com"  # OKX使用相同URL，通过header区分

class OKXAPIManager:
    """OKX API管理器"""
    
    def __init__(self, config: OKXConfig):
        self.config = config
        self.base_url = config.sandbox_url if config.is_sandbox else config.base_url
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = "") -> str:
        """生成OKX API签名"""
        message = timestamp + method + request_path + body
        signature = hmac.new(
            self.config.secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).digest()
        return base64.b64encode(signature).decode('utf-8')
    
    def _get_headers(self, method: str, request_path: str, body: str = "") -> Dict[str, str]:
        """获取请求头"""
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        signature = self._generate_signature(timestamp, method, request_path, body)
        
        headers = {
            'OK-ACCESS-KEY': self.config.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.config.passphrase,
            'Content-Type': 'application/json'
        }
        
        if self.config.is_sandbox:
            headers['x-simulated-trading'] = '1'
            
        return headers
    
    async def _request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        body = json.dumps(data) if data else ""
        headers = self._get_headers(method, endpoint, body)
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                data=body if body else None
            ) as response:
                result = await response.json()
                
                if result.get('code') != '0':
                    raise Exception(f"OKX API Error: {result.get('msg', 'Unknown error')}")
                
                return result
                
        except Exception as e:
            raise Exception(f"OKX API Request Failed: {str(e)}")
    
    # ==================== 账户相关API ====================
    
    async def get_account_balance(self, currency: str = None) -> Dict:
        """获取账户余额"""
        endpoint = "/api/v5/account/balance"
        params = {}
        if currency:
            params['ccy'] = currency
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_positions(self, inst_type: str = None, inst_id: str = None) -> Dict:
        """获取持仓信息"""
        endpoint = "/api/v5/account/positions"
        params = {}
        if inst_type:
            params['instType'] = inst_type
        if inst_id:
            params['instId'] = inst_id
        
        return await self._request("GET", endpoint, params=params)
    
    # ==================== 交易相关API ====================
    
    async def place_order(self, 
                         inst_id: str,
                         trade_mode: str,
                         side: OKXOrderSide,
                         order_type: OKXOrderType,
                         size: str,
                         price: str = None,
                         client_order_id: str = None) -> Dict:
        """下单"""
        endpoint = "/api/v5/trade/order"
        
        data = {
            "instId": inst_id,
            "tdMode": trade_mode,  # cash, cross, isolated
            "side": side.value,
            "ordType": order_type.value,
            "sz": size
        }
        
        if price and order_type != OKXOrderType.MARKET:
            data["px"] = price
            
        if client_order_id:
            data["clOrdId"] = client_order_id
        
        return await self._request("POST", endpoint, data=data)
    
    async def cancel_order(self, inst_id: str, order_id: str = None, client_order_id: str = None) -> Dict:
        """撤单"""
        endpoint = "/api/v5/trade/cancel-order"
        
        data = {"instId": inst_id}
        if order_id:
            data["ordId"] = order_id
        if client_order_id:
            data["clOrdId"] = client_order_id
        
        return await self._request("POST", endpoint, data=data)
    
    async def get_order_details(self, inst_id: str, order_id: str = None, client_order_id: str = None) -> Dict:
        """获取订单详情"""
        endpoint = "/api/v5/trade/order"
        params = {"instId": inst_id}
        
        if order_id:
            params["ordId"] = order_id
        if client_order_id:
            params["clOrdId"] = client_order_id
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_pending_orders(self, inst_type: str = None, inst_id: str = None) -> Dict:
        """获取未成交订单列表"""
        endpoint = "/api/v5/trade/orders-pending"
        params = {}
        
        if inst_type:
            params["instType"] = inst_type
        if inst_id:
            params["instId"] = inst_id
        
        return await self._request("GET", endpoint, params=params)
    
    # ==================== 行情相关API ====================
    
    async def get_ticker(self, inst_id: str) -> Dict:
        """获取单个产品行情信息"""
        endpoint = "/api/v5/market/ticker"
        params = {"instId": inst_id}
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_tickers(self, inst_type: str) -> Dict:
        """获取所有产品行情信息"""
        endpoint = "/api/v5/market/tickers"
        params = {"instType": inst_type}
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_order_book(self, inst_id: str, depth: int = 20) -> Dict:
        """获取产品深度"""
        endpoint = "/api/v5/market/books"
        params = {
            "instId": inst_id,
            "sz": str(depth)
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_kline_data(self, inst_id: str, bar: str = "1m", limit: int = 100) -> Dict:
        """获取K线数据"""
        endpoint = "/api/v5/market/candles"
        params = {
            "instId": inst_id,
            "bar": bar,  # 1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 6H, 12H, 1D, 1W, 1M
            "limit": str(limit)
        }
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_trades(self, inst_id: str, limit: int = 100) -> Dict:
        """获取交易数据"""
        endpoint = "/api/v5/market/trades"
        params = {
            "instId": inst_id,
            "limit": str(limit)
        }
        
        return await self._request("GET", endpoint, params=params)
    
    # ==================== 公共数据API ====================
    
    async def get_instruments(self, inst_type: str) -> Dict:
        """获取交易产品基础信息"""
        endpoint = "/api/v5/public/instruments"
        params = {"instType": inst_type}
        
        return await self._request("GET", endpoint, params=params)
    
    async def get_system_time(self) -> Dict:
        """获取系统时间"""
        endpoint = "/api/v5/public/time"
        return await self._request("GET", endpoint)
    
    # ==================== 策略交易API ====================
    
    async def place_algo_order(self, 
                              inst_id: str,
                              trade_mode: str,
                              side: OKXOrderSide,
                              order_type: str,  # conditional, oco, trigger
                              size: str,
                              trigger_price: str,
                              order_price: str = None) -> Dict:
        """策略委托下单"""
        endpoint = "/api/v5/trade/order-algo"
        
        data = {
            "instId": inst_id,
            "tdMode": trade_mode,
            "side": side.value,
            "ordType": order_type,
            "sz": size,
            "triggerPx": trigger_price
        }
        
        if order_price:
            data["orderPx"] = order_price
        
        return await self._request("POST", endpoint, data=data)
    
    async def cancel_algo_order(self, algo_id: str) -> Dict:
        """撤销策略委托订单"""
        endpoint = "/api/v5/trade/cancel-algos"
        data = [{"algoId": algo_id}]
        
        return await self._request("POST", endpoint, data=data)

# 使用示例
async def example_usage():
    """使用示例"""
    config = OKXConfig(
        api_key=os.getenv("OKX_API_KEY", ""),
        secret_key=os.getenv("OKX_SECRET_KEY", ""),
        passphrase=os.getenv("OKX_PASSPHRASE", ""),
        is_sandbox=True
    )
    
    async with OKXAPIManager(config) as okx:
        # 获取账户余额
        balance = await okx.get_account_balance()
        print("账户余额:", balance)
        
        # 获取BTC-USDT行情
        ticker = await okx.get_ticker("BTC-USDT")
        print("BTC-USDT行情:", ticker)
        
        # 下限价买单
        order = await okx.place_order(
            inst_id="BTC-USDT",
            trade_mode="cash",
            side=OKXOrderSide.BUY,
            order_type=OKXOrderType.LIMIT,
            size="0.001",
            price="50000"
        )
        print("下单结果:", order)

if __name__ == "__main__":
    asyncio.run(example_usage())
