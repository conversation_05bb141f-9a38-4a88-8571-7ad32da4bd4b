#!/usr/bin/env python3
"""
测试事件循环修复效果
验证"Event loop is closed"错误是否已解决
"""

import asyncio
import threading
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_event_loop_management():
    """测试事件循环管理"""
    print("🔍 测试事件循环管理修复...")
    print("=" * 60)

    # 模拟修复后的RSI策略
    class MockRSIStrategy:
        def __init__(self):
            self.is_active = True
            self.logger = self

        def info(self, msg):
            print(f"[INFO] {msg}")

        def error(self, msg):
            print(f"[ERROR] {msg}")

        def warning(self, msg):
            print(f"[WARNING] {msg}")

        async def _start_data_processing_loop(self):
            """模拟数据处理循环"""
            print("📈 开始数据处理循环...")
            await asyncio.sleep(1)  # 模拟数据获取
            print("✅ 数据处理完成")

        def _async_update_wrapper(self):
            """修复后的异步更新包装器 - 简化版本"""
            try:
                # 检查策略是否仍然激活
                if not self.is_active:
                    self.info("🛑 策略已停止，跳过更新")
                    return

                import asyncio

                # 检查是否有运行中的事件循环
                try:
                    current_loop = asyncio.get_running_loop()
                    if current_loop.is_closed():
                        self.warning("当前事件循环已关闭，跳过此次更新")
                        return
                    else:
                        # 使用当前循环创建任务
                        task = current_loop.create_task(self._start_data_processing_loop())
                        # 添加错误处理回调
                        task.add_done_callback(self._handle_task_completion)
                        return
                except RuntimeError:
                    # 没有运行中的循环，记录警告并跳过
                    self.warning("没有可用的事件循环，跳过此次更新")
                    return

            except Exception as e:
                self.error(f"异步更新包装器失败: {e}")

        def _handle_task_completion(self, task):
            """处理任务完成"""
            try:
                if task.exception():
                    self.error(f"数据处理任务异常: {task.exception()}")
            except Exception as e:
                self.error(f"处理任务完成回调失败: {e}")

        async def stop(self):
            """停止策略"""
            try:
                self.is_active = False
                self.info("🛑 策略已停止")
            except Exception as e:
                self.error(f"停止策略失败: {e}")

    # 执行测试
    print("1️⃣ 创建模拟RSI策略...")
    strategy = MockRSIStrategy()

    print("\n2️⃣ 测试在事件循环中的调用...")
    async def test_in_loop():
        print("在事件循环中测试...")
        strategy._async_update_wrapper()
        await asyncio.sleep(2)
        print("事件循环中测试完成")

    asyncio.run(test_in_loop())

    print("\n3️⃣ 测试在主线程中的调用...")
    strategy._async_update_wrapper()  # 应该跳过，因为没有事件循环

    print("\n4️⃣ 测试策略停止...")
    asyncio.run(strategy.stop())

    print("\n✅ 测试完成！")
    print("如果没有看到'Event loop is closed'错误，说明修复成功。")

def test_session_management():
    """测试Session管理修复"""
    print("\n🔍 测试Session管理修复...")
    print("=" * 60)

    import aiohttp

    class MockExchangeManager:
        def __init__(self):
            self.session = None
            self._is_closing = False
            self._session_lock = asyncio.Lock()

        async def initialize(self):
            """初始化session"""
            self.session = aiohttp.ClientSession()
            print("✅ Session已初始化")

        async def test_request(self):
            """测试请求"""
            try:
                # 检查session状态
                if not self.session or self.session.closed or getattr(self, '_is_closing', False):
                    print("⚠️ Session不可用，跳过请求")
                    return False

                # 使用锁保护session访问
                async with self._session_lock:
                    if self.session and not self.session.closed and not getattr(self, '_is_closing', False):
                        print("📡 发送测试请求...")
                        # 模拟请求
                        await asyncio.sleep(0.1)
                        print("✅ 请求成功")
                        return True
                    else:
                        print("⚠️ Session在请求过程中变为不可用")
                        return False

            except Exception as e:
                print(f"❌ 请求失败: {e}")
                return False

        async def close(self):
            """关闭session"""
            try:
                self._is_closing = True

                if self.session and not self.session.closed:
                    await asyncio.wait_for(self.session.close(), timeout=2.0)
                    print("✅ Session已关闭")

                self.session = None
                self._is_closing = False

            except Exception as e:
                print(f"❌ 关闭Session失败: {e}")

    async def test_session():
        manager = MockExchangeManager()

        # 初始化
        await manager.initialize()

        # 测试正常请求
        await manager.test_request()

        # 测试关闭后的请求
        await manager.close()
        await manager.test_request()  # 应该跳过

    asyncio.run(test_session())
    print("✅ Session管理测试完成")

if __name__ == "__main__":
    test_event_loop_management()
    test_session_management()
