#!/usr/bin/env python3
"""
测试事件循环修复效果
验证"Event loop is closed"错误是否已解决
"""

import asyncio
import threading
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_event_loop_management():
    """测试事件循环管理"""
    print("🔍 测试事件循环管理修复...")
    print("=" * 60)
    
    # 模拟RSI策略的事件循环创建逻辑
    class MockRSIStrategy:
        def __init__(self):
            self.is_active = True
            self.logger = self
            
        def info(self, msg):
            print(f"[INFO] {msg}")
            
        def error(self, msg):
            print(f"[ERROR] {msg}")
            
        def warning(self, msg):
            print(f"[WARNING] {msg}")
            
        async def _start_data_processing_loop(self):
            """模拟数据处理循环"""
            print("📈 开始数据处理循环...")
            await asyncio.sleep(2)  # 模拟数据获取
            print("✅ 数据处理完成")
            
        def _async_update_wrapper(self):
            """修复后的异步更新包装器"""
            try:
                if not self.is_active:
                    self.info("🛑 策略已停止，跳过更新")
                    return

                import asyncio

                # 检查是否有运行中的事件循环
                try:
                    current_loop = asyncio.get_running_loop()
                    if current_loop.is_closed():
                        self.warning("当前事件循环已关闭，创建新循环")
                        raise RuntimeError("Loop is closed")
                    else:
                        # 使用当前循环
                        current_loop.create_task(self._start_data_processing_loop())
                        return
                except RuntimeError:
                    # 没有运行中的循环，创建新的持久化事件循环
                    try:
                        # 检查是否已有全局事件循环
                        if not hasattr(self, '_persistent_loop') or self._persistent_loop.is_closed():
                            self._persistent_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(self._persistent_loop)
                            self.info("创建新的持久化事件循环")
                        
                        # 在持久化循环中创建任务，不立即关闭循环
                        task = self._persistent_loop.create_task(self._start_data_processing_loop())
                        # 启动事件循环但不阻塞，让任务在后台运行
                        if not self._persistent_loop.is_running():
                            # 使用线程运行事件循环，避免阻塞主线程
                            import threading
                            def run_loop():
                                try:
                                    self._persistent_loop.run_forever()
                                except Exception as e:
                                    self.error(f"事件循环运行异常: {e}")
                            
                            loop_thread = threading.Thread(target=run_loop, daemon=True)
                            loop_thread.start()
                            self.info("事件循环已在后台线程启动")
                        
                    except Exception as loop_error:
                        self.error(f"创建持久化事件循环失败: {loop_error}")

            except Exception as e:
                self.error(f"异步更新包装器失败: {e}")
                
        async def stop(self):
            """停止策略"""
            try:
                self.is_active = False
                
                # 清理持久化事件循环
                if hasattr(self, '_persistent_loop') and self._persistent_loop:
                    try:
                        if not self._persistent_loop.is_closed():
                            # 停止事件循环
                            self._persistent_loop.call_soon_threadsafe(self._persistent_loop.stop)
                            self.info("持久化事件循环已停止")
                    except Exception as e:
                        self.warning(f"清理事件循环失败: {e}")
                        
            except Exception as e:
                self.error(f"停止策略失败: {e}")

    # 执行测试
    print("1️⃣ 创建模拟RSI策略...")
    strategy = MockRSIStrategy()
    
    print("\n2️⃣ 测试事件循环创建...")
    strategy._async_update_wrapper()
    
    print("\n3️⃣ 等待任务执行...")
    time.sleep(3)
    
    print("\n4️⃣ 测试多次调用...")
    strategy._async_update_wrapper()  # 应该复用现有循环
    time.sleep(2)
    
    print("\n5️⃣ 测试策略停止...")
    asyncio.run(strategy.stop())
    
    print("\n✅ 测试完成！")
    print("如果没有看到'Event loop is closed'错误，说明修复成功。")

if __name__ == "__main__":
    test_event_loop_management()
