#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试os模块导入Bug
"""

def test_gate_api_manager():
    """测试gate_api_manager.py的os模块导入问题"""
    try:
        # 尝试执行gate_api_manager.py中的example_usage函数
        exec("""
import hmac
import hashlib
import json
import os
import time
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

@dataclass
class GateConfig:
    api_key: str
    secret_key: str
    is_sandbox: bool = False

# 现在应该能正常工作，因为os模块已导入
config = GateConfig(
    api_key=os.getenv("GATE_API_KEY", ""),
    secret_key=os.getenv("GATE_SECRET_KEY", ""),
    is_sandbox=True
)
print("配置创建成功:", config.api_key, config.secret_key, config.is_sandbox)
""")
        print("✅ os模块导入问题已修复")
        return True
    except NameError as e:
        if "os" in str(e):
            print(f"❌ os模块导入问题仍存在: {e}")
            return False
        else:
            print(f"❌ 其他NameError: {e}")
            return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_gate_api_manager()
