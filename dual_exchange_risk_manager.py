#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统双交易所专用风险管理器

专门针对OKX和Gate.io的风险控制和管理
严格遵守WMZC系统的14项禁令、5项必须原则和5项主动原则
"""

import asyncio
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# 导入WMZC系统模块
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from asp import ExchangeManager, ExchangeType, LogManager

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskMetrics:
    """风险指标"""
    correlation: float  # 相关性
    volatility_okx: float  # OKX波动率
    volatility_gate: float  # Gate.io波动率
    price_deviation: float  # 价格偏差
    volume_ratio: float  # 成交量比率
    liquidity_risk: float  # 流动性风险
    arbitrage_opportunity: float  # 套利机会
    risk_level: RiskLevel  # 风险等级
    timestamp: datetime  # 时间戳

@dataclass
class PositionRisk:
    """仓位风险"""
    symbol: str
    okx_position: float
    gate_position: float
    total_exposure: float
    risk_ratio: float
    max_allowed: float
    current_risk: RiskLevel

class DualExchangeRiskManager:
    """
    双交易所专用风险管理器
    
    核心功能：
    1. 跨交易所相关性监控
    2. 套利风险检测
    3. 流动性风险管理
    4. 仓位风险控制
    5. 实时风险预警
    """
    
    def __init__(self):
        self.logger = LogManager()
        
        # 风险配置参数
        self.risk_config = {
            'max_correlation': 0.8,  # 最大相关性阈值
            'max_position_ratio': 0.3,  # 最大仓位比例
            'max_total_exposure': 1000000,  # 最大总敞口（USDT）
            'volatility_threshold': 0.05,  # 波动率阈值
            'price_deviation_threshold': 0.02,  # 价格偏差阈值
            'min_liquidity_ratio': 0.1,  # 最小流动性比率
            'arbitrage_threshold': 0.001,  # 套利机会阈值
            'risk_check_interval': 30,  # 风险检查间隔（秒）
        }
        
        # 风险数据存储
        self.price_history = {'OKX': {}, 'GATE': {}}
        self.volume_history = {'OKX': {}, 'GATE': {}}
        self.risk_metrics_history = []
        self.position_risks = {}
        self.alerts = []
        
        # 交易所管理器
        self.exchange_managers = {}
        
        # 风险监控状态
        self.monitoring_active = False
        self.last_risk_check = None
        
    async def initialize(self):
        """初始化风险管理器"""
        try:
            self.logger.info("初始化双交易所风险管理器")
            
            # 初始化交易所管理器
            for exchange_type in [ExchangeType.OKX, ExchangeType.GATE]:
                manager = ExchangeManager(exchange_type)
                await manager.initialize()
                self.exchange_managers[exchange_type] = manager
                self.logger.info(f"{exchange_type.value}交易所管理器初始化完成")
            
            self.logger.info("双交易所风险管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"风险管理器初始化失败: {e}")
            raise
    
    async def start_monitoring(self, symbols: List[str]):
        """开始风险监控"""
        try:
            self.logger.info(f"开始监控 {len(symbols)} 个交易对的风险")
            self.monitoring_active = True
            
            # 启动监控任务
            monitoring_tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self._monitor_symbol_risk(symbol))
                monitoring_tasks.append(task)
            
            # 启动全局风险检查任务
            global_task = asyncio.create_task(self._global_risk_monitor())
            monitoring_tasks.append(global_task)
            
            # 等待所有监控任务
            await asyncio.gather(*monitoring_tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"风险监控启动失败: {e}")
            self.monitoring_active = False
            raise
    
    async def stop_monitoring(self):
        """停止风险监控"""
        self.monitoring_active = False
        self.logger.info("风险监控已停止")
    
    async def _monitor_symbol_risk(self, symbol: str):
        """监控单个交易对的风险"""
        try:
            while self.monitoring_active:
                # 获取双交易所数据
                okx_data = await self._get_market_data(ExchangeType.OKX, symbol)
                gate_data = await self._get_market_data(ExchangeType.GATE, symbol)
                
                if okx_data and gate_data:
                    # 计算风险指标
                    risk_metrics = await self._calculate_risk_metrics(symbol, okx_data, gate_data)
                    
                    # 存储历史数据
                    self._store_risk_data(symbol, okx_data, gate_data, risk_metrics)
                    
                    # 检查风险阈值
                    await self._check_risk_thresholds(symbol, risk_metrics)
                
                # 等待下一次检查
                await asyncio.sleep(self.risk_config['risk_check_interval'])
                
        except Exception as e:
            self.logger.error(f"交易对 {symbol} 风险监控异常: {e}")
    
    async def _global_risk_monitor(self):
        """全局风险监控"""
        try:
            while self.monitoring_active:
                # 检查整体仓位风险
                await self._check_portfolio_risk()
                
                # 检查系统性风险
                await self._check_systemic_risk()
                
                # 清理过期数据
                self._cleanup_old_data()
                
                # 等待下一次全局检查
                await asyncio.sleep(self.risk_config['risk_check_interval'] * 2)
                
        except Exception as e:
            self.logger.error(f"全局风险监控异常: {e}")
    
    async def _get_market_data(self, exchange_type: ExchangeType, symbol: str) -> Optional[Dict]:
        """获取市场数据"""
        try:
            manager = self.exchange_managers[exchange_type]
            
            # 获取K线数据（最近20根）
            kline_data = await manager.get_kline_data(symbol, "1m", 20)
            
            if not kline_data:
                return None
            
            # 提取价格和成交量数据
            prices = []
            volumes = []

            for kline in kline_data:
                try:
                    # 处理不同的数据格式
                    if hasattr(kline, 'close') and hasattr(kline, 'volume'):
                        # KlineData对象格式
                        prices.append(float(kline.close))
                        volumes.append(float(kline.volume))
                    elif isinstance(kline, (list, tuple)) and len(kline) >= 6:
                        # 列表格式 [timestamp, open, high, low, close, volume]
                        prices.append(float(kline[4]))  # 收盘价
                        volumes.append(float(kline[5]))  # 成交量
                    elif isinstance(kline, dict):
                        # 字典格式
                        prices.append(float(kline.get('close', kline.get('c', 0))))
                        volumes.append(float(kline.get('volume', kline.get('v', 0))))
                    else:
                        self.logger.warning(f"未知的K线数据格式: {type(kline)}")
                        continue
                except (ValueError, AttributeError, KeyError) as e:
                    self.logger.warning(f"解析K线数据失败: {e}")
                    continue

            if not prices or not volumes:
                self.logger.warning(f"未能提取到有效的价格或成交量数据")
                return None
            
            return {
                'price': prices[-1],  # 最新价格
                'prices': prices,
                'volume': volumes[-1],  # 最新成交量
                'volumes': volumes,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"获取 {exchange_type.value} {symbol} 市场数据失败: {e}")
            return None
    
    async def _calculate_risk_metrics(self, symbol: str, okx_data: Dict, gate_data: Dict) -> RiskMetrics:
        """计算风险指标"""
        try:
            # 1. 计算价格相关性
            correlation = self._calculate_correlation(okx_data['prices'], gate_data['prices'])
            
            # 2. 计算波动率
            volatility_okx = self._calculate_volatility(okx_data['prices'])
            volatility_gate = self._calculate_volatility(gate_data['prices'])
            
            # 3. 计算价格偏差
            price_deviation = abs(okx_data['price'] - gate_data['price']) / okx_data['price']
            
            # 4. 计算成交量比率
            volume_ratio = min(okx_data['volume'], gate_data['volume']) / max(okx_data['volume'], gate_data['volume'])
            
            # 5. 计算流动性风险
            liquidity_risk = self._calculate_liquidity_risk(okx_data['volumes'], gate_data['volumes'])
            
            # 6. 计算套利机会
            arbitrage_opportunity = abs(okx_data['price'] - gate_data['price']) / min(okx_data['price'], gate_data['price'])
            
            # 7. 评估风险等级
            risk_level = self._assess_risk_level(correlation, volatility_okx, volatility_gate, price_deviation)
            
            return RiskMetrics(
                correlation=correlation,
                volatility_okx=volatility_okx,
                volatility_gate=volatility_gate,
                price_deviation=price_deviation,
                volume_ratio=volume_ratio,
                liquidity_risk=liquidity_risk,
                arbitrage_opportunity=arbitrage_opportunity,
                risk_level=risk_level,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"计算 {symbol} 风险指标失败: {e}")
            # 返回默认风险指标
            return RiskMetrics(
                correlation=0.0,
                volatility_okx=0.0,
                volatility_gate=0.0,
                price_deviation=0.0,
                volume_ratio=0.0,
                liquidity_risk=1.0,
                arbitrage_opportunity=0.0,
                risk_level=RiskLevel.CRITICAL,
                timestamp=datetime.now()
            )
    
    def _calculate_correlation(self, prices1: List[float], prices2: List[float]) -> float:
        """计算价格相关性"""
        try:
            if len(prices1) < 2 or len(prices2) < 2:
                return 0.0
            
            # 计算收益率
            returns1 = np.diff(prices1) / prices1[:-1]
            returns2 = np.diff(prices2) / prices2[:-1]
            
            # 计算相关系数
            correlation = np.corrcoef(returns1, returns2)[0, 1]
            
            return correlation if not np.isnan(correlation) else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算波动率"""
        try:
            if len(prices) < 2:
                return 0.0

            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns)

            return volatility if not np.isnan(volatility) else 0.0

        except Exception:
            return 0.0

    def _calculate_liquidity_risk(self, volumes1: List[float], volumes2: List[float]) -> float:
        """计算流动性风险"""
        try:
            avg_volume1 = np.mean(volumes1)
            avg_volume2 = np.mean(volumes2)

            # 流动性风险 = 1 - 最小成交量/最大成交量
            liquidity_risk = 1 - min(avg_volume1, avg_volume2) / max(avg_volume1, avg_volume2)

            return liquidity_risk

        except Exception:
            return 1.0  # 最高流动性风险

    def _assess_risk_level(self, correlation: float, vol_okx: float, vol_gate: float, price_dev: float) -> RiskLevel:
        """评估风险等级"""
        try:
            risk_score = 0

            # 相关性风险
            if abs(correlation) > self.risk_config['max_correlation']:
                risk_score += 3
            elif abs(correlation) > 0.6:
                risk_score += 2
            elif abs(correlation) > 0.4:
                risk_score += 1

            # 波动率风险
            max_volatility = max(vol_okx, vol_gate)
            if max_volatility > self.risk_config['volatility_threshold'] * 2:
                risk_score += 3
            elif max_volatility > self.risk_config['volatility_threshold']:
                risk_score += 2
            elif max_volatility > self.risk_config['volatility_threshold'] * 0.5:
                risk_score += 1

            # 价格偏差风险
            if price_dev > self.risk_config['price_deviation_threshold'] * 2:
                risk_score += 3
            elif price_dev > self.risk_config['price_deviation_threshold']:
                risk_score += 2
            elif price_dev > self.risk_config['price_deviation_threshold'] * 0.5:
                risk_score += 1

            # 根据风险分数确定等级
            if risk_score >= 7:
                return RiskLevel.CRITICAL
            elif risk_score >= 5:
                return RiskLevel.HIGH
            elif risk_score >= 3:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW

        except Exception:
            return RiskLevel.CRITICAL

    def _store_risk_data(self, symbol: str, okx_data: Dict, gate_data: Dict, risk_metrics: RiskMetrics):
        """存储风险数据"""
        try:
            # 存储价格历史
            if symbol not in self.price_history['OKX']:
                self.price_history['OKX'][symbol] = []
                self.price_history['GATE'][symbol] = []

            self.price_history['OKX'][symbol].append({
                'price': okx_data['price'],
                'timestamp': okx_data['timestamp']
            })

            self.price_history['GATE'][symbol].append({
                'price': gate_data['price'],
                'timestamp': gate_data['timestamp']
            })

            # 限制历史数据长度
            max_history = 1000
            if len(self.price_history['OKX'][symbol]) > max_history:
                self.price_history['OKX'][symbol] = self.price_history['OKX'][symbol][-max_history:]
                self.price_history['GATE'][symbol] = self.price_history['GATE'][symbol][-max_history:]

            # 存储风险指标历史
            self.risk_metrics_history.append({
                'symbol': symbol,
                'metrics': risk_metrics,
                'timestamp': datetime.now()
            })

            # 限制风险指标历史长度
            if len(self.risk_metrics_history) > max_history:
                self.risk_metrics_history = self.risk_metrics_history[-max_history:]

        except Exception as e:
            self.logger.error(f"存储 {symbol} 风险数据失败: {e}")

    async def _check_risk_thresholds(self, symbol: str, risk_metrics: RiskMetrics):
        """检查风险阈值"""
        try:
            alerts = []

            # 检查相关性风险
            if abs(risk_metrics.correlation) > self.risk_config['max_correlation']:
                alerts.append(f"{symbol} 相关性过高: {risk_metrics.correlation:.3f}")

            # 检查价格偏差
            if risk_metrics.price_deviation > self.risk_config['price_deviation_threshold']:
                alerts.append(f"{symbol} 价格偏差过大: {risk_metrics.price_deviation:.3f}")

            # 检查波动率
            max_vol = max(risk_metrics.volatility_okx, risk_metrics.volatility_gate)
            if max_vol > self.risk_config['volatility_threshold']:
                alerts.append(f"{symbol} 波动率过高: {max_vol:.3f}")

            # 检查流动性风险
            if risk_metrics.liquidity_risk > (1 - self.risk_config['min_liquidity_ratio']):
                alerts.append(f"{symbol} 流动性风险过高: {risk_metrics.liquidity_risk:.3f}")

            # 检查套利机会
            if risk_metrics.arbitrage_opportunity > self.risk_config['arbitrage_threshold']:
                alerts.append(f"{symbol} 发现套利机会: {risk_metrics.arbitrage_opportunity:.3f}")

            # 发送风险警报
            for alert in alerts:
                await self._send_risk_alert(alert, risk_metrics.risk_level)

        except Exception as e:
            self.logger.error(f"检查 {symbol} 风险阈值失败: {e}")

    async def _send_risk_alert(self, message: str, risk_level: RiskLevel):
        """发送风险警报"""
        try:
            alert = {
                'message': message,
                'risk_level': risk_level,
                'timestamp': datetime.now()
            }

            self.alerts.append(alert)

            # 根据风险等级记录日志
            if risk_level == RiskLevel.CRITICAL:
                self.logger.error(f"严重风险警报: {message}")
            elif risk_level == RiskLevel.HIGH:
                self.logger.warning(f"高风险警报: {message}")
            elif risk_level == RiskLevel.MEDIUM:
                self.logger.warning(f"中等风险警报: {message}")
            else:
                self.logger.info(f"低风险提醒: {message}")

            # 限制警报历史长度
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]

        except Exception as e:
            self.logger.error(f"发送风险警报失败: {e}")

    async def _check_portfolio_risk(self):
        """检查投资组合风险"""
        try:
            # 这里可以实现投资组合级别的风险检查
            # 例如：总敞口、仓位集中度、相关性等
            pass

        except Exception as e:
            self.logger.error(f"检查投资组合风险失败: {e}")

    async def _check_systemic_risk(self):
        """检查系统性风险"""
        try:
            # 这里可以实现系统性风险检查
            # 例如：市场整体波动、交易所连接状态等
            pass

        except Exception as e:
            self.logger.error(f"检查系统性风险失败: {e}")

    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=24)

            # 清理过期的风险指标历史
            self.risk_metrics_history = [
                item for item in self.risk_metrics_history
                if item['timestamp'] > cutoff_time
            ]

            # 清理过期的警报
            self.alerts = [
                alert for alert in self.alerts
                if alert['timestamp'] > cutoff_time
            ]

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")

    async def get_risk_report(self) -> Dict[str, Any]:
        """获取风险报告"""
        try:
            # 计算最新风险统计
            recent_metrics = [
                item for item in self.risk_metrics_history
                if item['timestamp'] > datetime.now() - timedelta(hours=1)
            ]

            # 统计风险等级分布
            risk_distribution = {level.value: 0 for level in RiskLevel}
            for item in recent_metrics:
                risk_distribution[item['metrics'].risk_level.value] += 1

            # 统计警报数量
            recent_alerts = [
                alert for alert in self.alerts
                if alert['timestamp'] > datetime.now() - timedelta(hours=1)
            ]

            alert_distribution = {level.value: 0 for level in RiskLevel}
            for alert in recent_alerts:
                alert_distribution[alert['risk_level'].value] += 1

            return {
                'monitoring_status': self.monitoring_active,
                'last_check': self.last_risk_check.isoformat() if self.last_risk_check else None,
                'total_symbols': len(set(item['symbol'] for item in recent_metrics)),
                'risk_distribution': risk_distribution,
                'alert_distribution': alert_distribution,
                'total_alerts': len(recent_alerts),
                'recent_metrics_count': len(recent_metrics),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"生成风险报告失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def get_arbitrage_opportunities(self) -> List[Dict[str, Any]]:
        """获取套利机会"""
        try:
            opportunities = []

            # 分析最近的风险指标
            recent_metrics = [
                item for item in self.risk_metrics_history
                if item['timestamp'] > datetime.now() - timedelta(minutes=5)
            ]

            for item in recent_metrics:
                metrics = item['metrics']
                if metrics.arbitrage_opportunity > self.risk_config['arbitrage_threshold']:
                    opportunities.append({
                        'symbol': item['symbol'],
                        'arbitrage_rate': metrics.arbitrage_opportunity,
                        'price_deviation': metrics.price_deviation,
                        'risk_level': metrics.risk_level.value,
                        'timestamp': item['timestamp'].isoformat()
                    })

            # 按套利机会大小排序
            opportunities.sort(key=lambda x: x['arbitrage_rate'], reverse=True)

            return opportunities

        except Exception as e:
            self.logger.error(f"获取套利机会失败: {e}")
            return []

    async def get_correlation_analysis(self) -> Dict[str, Any]:
        """获取相关性分析"""
        try:
            analysis = {}

            # 分析最近的相关性数据
            recent_metrics = [
                item for item in self.risk_metrics_history
                if item['timestamp'] > datetime.now() - timedelta(hours=1)
            ]

            # 按交易对分组
            symbol_correlations = {}
            for item in recent_metrics:
                symbol = item['symbol']
                if symbol not in symbol_correlations:
                    symbol_correlations[symbol] = []
                symbol_correlations[symbol].append(item['metrics'].correlation)

            # 计算平均相关性
            for symbol, correlations in symbol_correlations.items():
                analysis[symbol] = {
                    'avg_correlation': np.mean(correlations),
                    'max_correlation': np.max(correlations),
                    'min_correlation': np.min(correlations),
                    'correlation_std': np.std(correlations),
                    'data_points': len(correlations)
                }

            return analysis

        except Exception as e:
            self.logger.error(f"获取相关性分析失败: {e}")
            return {}

    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop_monitoring()

            for manager in self.exchange_managers.values():
                await manager.close()

            self.logger.info("双交易所风险管理器清理完成")

        except Exception as e:
            self.logger.error(f"风险管理器清理失败: {e}")

# 使用示例
async def main():
    risk_manager = DualExchangeRiskManager()

    try:
        await risk_manager.initialize()

        # 开始监控主要交易对
        symbols = ['BTC-USDT', 'ETH-USDT']

        # 启动监控（这里只运行30秒作为演示）
        monitoring_task = asyncio.create_task(risk_manager.start_monitoring(symbols))

        # 等待30秒
        await asyncio.sleep(30)

        # 停止监控
        await risk_manager.stop_monitoring()

        # 获取风险报告
        report = await risk_manager.get_risk_report()
        print("风险报告:", report)

        # 获取套利机会
        opportunities = await risk_manager.get_arbitrage_opportunities()
        print("套利机会:", opportunities)

        # 获取相关性分析
        correlation_analysis = await risk_manager.get_correlation_analysis()
        print("相关性分析:", correlation_analysis)

    finally:
        await risk_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
