#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统快速启动脚本
优化版本，减少初始化时间，提高GUI响应速度
"""

import sys
import os
import time
import logging

def check_dependencies():
    """检查核心依赖"""
    required_packages = ['tkinter', 'asyncio', 'aiohttp', 'pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'asyncio':
                import asyncio
            elif package == 'aiohttp':
                import aiohttp
            elif package == 'pandas':
                import pandas
            elif package == 'numpy':
                import numpy
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        return False
    
    print("✅ 核心依赖检查通过")
    return True

def optimize_python_environment():
    """优化Python环境"""
    try:
        # 设置环境变量优化
        os.environ['PYTHONUNBUFFERED'] = '1'
        os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
        
        # 禁用一些不必要的警告
        import warnings
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        warnings.filterwarnings('ignore', category=FutureWarning)
        
        print("✅ Python环境优化完成")
        return True
    except Exception as e:
        print(f"⚠️ Python环境优化失败: {e}")
        return False

def main():
    """快速启动主程序"""
    print("=" * 60)
    print("WMZC量化交易系统 - 快速启动模式")
    print("版本: 1.0.0 (优化版)")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        input("按回车键退出...")
        sys.exit(1)
    
    # 优化环境
    optimize_python_environment()
    
    # 设置日志级别为WARNING，减少日志输出
    logging.basicConfig(level=logging.WARNING)
    
    print("🚀 正在启动WMZC系统...")
    start_time = time.time()
    
    try:
        # 导入主程序
        from asp import MainWindow
        
        print("✅ 主程序模块导入成功")
        
        # 创建主窗口
        print("🔧 正在创建主窗口...")
        app = MainWindow()
        
        elapsed_time = time.time() - start_time
        print(f"✅ 系统启动完成 (耗时: {elapsed_time:.2f}秒)")
        print("🎉 WMZC量化交易系统已就绪")
        
        # 运行程序
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入主程序失败: {e}")
        print("请确保asp.py文件存在且可访问")
        input("按回车键退出...")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
