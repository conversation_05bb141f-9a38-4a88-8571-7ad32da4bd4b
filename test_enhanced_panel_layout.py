#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强技术指标面板布局修复
"""

import tkinter as tk
from tkinter import ttk
import random

class EnhancedPanelLayoutTest:
    """增强面板布局测试"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔧 增强技术指标面板布局测试")
        self.root.geometry("1200x900")
        
        # 初始化变量
        self.setup_variables()
        
        # 创建测试界面
        self.create_test_interface()
        
        # 生成测试数据
        self.generate_test_data()
    
    def setup_variables(self):
        """设置测试变量"""
        self.beginner_mode_var = tk.BooleanVar(value=True)
        self.status_var = tk.StringVar(value="🟢 布局测试模式")
        
        # RSI变量
        self.rsi_value_var = tk.StringVar(value="65.2")
        self.rsi_status_var = tk.StringVar(value="🟡 偏强区域")
        self.rsi_trend_var = tk.StringVar(value="⬆️ 上涨")
        self.rsi_advice_var = tk.StringVar(value="🟡 谨慎操作：RSI偏强，注意回调风险")
        
        # KDJ变量
        self.kdj_k_var = tk.StringVar(value="72.5")
        self.kdj_d_var = tk.StringVar(value="68.3")
        self.kdj_j_var = tk.StringVar(value="80.9")
        self.kdj_status_var = tk.StringVar(value="🔴 超买区域")
        self.kdj_signal_var = tk.StringVar(value="🟡 金叉信号")
        self.kdj_advice_var = tk.StringVar(value="🔴 卖出信号：KDJ超买，建议减仓，K线上穿D线")
        
        # MACD变量
        self.macd_value_var = tk.StringVar(value="125.67")
        self.macd_signal_var = tk.StringVar(value="118.45")
        self.macd_hist_var = tk.StringVar(value="7.22")
        self.macd_status_var = tk.StringVar(value="🟢 多头市场")
        self.macd_trend_var = tk.StringVar(value="⬆️ 向上")
        self.macd_cross_var = tk.StringVar(value="🟡 金叉")
        self.macd_advice_var = tk.StringVar(value="🟢 买入信号：MACD金叉，建议买入")
        
        # 布林带变量
        self.bb_upper_var = tk.StringVar(value="119500")
        self.bb_middle_var = tk.StringVar(value="117000")
        self.bb_lower_var = tk.StringVar(value="114500")
        self.bb_position_var = tk.StringVar(value="🟡 中上区域")
        self.bb_width_var = tk.StringVar(value="➡️ 稳定")
        self.bb_advice_var = tk.StringVar(value="🟡 谨慎操作：价格在中轨上方")
    
    def create_test_interface(self):
        """创建测试界面"""
        # 创建带滚动条的主容器（模拟真实环境）
        canvas = tk.Canvas(self.root)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # 创建主内容容器
        main_container = ttk.Frame(scrollable_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题区域
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(title_frame, text="🔧 增强技术指标面板布局测试", 
                 font=("Microsoft YaHei", 16, "bold")).pack(side=tk.LEFT)

        # 基础指标面板（模拟）
        basic_frame = ttk.LabelFrame(main_container, text="📊 基础指标监控")
        basic_frame.pack(fill=tk.X, pady=(0, 15))
        basic_frame.configure(height=150)
        basic_frame.pack_propagate(False)
        
        ttk.Label(basic_frame, text="这里是原有的基础指标面板（模拟）", 
                 font=("Arial", 12)).pack(expand=True)

        # 增强技术指标面板
        enhanced_frame = ttk.LabelFrame(main_container, text="🚀 增强技术指标监视面板")
        enhanced_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        enhanced_frame.configure(height=500)

        # 创建增强面板内容
        self.create_enhanced_panel_content(enhanced_frame)

        # 保存引用
        self.canvas = canvas
        self.scrollable_frame = scrollable_frame
    
    def create_enhanced_panel_content(self, parent):
        """创建增强面板内容"""
        # 控制区域
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模式切换
        ttk.Checkbutton(control_frame, text="🔰 新手模式", 
                       variable=self.beginner_mode_var,
                       command=self.toggle_mode).pack(side=tk.LEFT, padx=(0, 20))

        # 控制按钮
        ttk.Button(control_frame, text="🔄 刷新", width=8,
                  command=self.refresh_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="❓ 帮助", width=8,
                  command=self.show_help).pack(side=tk.LEFT, padx=2)

        # 状态显示
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.RIGHT)

        # 内容区域
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 检测屏幕尺寸并选择布局
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        if screen_width >= 1400 and screen_height >= 800:
            self.create_grid_layout(content_frame)
        else:
            self.create_vertical_layout(content_frame)
    
    def create_grid_layout(self, parent):
        """创建网格布局"""
        parent.columnconfigure(0, weight=1)
        parent.columnconfigure(1, weight=1)
        parent.rowconfigure(0, weight=1)
        parent.rowconfigure(1, weight=1)

        self.create_rsi_module(parent, 0, 0)
        self.create_kdj_module(parent, 0, 1)
        self.create_macd_module(parent, 1, 0)
        self.create_bb_module(parent, 1, 1)
    
    def create_vertical_layout(self, parent):
        """创建垂直布局"""
        container = ttk.Frame(parent)
        container.pack(fill=tk.BOTH, expand=True)

        # 上半部分
        top_frame = ttk.Frame(container)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        top_frame.columnconfigure(0, weight=1)
        top_frame.columnconfigure(1, weight=1)

        self.create_rsi_module(top_frame, 0, 0)
        self.create_kdj_module(top_frame, 0, 1)

        # 下半部分
        bottom_frame = ttk.Frame(container)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        bottom_frame.columnconfigure(0, weight=1)
        bottom_frame.columnconfigure(1, weight=1)

        self.create_macd_module(bottom_frame, 0, 0)
        self.create_bb_module(bottom_frame, 0, 1)
    
    def create_rsi_module(self, parent, row, col):
        """创建RSI模块"""
        frame = ttk.LabelFrame(parent, text="📈 RSI相对强弱指数")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)

        # 数值显示
        value_frame = ttk.Frame(frame)
        value_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(value_frame, text="当前RSI:", font=("Arial", 10)).pack(side=tk.LEFT)
        rsi_label = ttk.Label(value_frame, textvariable=self.rsi_value_var,
                             font=("Arial", 16, "bold"), foreground="orange")
        rsi_label.pack(side=tk.LEFT, padx=10)

        # 状态和趋势
        ttk.Label(frame, textvariable=self.rsi_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.rsi_trend_var).pack(anchor=tk.W, padx=5)

        # 建议区域
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.rsi_advice_var, 
                 wraplength=250, justify=tk.LEFT).pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 详细信息（专业模式）
        self.rsi_detail_frame = ttk.LabelFrame(frame, text="🔧 详细信息")
        detail_text = tk.Text(self.rsi_detail_frame, height=2, wrap=tk.WORD, font=("Arial", 8))
        detail_text.pack(fill=tk.X, padx=5, pady=5)
        detail_text.insert(tk.END, "RSI详细分析：当前值65.2，距离超买4.8，距离超卖35.2")
        detail_text.config(state=tk.DISABLED)
    
    def create_kdj_module(self, parent, row, col):
        """创建KDJ模块"""
        frame = ttk.LabelFrame(parent, text="📊 KDJ随机指标")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)

        # KDJ值显示
        kdj_frame = ttk.Frame(frame)
        kdj_frame.pack(fill=tk.X, padx=5, pady=5)

        for label, var in [("K:", self.kdj_k_var), ("D:", self.kdj_d_var), ("J:", self.kdj_j_var)]:
            col_frame = ttk.Frame(kdj_frame)
            col_frame.pack(side=tk.LEFT, padx=10)
            ttk.Label(col_frame, text=label).pack()
            color = "red" if label == "J:" else "blue"
            ttk.Label(col_frame, textvariable=var, font=("Arial", 12, "bold"), 
                     foreground=color).pack()

        # 状态和信号
        ttk.Label(frame, textvariable=self.kdj_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.kdj_signal_var).pack(anchor=tk.W, padx=5)

        # 建议区域
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.kdj_advice_var, 
                 wraplength=250, justify=tk.LEFT).pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_macd_module(self, parent, row, col):
        """创建MACD模块"""
        frame = ttk.LabelFrame(parent, text="📈 MACD指数平滑移动平均")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)

        # MACD值显示
        value_frame = ttk.Frame(frame)
        value_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(value_frame, text="MACD:").pack(side=tk.LEFT)
        macd_label = ttk.Label(value_frame, textvariable=self.macd_value_var,
                              font=("Arial", 12, "bold"), foreground="green")
        macd_label.pack(side=tk.LEFT, padx=5)

        # 状态显示
        ttk.Label(frame, textvariable=self.macd_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.macd_trend_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.macd_cross_var).pack(anchor=tk.W, padx=5)

        # 建议区域
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.macd_advice_var, 
                 wraplength=250, justify=tk.LEFT).pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_bb_module(self, parent, row, col):
        """创建布林带模块"""
        frame = ttk.LabelFrame(parent, text="📊 布林带指标")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)

        # 布林带值显示
        bb_frame = ttk.Frame(frame)
        bb_frame.pack(fill=tk.X, padx=5, pady=5)

        for label, var in [("上轨:", self.bb_upper_var), ("中轨:", self.bb_middle_var), ("下轨:", self.bb_lower_var)]:
            col_frame = ttk.Frame(bb_frame)
            col_frame.pack(side=tk.LEFT, padx=5)
            ttk.Label(col_frame, text=label).pack()
            font_weight = "bold" if label == "中轨:" else "normal"
            ttk.Label(col_frame, textvariable=var, font=("Arial", 10, font_weight)).pack()

        # 状态显示
        ttk.Label(frame, textvariable=self.bb_position_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.bb_width_var).pack(anchor=tk.W, padx=5)

        # 建议区域
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.bb_advice_var, 
                 wraplength=250, justify=tk.LEFT).pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def toggle_mode(self):
        """切换模式"""
        is_beginner = self.beginner_mode_var.get()
        
        if hasattr(self, 'rsi_detail_frame'):
            if is_beginner:
                self.rsi_detail_frame.pack_forget()
            else:
                self.rsi_detail_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 更新滚动区域
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        mode_text = "新手模式" if is_beginner else "专业模式"
        self.status_var.set(f"🔰 {mode_text}")
        print(f"切换到{mode_text}")
    
    def refresh_data(self):
        """刷新数据"""
        self.generate_test_data()
        self.status_var.set("🟢 数据已刷新")
        print("数据已刷新")
    
    def show_help(self):
        """显示帮助"""
        print("显示帮助信息")
    
    def generate_test_data(self):
        """生成测试数据"""
        # 随机更新一些数值来测试显示
        rsi = random.uniform(30, 70)
        self.rsi_value_var.set(f"{rsi:.1f}")
        
        if rsi >= 60:
            self.rsi_status_var.set("🟡 偏强区域")
        elif rsi <= 40:
            self.rsi_status_var.set("🔵 偏弱区域")
        else:
            self.rsi_status_var.set("🟢 正常区域")
    
    def run(self):
        """运行测试"""
        print("🔧 启动增强技术指标面板布局测试...")
        print("测试功能：")
        print("1. 滚动条功能")
        print("2. 响应式布局")
        print("3. 模式切换")
        print("4. 内容完整显示")
        self.root.mainloop()

if __name__ == "__main__":
    test = EnhancedPanelLayoutTest()
    test.run()
