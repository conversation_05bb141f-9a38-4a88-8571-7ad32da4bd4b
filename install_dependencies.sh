#!/bin/bash

echo "=========================================="
echo "WMZC量化交易系统 - 依赖安装脚本"
echo "=========================================="
echo

echo "检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误：未找到Python3，请先安装Python 3.8+"
    exit 1
fi

echo
echo "升级pip..."
python3 -m pip install --upgrade pip

echo
echo "安装核心依赖..."
python3 -m pip install aiohttp>=3.8.0
python3 -m pip install pandas>=1.5.0
python3 -m pip install numpy>=1.21.0
python3 -m pip install websockets>=10.0
python3 -m pip install certifi>=2022.12.7
python3 -m pip install psutil>=5.9.0
python3 -m pip install aiosqlite>=0.17.0

echo
echo "检查tkinter支持..."
python3 -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告：tkinter未安装，尝试安装..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y python3-tk
    elif command -v yum &> /dev/null; then
        sudo yum install -y tkinter
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y python3-tkinter
    else
        echo "请手动安装tkinter包"
    fi
fi

echo
echo "=========================================="
echo "核心依赖安装完成！"
echo "=========================================="
echo

echo "是否安装可选功能依赖？(y/n)"
read -p "请选择: " choice
if [[ $choice == [Yy]* ]]; then
    echo
    echo "安装可选依赖..."
    python3 -m pip install tensorflow>=2.10.0
    python3 -m pip install scikit-learn>=1.1.0
    python3 -m pip install matplotlib>=3.5.0
    python3 -m pip install plotly>=5.10.0
    echo "可选依赖安装完成！"
fi

echo
echo "验证安装..."
python3 -c "import aiohttp, pandas, numpy, websockets, certifi, psutil; print('✅ 核心依赖验证成功！')"

echo
echo "=========================================="
echo "安装完成！现在可以运行WMZC系统了"
echo "运行命令: python3 run_wmzc.py"
echo "=========================================="
