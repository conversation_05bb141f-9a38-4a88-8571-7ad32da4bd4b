# WMZC量化交易系统 - 完整依赖清单
# 生成时间: 2024-12-19
# Python版本要求: Python 3.8+
# 系统架构: 100%异步 + 响应式GUI + 交易所分离

# ==================== 核心必需依赖 ====================
# 这些依赖是系统运行的基础，必须安装

# 异步HTTP客户端 - 用于交易所API调用
aiohttp>=3.8.0

# 数据处理和分析
pandas>=1.5.0
numpy>=1.21.0

# WebSocket通信 - 用于实时数据流
websockets>=10.0

# SSL证书验证
certifi>=2022.12.7

# 系统监控和性能分析
psutil>=5.9.0

# 异步SQLite数据库支持（可选但推荐）
aiosqlite>=0.17.0

# ==================== GUI界面依赖 ====================
# Tkinter是Python标准库的一部分，通常不需要单独安装
# 但在某些Linux发行版中可能需要单独安装
# Ubuntu/Debian: sudo apt-get install python3-tk
# CentOS/RHEL: sudo yum install tkinter
# 或者: sudo dnf install python3-tkinter

# ==================== 企业级功能依赖（可选） ====================
# 这些依赖提供高级功能，可根据需要选择性安装

# 分布式服务发现和配置管理
# python-consul>=1.1.0

# 深度学习和LSTM预测
# tensorflow>=2.10.0
# keras>=2.10.0

# 机器学习算法
# scikit-learn>=1.1.0

# 高性能数据处理
# numba>=0.56.0

# 分布式缓存（如果不使用内置缓存）
# redis>=4.3.0

# 高级数据可视化
# matplotlib>=3.5.0
# plotly>=5.10.0

# 技术指标计算库
# talib>=0.4.0

# 加密货币数据源
# ccxt>=2.0.0

# ==================== 开发和测试依赖（可选） ====================
# 用于开发、测试和代码质量检查

# 代码格式化
# black>=22.0.0
# autopep8>=1.7.0

# 代码质量检查
# pylint>=2.15.0
# flake8>=5.0.0

# 类型检查
# mypy>=0.991

# 测试框架
# pytest>=7.0.0
# pytest-asyncio>=0.19.0

# 性能分析
# memory-profiler>=0.60.0
# line-profiler>=3.5.0

# ==================== 安装命令 ====================

# 基础安装（最小依赖）：
# pip install aiohttp pandas numpy websockets certifi psutil aiosqlite

# 完整安装（包含所有功能）：
# pip install aiohttp pandas numpy websockets certifi psutil aiosqlite python-consul tensorflow scikit-learn numba redis matplotlib plotly

# 开发环境安装：
# pip install aiohttp pandas numpy websockets certifi psutil aiosqlite black pylint pytest pytest-asyncio

# ==================== 系统要求 ====================

# 操作系统：
# - Windows 10/11 (推荐)
# - macOS 10.15+
# - Ubuntu 18.04+
# - CentOS 7+

# 硬件要求：
# - CPU: 双核2.0GHz以上
# - 内存: 4GB以上（推荐8GB）
# - 存储: 1GB可用空间
# - 网络: 稳定的互联网连接

# Python版本：
# - Python 3.8+ (推荐Python 3.10+)
# - 支持asyncio和类型提示

# ==================== 特殊说明 ====================

# 1. Tkinter依赖：
#    - Windows: 通常随Python一起安装
#    - macOS: 通常随Python一起安装
#    - Linux: 可能需要单独安装python3-tk包

# 2. 数据库依赖：
#    - SQLite3: Python标准库，无需单独安装
#    - aiosqlite: 异步SQLite支持，强烈推荐安装

# 3. SSL/TLS支持：
#    - certifi: 提供可信的CA证书包
#    - ssl: Python标准库，用于HTTPS连接

# 4. 可选依赖说明：
#    - tensorflow: 用于LSTM深度学习预测（约500MB）
#    - scikit-learn: 用于传统机器学习算法
#    - redis: 用于分布式缓存（需要Redis服务器）
#    - talib: 用于技术指标计算（需要编译）

# ==================== 安装验证 ====================

# 验证安装是否成功，运行以下Python代码：
"""
import asyncio
import aiohttp
import pandas as pd
import numpy as np
import websockets
import certifi
import psutil
import tkinter as tk

print("✅ 所有核心依赖安装成功！")

# 检查可选依赖
optional_deps = {
    'aiosqlite': '异步SQLite支持',
    'tensorflow': 'LSTM深度学习',
    'sklearn': '机器学习算法',
    'redis': '分布式缓存',
    'matplotlib': '数据可视化'
}

for dep, desc in optional_deps.items():
    try:
        __import__(dep)
        print(f"✅ {dep} - {desc}")
    except ImportError:
        print(f"⚠️ {dep} - {desc} (未安装)")
"""

# ==================== 故障排除 ====================

# 常见问题及解决方案：

# 1. 安装失败：
#    - 升级pip: python -m pip install --upgrade pip
#    - 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 2. Tkinter导入错误：
#    - Linux: sudo apt-get install python3-tk
#    - 检查Python安装是否完整

# 3. 编译错误（如talib）：
#    - Windows: 安装Visual Studio Build Tools
#    - Linux: 安装build-essential
#    - macOS: 安装Xcode Command Line Tools

# 4. 网络连接问题：
#    - 检查防火墙设置
#    - 使用代理: pip install --proxy http://proxy:port package

# 5. 权限问题：
#    - 使用虚拟环境: python -m venv wmzc_env
#    - 激活环境: wmzc_env\Scripts\activate (Windows)

# ==================== 版本兼容性 ====================

# 测试过的Python版本：
# - Python 3.8.10 ✅
# - Python 3.9.16 ✅
# - Python 3.10.11 ✅ (推荐)
# - Python 3.11.5 ✅
# - Python 3.12.0 ✅

# 不支持的版本：
# - Python 3.7及以下 ❌ (缺少必要的asyncio功能)
# - Python 2.x ❌ (已停止支持)

# ==================== 更新说明 ====================

# 定期更新依赖以获得最新功能和安全修复：
# pip install --upgrade aiohttp pandas numpy websockets certifi psutil

# 检查过时的包：
# pip list --outdated

# 生成当前环境的requirements.txt：
# pip freeze > requirements.txt

# ==================== 联系信息 ====================

# 如果遇到依赖安装问题，请检查：
# 1. Python版本是否符合要求
# 2. 网络连接是否正常
# 3. 系统权限是否足够
# 4. 是否使用了虚拟环境

# 系统版本：WMZC v1.0.0
# 架构：100%异步 + 响应式GUI + 交易所分离
# 最后更新：2024-12-19
