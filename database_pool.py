#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - 企业级数据库连接池
实现真正的数据库连接池，支持连接复用、超时管理、连接检测、故障恢复
"""

import sqlite3
import threading
import time
import queue
import logging
from contextlib import contextmanager
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum
import weakref
import gc

class ConnectionState(Enum):
    """连接状态枚举"""
    IDLE = "idle"           # 空闲
    ACTIVE = "active"       # 活跃使用中
    INVALID = "invalid"     # 无效连接
    CLOSED = "closed"       # 已关闭

@dataclass
class ConnectionInfo:
    """连接信息"""
    connection: sqlite3.Connection
    created_time: float
    last_used_time: float
    use_count: int
    state: ConnectionState
    thread_id: Optional[int] = None

class DatabaseConnectionPool:
    """企业级数据库连接池"""
    
    def __init__(self, 
                 db_path: str,
                 min_connections: int = 2,
                 max_connections: int = 10,
                 max_idle_time: int = 300,  # 5分钟
                 connection_timeout: int = 30,
                 validation_query: str = "SELECT 1",
                 enable_monitoring: bool = True):
        """
        初始化连接池
        
        Args:
            db_path: 数据库文件路径
            min_connections: 最小连接数
            max_connections: 最大连接数
            max_idle_time: 最大空闲时间（秒）
            connection_timeout: 连接超时时间（秒）
            validation_query: 连接验证查询
            enable_monitoring: 是否启用监控
        """
        self.db_path = db_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        self.connection_timeout = connection_timeout
        self.validation_query = validation_query
        self.enable_monitoring = enable_monitoring
        
        # 连接池管理
        self._pool = queue.Queue(maxsize=max_connections)
        self._active_connections: Dict[int, ConnectionInfo] = {}
        self._connection_count = 0
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'total_created': 0,
            'total_closed': 0,
            'total_requests': 0,
            'total_hits': 0,
            'total_misses': 0,
            'total_timeouts': 0,
            'total_errors': 0
        }
        
        # 日志
        self.logger = logging.getLogger('DatabasePool')
        
        # 初始化连接池
        self._initialize_pool()
        
        # 启动监控线程
        if self.enable_monitoring:
            self._start_monitoring()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            # 创建最小连接数
            for _ in range(self.min_connections):
                conn_info = self._create_connection()
                if conn_info:
                    self._pool.put(conn_info)
            
            self.logger.info(f"数据库连接池初始化完成，初始连接数: {self.min_connections}")
            
        except Exception as e:
            self.logger.error(f"连接池初始化失败: {e}")
            raise
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """创建新连接"""
        try:
            with self._lock:
                if self._connection_count >= self.max_connections:
                    return None
                
                # 创建SQLite连接
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=self.connection_timeout,
                    check_same_thread=False,
                    isolation_level=None  # 自动提交模式
                )
                
                # 设置连接参数
                conn.execute("PRAGMA journal_mode=WAL")  # WAL模式提升并发性能
                conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全
                conn.execute("PRAGMA cache_size=10000")  # 增加缓存
                conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存
                
                # 创建连接信息
                conn_info = ConnectionInfo(
                    connection=conn,
                    created_time=time.time(),
                    last_used_time=time.time(),
                    use_count=0,
                    state=ConnectionState.IDLE
                )
                
                self._connection_count += 1
                self._stats['total_created'] += 1
                
                self.logger.debug(f"创建新数据库连接，当前连接数: {self._connection_count}")
                return conn_info
                
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {e}")
            self._stats['total_errors'] += 1
            return None
    
    def _validate_connection(self, conn_info: ConnectionInfo) -> bool:
        """验证连接有效性"""
        try:
            cursor = conn_info.connection.cursor()
            cursor.execute(self.validation_query)
            cursor.fetchone()
            cursor.close()
            return True
        except Exception as e:
            self.logger.warning(f"连接验证失败: {e}")
            return False
    
    def _close_connection(self, conn_info: ConnectionInfo):
        """关闭连接"""
        try:
            conn_info.state = ConnectionState.CLOSED
            conn_info.connection.close()
            
            with self._lock:
                self._connection_count -= 1
                self._stats['total_closed'] += 1
            
            self.logger.debug(f"关闭数据库连接，当前连接数: {self._connection_count}")
            
        except Exception as e:
            self.logger.error(f"关闭连接失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn_info = None
        start_time = time.time()
        
        try:
            self._stats['total_requests'] += 1
            
            # 尝试从池中获取连接
            try:
                conn_info = self._pool.get(timeout=self.connection_timeout)
                self._stats['total_hits'] += 1
                
                # 验证连接有效性
                if not self._validate_connection(conn_info):
                    self._close_connection(conn_info)
                    conn_info = self._create_connection()
                    if not conn_info:
                        raise Exception("无法创建新连接")
                
            except queue.Empty:
                # 池中无可用连接，尝试创建新连接
                self._stats['total_misses'] += 1
                conn_info = self._create_connection()
                if not conn_info:
                    self._stats['total_timeouts'] += 1
                    raise Exception("连接池已满，无法获取连接")
            
            # 更新连接信息
            conn_info.last_used_time = time.time()
            conn_info.use_count += 1
            conn_info.state = ConnectionState.ACTIVE
            conn_info.thread_id = threading.get_ident()
            
            # 记录活跃连接
            with self._lock:
                self._active_connections[id(conn_info)] = conn_info
            
            yield conn_info.connection
            
        except Exception as e:
            self.logger.error(f"获取数据库连接失败: {e}")
            self._stats['total_errors'] += 1
            raise
            
        finally:
            # 归还连接到池中
            if conn_info:
                try:
                    # 移除活跃连接记录
                    with self._lock:
                        self._active_connections.pop(id(conn_info), None)
                    
                    # 检查连接是否仍然有效
                    if (conn_info.state != ConnectionState.CLOSED and 
                        self._validate_connection(conn_info)):
                        
                        conn_info.state = ConnectionState.IDLE
                        conn_info.thread_id = None
                        
                        # 归还到池中
                        try:
                            self._pool.put_nowait(conn_info)
                        except queue.Full:
                            # 池已满，关闭连接
                            self._close_connection(conn_info)
                    else:
                        # 连接无效，关闭它
                        self._close_connection(conn_info)
                        
                except Exception as e:
                    self.logger.error(f"归还连接失败: {e}")
                    if conn_info:
                        self._close_connection(conn_info)
            
            # 记录性能统计
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # 超过1秒记录警告
                self.logger.warning(f"数据库连接获取耗时过长: {execution_time:.3f}秒")
    
    def execute_query(self, query: str, params: tuple = None) -> Any:
        """执行查询 - 优化的事务处理"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            is_write_operation = not query.strip().upper().startswith(('SELECT', 'PRAGMA', 'EXPLAIN'))

            try:
                # WAL模式下不需要显式BEGIN，使用自动提交模式
                # 只有在需要多语句事务时才使用显式事务
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # 根据查询类型返回结果
                if query.strip().upper().startswith(('SELECT', 'PRAGMA')):
                    return cursor.fetchall()
                else:
                    # 写操作在WAL模式下自动提交
                    return cursor.rowcount

            except Exception as e:
                # 记录错误信息
                if is_write_operation:
                    self.logger.error(f"数据库写操作失败: {query[:100]}..., 错误: {e}")
                else:
                    self.logger.error(f"数据库查询失败: {query[:100]}..., 错误: {e}")
                raise
            finally:
                cursor.close()
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """批量执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.executemany(query, params_list)
                return cursor.rowcount
            finally:
                cursor.close()

    def execute_transaction(self, queries: List[tuple]) -> bool:
        """执行事务 - 多个查询作为一个事务"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                # 显式开始事务
                conn.execute("BEGIN IMMEDIATE")

                for query_info in queries:
                    if isinstance(query_info, tuple) and len(query_info) == 2:
                        query, params = query_info
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query_info)

                # 提交事务
                conn.commit()
                return True

            except Exception as e:
                # 回滚事务
                try:
                    conn.rollback()
                    self.logger.error(f"事务已回滚，错误: {e}")
                except Exception as rollback_error:
                    self.logger.error(f"事务回滚失败: {rollback_error}")
                raise
            finally:
                cursor.close()
    
    def _start_monitoring(self):
        """启动监控线程"""
        def monitor_loop():
            while True:
                try:
                    self._cleanup_idle_connections()
                    self._log_statistics()
                    time.sleep(60)  # 每分钟检查一次
                except Exception as e:
                    self.logger.error(f"连接池监控异常: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        self.logger.info("连接池监控线程已启动")
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        connections_to_close = []
        
        # 检查池中的空闲连接
        temp_connections = []
        while not self._pool.empty():
            try:
                conn_info = self._pool.get_nowait()
                if (current_time - conn_info.last_used_time > self.max_idle_time and
                    self._connection_count > self.min_connections):
                    connections_to_close.append(conn_info)
                else:
                    temp_connections.append(conn_info)
            except queue.Empty:
                break
        
        # 将有效连接放回池中
        for conn_info in temp_connections:
            try:
                self._pool.put_nowait(conn_info)
            except queue.Full:
                connections_to_close.append(conn_info)
        
        # 关闭需要清理的连接
        for conn_info in connections_to_close:
            self._close_connection(conn_info)
        
        if connections_to_close:
            self.logger.info(f"清理了 {len(connections_to_close)} 个空闲连接")
    
    def _log_statistics(self):
        """记录统计信息"""
        with self._lock:
            active_count = len(self._active_connections)
            idle_count = self._pool.qsize()
            
        hit_rate = (self._stats['total_hits'] / max(self._stats['total_requests'], 1)) * 100
        
        self.logger.info(
            f"连接池统计 - "
            f"总连接: {self._connection_count}, "
            f"活跃: {active_count}, "
            f"空闲: {idle_count}, "
            f"命中率: {hit_rate:.1f}%, "
            f"总请求: {self._stats['total_requests']}, "
            f"错误: {self._stats['total_errors']}"
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            active_count = len(self._active_connections)
            idle_count = self._pool.qsize()
        
        hit_rate = (self._stats['total_hits'] / max(self._stats['total_requests'], 1)) * 100
        
        return {
            'total_connections': self._connection_count,
            'active_connections': active_count,
            'idle_connections': idle_count,
            'hit_rate': hit_rate,
            **self._stats
        }
    
    def close_all(self):
        """关闭所有连接"""
        self.logger.info("开始关闭连接池...")
        
        # 关闭池中的连接
        while not self._pool.empty():
            try:
                conn_info = self._pool.get_nowait()
                self._close_connection(conn_info)
            except queue.Empty:
                break
        
        # 关闭活跃连接
        with self._lock:
            for conn_info in list(self._active_connections.values()):
                self._close_connection(conn_info)
            self._active_connections.clear()
        
        self.logger.info("连接池已关闭")

# 全局连接池实例
_connection_pools: Dict[str, DatabaseConnectionPool] = {}
_pool_lock = threading.Lock()

def get_connection_pool(db_path: str, **kwargs) -> DatabaseConnectionPool:
    """获取连接池实例（单例模式）"""
    with _pool_lock:
        if db_path not in _connection_pools:
            _connection_pools[db_path] = DatabaseConnectionPool(db_path, **kwargs)
        return _connection_pools[db_path]

def close_all_pools():
    """关闭所有连接池"""
    with _pool_lock:
        for pool in _connection_pools.values():
            pool.close_all()
        _connection_pools.clear()

if __name__ == '__main__':
    # 测试连接池
    import tempfile
    import os
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        # 创建连接池
        pool = DatabaseConnectionPool(
            temp_db.name,
            min_connections=2,
            max_connections=5,
            enable_monitoring=True
        )
        
        # 测试连接池功能
        print("测试连接池功能...")
        
        # 创建测试表
        pool.execute_query('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT,
                value REAL
            )
        ''')
        
        # 插入测试数据
        test_data = [
            ('test1', 1.0),
            ('test2', 2.0),
            ('test3', 3.0)
        ]
        
        rows_affected = pool.execute_many(
            'INSERT INTO test_table (name, value) VALUES (?, ?)',
            test_data
        )
        print(f"插入了 {rows_affected} 行数据")
        
        # 查询数据
        results = pool.execute_query('SELECT * FROM test_table')
        print(f"查询结果: {results}")
        
        # 显示统计信息
        stats = pool.get_statistics()
        print(f"连接池统计: {stats}")
        
        # 测试并发访问
        import concurrent.futures
        
        def concurrent_query(i):
            return pool.execute_query('SELECT COUNT(*) FROM test_table')
        
        print("\n测试并发访问...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(concurrent_query, i) for i in range(20)]
            results = [future.result() for future in futures]
        
        print(f"并发查询完成，结果数量: {len(results)}")
        
        # 最终统计
        final_stats = pool.get_statistics()
        print(f"最终统计: {final_stats}")
        
        # 关闭连接池
        pool.close_all()
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_db.name)
        except:
            pass
