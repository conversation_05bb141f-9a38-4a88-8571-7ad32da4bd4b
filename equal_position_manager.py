#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC量化交易系统 - 等量加仓管理器
实现等量加仓策略的监控、触发和风险控制功能
"""

import asyncio
import logging
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import threading
import time

class AddPositionStatus(Enum):
    """加仓状态枚举"""
    WAITING = "waiting"          # 等待触发
    TRIGGERED = "triggered"      # 已触发
    EXECUTED = "executed"        # 已执行
    FAILED = "failed"           # 执行失败
    CANCELLED = "cancelled"      # 已取消

@dataclass
class PositionInfo:
    """持仓信息"""
    strategy_name: str
    symbol: str
    entry_price: float
    position_size: float
    stop_loss: float
    take_profit: float
    timestamp: datetime
    position_id: str

@dataclass
class AddPositionRecord:
    """加仓记录"""
    id: int
    strategy_name: str
    symbol: str
    original_entry: float
    add_price: float
    add_size: float
    trigger_distance: float
    new_avg_cost: float
    new_stop_loss: float
    new_take_profit: float
    status: AddPositionStatus
    timestamp: datetime
    result_message: str = ""

class EqualPositionManager:
    """等量加仓管理器"""
    
    def __init__(self, db_manager=None, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # 配置参数
        self.config = {
            'enabled': False,                    # 是否启用等量加仓
            'trigger_distance_min': 1,           # 最小触发距离(点)
            'trigger_distance_max': 5,           # 最大触发距离(点)
            'trigger_distance_default': 2,      # 默认触发距离(点)
            'max_add_times': 1,                 # 最大加仓次数
            'max_total_risk_pct': 0.05,         # 最大总风险比例(5%)
            'emergency_stop': False,             # 紧急停止开关
            'half_size_enabled': False,          # 半量加仓开关
        }
        
        # 监控的策略列表
        self.monitored_strategies = set()
        
        # 当前持仓信息
        self.current_positions = {}  # {strategy_name: PositionInfo}
        
        # 加仓记录
        self.add_position_records = []
        
        # 加仓计数器
        self.add_position_counts = {}  # {strategy_name: count}
        
        # 监控线程
        self.monitor_thread = None
        self.is_monitoring = False
        
        # 初始化数据库
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        try:
            if not self.db_manager:
                return
                
            # 创建等量加仓配置表
            self.db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS equal_position_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_data TEXT NOT NULL,
                    monitored_strategies TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建加仓记录表
            self.db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS equal_position_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    original_entry REAL NOT NULL,
                    add_price REAL NOT NULL,
                    add_size REAL NOT NULL,
                    trigger_distance REAL NOT NULL,
                    new_avg_cost REAL NOT NULL,
                    new_stop_loss REAL NOT NULL,
                    new_take_profit REAL NOT NULL,
                    status TEXT NOT NULL,
                    result_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建持仓监控表
            self.db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS position_monitor (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    entry_price REAL NOT NULL,
                    position_size REAL NOT NULL,
                    stop_loss REAL NOT NULL,
                    take_profit REAL NOT NULL,
                    position_id TEXT UNIQUE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.logger.info("等量加仓数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化等量加仓数据库表失败: {e}")

    async def load_config(self):
        """加载配置"""
        try:
            if not self.db_manager:
                return
                
            result = self.db_manager.fetch_one(
                "SELECT config_data, monitored_strategies FROM equal_position_config ORDER BY id DESC LIMIT 1"
            )
            
            if result:
                # 加载配置数据
                config_data = json.loads(result[0])
                self.config.update(config_data)
                
                # 加载监控策略列表
                if result[1]:
                    monitored_list = json.loads(result[1])
                    self.monitored_strategies = set(monitored_list)
                
                self.logger.info("等量加仓配置加载完成")
            else:
                self.logger.info("未找到等量加仓配置，使用默认设置")
                
        except Exception as e:
            self.logger.error(f"加载等量加仓配置失败: {e}")

    async def save_config(self):
        """保存配置"""
        try:
            if not self.db_manager:
                return False
                
            config_json = json.dumps(self.config)
            strategies_json = json.dumps(list(self.monitored_strategies))
            
            self.db_manager.execute_query('''
                INSERT OR REPLACE INTO equal_position_config 
                (id, config_data, monitored_strategies, updated_at)
                VALUES (1, ?, ?, CURRENT_TIMESTAMP)
            ''', (config_json, strategies_json))
            
            self.logger.info("等量加仓配置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存等量加仓配置失败: {e}")
            return False

    def add_monitored_strategy(self, strategy_name: str):
        """添加监控策略"""
        try:
            self.monitored_strategies.add(strategy_name)
            self.logger.info(f"添加监控策略: {strategy_name}")
        except Exception as e:
            self.logger.error(f"添加监控策略失败: {e}")

    def remove_monitored_strategy(self, strategy_name: str):
        """移除监控策略"""
        try:
            self.monitored_strategies.discard(strategy_name)
            # 清理相关数据
            if strategy_name in self.current_positions:
                del self.current_positions[strategy_name]
            if strategy_name in self.add_position_counts:
                del self.add_position_counts[strategy_name]
            self.logger.info(f"移除监控策略: {strategy_name}")
        except Exception as e:
            self.logger.error(f"移除监控策略失败: {e}")

    def update_position(self, strategy_name: str, position_info: PositionInfo):
        """更新持仓信息"""
        try:
            if strategy_name in self.monitored_strategies:
                self.current_positions[strategy_name] = position_info
                self.logger.debug(f"更新持仓信息: {strategy_name} @ {position_info.entry_price}")
        except Exception as e:
            self.logger.error(f"更新持仓信息失败: {e}")

    def check_add_position_trigger(self, strategy_name: str, current_price: float) -> bool:
        """检查是否触发等量加仓条件"""
        try:
            # 检查基本条件
            if not self.config['enabled']:
                return False
                
            if self.config['emergency_stop']:
                return False
                
            if strategy_name not in self.monitored_strategies:
                return False
                
            if strategy_name not in self.current_positions:
                return False
            
            # 检查加仓次数限制
            current_count = self.add_position_counts.get(strategy_name, 0)
            if current_count >= self.config['max_add_times']:
                return False
            
            # 获取持仓信息
            position = self.current_positions[strategy_name]
            
            # 计算距离止损价的距离
            if position.entry_price > position.stop_loss:  # 做多
                distance_to_stop = abs(current_price - position.stop_loss)
                price_points = distance_to_stop
            else:  # 做空
                distance_to_stop = abs(current_price - position.stop_loss)
                price_points = distance_to_stop
            
            # 检查是否在触发范围内
            trigger_distance = self.config['trigger_distance_default']
            if price_points <= trigger_distance:
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"检查加仓触发条件失败: {e}")
            return False

    async def execute_add_position(self, strategy_name: str, current_price: float) -> bool:
        """执行等量加仓"""
        try:
            if strategy_name not in self.current_positions:
                return False
            
            position = self.current_positions[strategy_name]
            
            # 计算加仓参数 - 支持半量加仓
            base_add_size = position.position_size  # 基础等量加仓

            # 检查是否启用半量加仓
            if self.config.get('half_size_enabled', False):
                add_size = base_add_size * 0.5  # 半量加仓：50%
                add_type = "半量加仓"
            else:
                add_size = base_add_size  # 等量加仓：100%
                add_type = "等量加仓"
            
            # 计算新的平均成本
            total_size = position.position_size + add_size
            new_avg_cost = (position.entry_price * position.position_size + 
                          current_price * add_size) / total_size
            
            # 重新计算止损止盈
            risk_pct = self.config['max_total_risk_pct']
            if position.entry_price > position.stop_loss:  # 做多
                new_stop_loss = new_avg_cost * (1 - risk_pct)
                new_take_profit = new_avg_cost * (1 + risk_pct * 2)  # 1:2风险收益比
            else:  # 做空
                new_stop_loss = new_avg_cost * (1 + risk_pct)
                new_take_profit = new_avg_cost * (1 - risk_pct * 2)
            
            # 创建加仓记录
            record = AddPositionRecord(
                id=0,
                strategy_name=strategy_name,
                symbol=position.symbol,
                original_entry=position.entry_price,
                add_price=current_price,
                add_size=add_size,
                trigger_distance=abs(current_price - position.stop_loss),
                new_avg_cost=new_avg_cost,
                new_stop_loss=new_stop_loss,
                new_take_profit=new_take_profit,
                status=AddPositionStatus.EXECUTED,
                timestamp=datetime.now(),
                result_message=f"{add_type}执行成功"  # 显示具体的加仓类型
            )
            
            # 保存记录
            await self.save_add_position_record(record)
            
            # 更新持仓信息
            position.entry_price = new_avg_cost
            position.position_size = total_size
            position.stop_loss = new_stop_loss
            position.take_profit = new_take_profit
            
            # 更新加仓计数
            self.add_position_counts[strategy_name] = self.add_position_counts.get(strategy_name, 0) + 1
            
            self.logger.info(f"{add_type}执行成功: {strategy_name} @ {current_price} (加仓数量: {add_size})")
            return True
            
        except Exception as e:
            self.logger.error(f"执行等量加仓失败: {e}")
            return False

    async def save_add_position_record(self, record: AddPositionRecord):
        """保存加仓记录"""
        try:
            if not self.db_manager:
                return
                
            self.db_manager.execute_query('''
                INSERT INTO equal_position_records 
                (strategy_name, symbol, original_entry, add_price, add_size, 
                 trigger_distance, new_avg_cost, new_stop_loss, new_take_profit, 
                 status, result_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.strategy_name, record.symbol, record.original_entry,
                record.add_price, record.add_size, record.trigger_distance,
                record.new_avg_cost, record.new_stop_loss, record.new_take_profit,
                record.status.value, record.result_message
            ))
            
            self.add_position_records.append(record)
            
        except Exception as e:
            self.logger.error(f"保存加仓记录失败: {e}")

    async def get_add_position_history(self, limit: int = 100) -> List[AddPositionRecord]:
        """获取加仓历史记录"""
        try:
            if not self.db_manager:
                return []
                
            results = self.db_manager.fetch_all('''
                SELECT id, strategy_name, symbol, original_entry, add_price, add_size,
                       trigger_distance, new_avg_cost, new_stop_loss, new_take_profit,
                       status, result_message, created_at
                FROM equal_position_records 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            records = []
            for row in results:
                record = AddPositionRecord(
                    id=row[0],
                    strategy_name=row[1],
                    symbol=row[2],
                    original_entry=row[3],
                    add_price=row[4],
                    add_size=row[5],
                    trigger_distance=row[6],
                    new_avg_cost=row[7],
                    new_stop_loss=row[8],
                    new_take_profit=row[9],
                    status=AddPositionStatus(row[10]),
                    timestamp=datetime.fromisoformat(row[12]),
                    result_message=row[11] or ""
                )
                records.append(record)
            
            return records
            
        except Exception as e:
            self.logger.error(f"获取加仓历史失败: {e}")
            return []

    def start_monitoring(self):
        """开始监控"""
        try:
            if self.is_monitoring:
                return
                
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            self.logger.info("等量加仓监控已启动")
            
        except Exception as e:
            self.logger.error(f"启动等量加仓监控失败: {e}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
                
            self.logger.info("等量加仓监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止等量加仓监控失败: {e}")

    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 这里应该获取实时价格并检查触发条件
                # 由于需要与交易系统集成，这里只是框架
                time.sleep(1)  # 1秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        try:
            return {
                'enabled': self.config['enabled'],
                'emergency_stop': self.config['emergency_stop'],
                'monitored_strategies_count': len(self.monitored_strategies),
                'monitored_strategies': list(self.monitored_strategies),
                'current_positions_count': len(self.current_positions),
                'total_add_positions': sum(self.add_position_counts.values()),
                'is_monitoring': self.is_monitoring,
                'config': self.config.copy()
            }
        except Exception as e:
            self.logger.error(f"获取状态摘要失败: {e}")
            return {}
