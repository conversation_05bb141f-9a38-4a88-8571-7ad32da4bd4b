#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接管理修复效果
"""

import os
import tempfile
import threading
import time
import concurrent.futures

def test_safe_database_connection():
    """测试SafeDatabaseConnection上下文管理器"""
    print("🔍 测试SafeDatabaseConnection上下文管理器...")
    
    # 创建临时数据库文件
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        from asp import SafeDatabaseConnection
        
        # 测试正常使用
        with SafeDatabaseConnection(test_db_path) as (conn, cursor):
            cursor.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)")
            cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test_data",))
            cursor.execute("SELECT * FROM test_table")
            result = cursor.fetchall()
            assert len(result) == 1, "应该有一条记录"
        
        # 测试异常情况下的资源清理
        try:
            with SafeDatabaseConnection(test_db_path) as (conn, cursor):
                cursor.execute("SELECT * FROM test_table")
                raise Exception("模拟异常")
        except Exception as e:
            if "模拟异常" not in str(e):
                raise e
        
        # 检查连接统计
        stats = SafeDatabaseConnection.get_connection_stats()
        print(f"连接统计: {stats}")
        
        print("✅ SafeDatabaseConnection测试通过")
        return True
        
    except Exception as e:
        print(f"❌ SafeDatabaseConnection测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(test_db_path)
        except:
            pass

def test_concurrent_database_access():
    """测试并发数据库访问"""
    print("🔍 测试并发数据库访问...")
    
    # 创建临时数据库文件
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        from asp import SafeDatabaseConnection
        
        # 初始化数据库
        with SafeDatabaseConnection(test_db_path) as (conn, cursor):
            cursor.execute("CREATE TABLE IF NOT EXISTS concurrent_test (id INTEGER PRIMARY KEY, thread_id TEXT, timestamp REAL)")
        
        def worker_function(thread_id):
            """工作线程函数"""
            try:
                with SafeDatabaseConnection(test_db_path) as (conn, cursor):
                    cursor.execute("INSERT INTO concurrent_test (thread_id, timestamp) VALUES (?, ?)", 
                                 (f"thread_{thread_id}", time.time()))
                    cursor.execute("SELECT COUNT(*) FROM concurrent_test WHERE thread_id = ?", 
                                 (f"thread_{thread_id}",))
                    result = cursor.fetchone()
                    return result[0] if result else 0
            except Exception as e:
                print(f"线程{thread_id}执行失败: {e}")
                return 0
        
        # 使用多线程并发访问数据库
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(worker_function, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # 验证结果
        successful_operations = sum(results)
        print(f"成功的数据库操作: {successful_operations}/10")
        
        # 检查最终数据库状态
        with SafeDatabaseConnection(test_db_path) as (conn, cursor):
            cursor.execute("SELECT COUNT(*) FROM concurrent_test")
            total_records = cursor.fetchone()[0]
            print(f"数据库中总记录数: {total_records}")
        
        # 检查连接统计
        stats = SafeDatabaseConnection.get_connection_stats()
        print(f"并发测试后连接统计: {stats}")
        
        if successful_operations >= 8:  # 允许少量失败
            print("✅ 并发数据库访问测试通过")
            return True
        else:
            print("❌ 并发数据库访问测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 并发数据库访问测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(test_db_path)
        except:
            pass

def test_database_manager_health_check():
    """测试DatabaseManager健康检查功能"""
    print("🔍 测试DatabaseManager健康检查功能...")
    
    try:
        from asp import DatabaseManager
        
        # 创建临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db_path = tmp_file.name
        
        try:
            # 创建DatabaseManager实例
            db_manager = DatabaseManager(test_db_path)
            
            # 测试健康检查
            health_status = db_manager.check_connection_health()
            print(f"数据库健康状态: {health_status}")
            
            # 测试连接统计
            stats = db_manager.get_connection_stats()
            print(f"DatabaseManager统计: {stats}")
            
            # 清理
            db_manager.close()
            
            if health_status:
                print("✅ DatabaseManager健康检查测试通过")
                return True
            else:
                print("❌ DatabaseManager健康检查测试失败")
                return False
                
        finally:
            # 清理临时文件
            try:
                os.unlink(test_db_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ DatabaseManager健康检查测试失败: {e}")
        return False

def test_resource_cleanup():
    """测试资源清理"""
    print("🔍 测试资源清理...")
    
    try:
        from asp import SafeDatabaseConnection
        
        # 创建临时数据库文件
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db_path = tmp_file.name
        
        try:
            # 记录初始连接统计
            initial_stats = SafeDatabaseConnection.get_connection_stats()
            initial_active = initial_stats['active_connections']
            
            # 创建多个连接并确保它们被正确清理
            for i in range(5):
                with SafeDatabaseConnection(test_db_path) as (conn, cursor):
                    cursor.execute("SELECT 1")
            
            # 检查最终连接统计
            final_stats = SafeDatabaseConnection.get_connection_stats()
            final_active = final_stats['active_connections']
            
            print(f"初始活跃连接: {initial_active}")
            print(f"最终活跃连接: {final_active}")
            print(f"总连接数: {final_stats['total_connections']}")
            print(f"成功率: {final_stats['success_rate']:.2f}%")
            
            # 验证所有连接都被正确清理
            if final_active == initial_active:
                print("✅ 资源清理测试通过")
                return True
            else:
                print("❌ 资源清理测试失败，存在连接泄漏")
                return False
                
        finally:
            # 清理临时文件
            try:
                os.unlink(test_db_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 资源清理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始测试数据库连接管理修复效果...")
    
    # 测试SafeDatabaseConnection
    test1 = test_safe_database_connection()
    
    print("\n" + "="*50 + "\n")
    
    # 测试并发访问
    test2 = test_concurrent_database_access()
    
    print("\n" + "="*50 + "\n")
    
    # 测试DatabaseManager健康检查
    test3 = test_database_manager_health_check()
    
    print("\n" + "="*50 + "\n")
    
    # 测试资源清理
    test4 = test_resource_cleanup()
    
    print("\n" + "="*50 + "\n")
    
    if all([test1, test2, test3, test4]):
        print("🎉 所有测试通过！数据库连接管理问题已修复！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
