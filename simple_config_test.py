#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的配置管理器测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    try:
        print("测试导入JSONConfigManager...")
        from asp import JSONConfigManager
        print("✅ JSONConfigManager导入成功")
        
        print("测试创建实例...")
        config = JSONConfigManager("test_config.json")
        print("✅ JSONConfigManager实例创建成功")
        
        print("测试基本配置...")
        config.set_config("test", "key", "value")
        value = config.get_config("test", "key")
        print(f"✅ 配置测试成功: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始简单配置测试...")
    success = test_import()
    
    if success:
        print("🎉 简单测试通过！")
    else:
        print("⚠️ 测试失败")
