#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易按钮修复效果
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置加载"""
    print("🔍 测试配置加载...")
    
    try:
        from asp import JSONConfigManager
        
        config = JSONConfigManager("wmzc_config.json")
        
        # 测试加载OKX凭证
        okx_creds = config.load_api_credentials("OKX")
        
        if okx_creds and okx_creds.get('api_key'):
            print("✅ OKX API凭证加载成功")
            print(f"   API Key: {okx_creds['api_key'][:10]}...")
            print(f"   状态: {'启用' if okx_creds.get('enabled') else '禁用'}")
            return True
        else:
            print("❌ OKX API凭证未配置或加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def test_auto_connect_logic():
    """测试自动连接逻辑"""
    print("\n🔍 测试自动连接逻辑...")
    
    try:
        from asp import JSONConfigManager
        
        config = JSONConfigManager("wmzc_config.json")
        
        # 检查是否满足自动连接条件
        okx_creds = config.load_api_credentials("OKX")
        
        auto_connect_possible = (
            okx_creds and 
            okx_creds.get('enabled', False) and 
            okx_creds.get('api_key') and 
            okx_creds.get('secret_key')
        )
        
        if auto_connect_possible:
            print("✅ 满足自动连接条件")
            print("   系统启动时会提示用户是否自动连接")
            return True
        else:
            print("❌ 不满足自动连接条件")
            print("   用户需要手动配置API凭证")
            return False
            
    except Exception as e:
        print(f"❌ 自动连接逻辑测试失败: {e}")
        return False

def simulate_trading_button_click():
    """模拟交易按钮点击"""
    print("\n🔍 模拟交易按钮点击逻辑...")
    
    # 模拟检查条件
    exchange_connected = False  # 模拟未连接状态
    strategies_configured = False  # 模拟策略未配置
    
    print("📋 交易按钮前置条件检查:")
    
    # 检查1：交易所连接
    if not exchange_connected:
        print("   ❌ 交易所未连接")
        print("   → 系统会显示友好的连接提示对话框")
        print("   → 用户可选择跳转到配置页面")
        return False
    else:
        print("   ✅ 交易所已连接")
    
    # 检查2：策略配置
    if not strategies_configured:
        print("   ❌ 交易策略未配置")
        print("   → 系统会提示用户配置策略")
        return False
    else:
        print("   ✅ 交易策略已配置")
    
    print("   ✅ 所有前置条件满足，可以开始交易")
    return True

def provide_user_guide():
    """提供用户指南"""
    print("\n📖 用户操作指南:")
    print("=" * 50)
    
    print("🎯 要使'开始交易'按钮正常工作，请按以下步骤操作:")
    print()
    
    print("步骤1️⃣ 启动系统")
    print("   • 运行 python run_wmzc.py")
    print("   • 等待系统完全加载")
    print("   • 如果有保存的API凭证，系统会询问是否自动连接")
    print()
    
    print("步骤2️⃣ 配置API凭证（如果还没有）")
    print("   • 在主配置页面输入API凭证")
    print("   • 系统会自动加密保存")
    print()
    
    print("步骤3️⃣ 连接交易所")
    print("   • 点击'连接交易所'按钮")
    print("   • 等待连接成功提示")
    print("   • 状态栏显示'已连接到XXX'")
    print()
    
    print("步骤4️⃣ 配置交易策略")
    print("   • 切换到RSI策略或MACD策略页面")
    print("   • 启用至少一个策略")
    print("   • 设置合适的参数")
    print()
    
    print("步骤5️⃣ 开始交易")
    print("   • 点击'开始交易'按钮")
    print("   • 确认交易参数")
    print("   • 开始自动交易")
    print()
    
    print("💡 新增功能:")
    print("   • 自动连接提示：如果有保存的凭证，系统启动时会询问是否自动连接")
    print("   • 友好错误提示：点击'开始交易'时会给出详细的操作指导")
    print("   • 快速跳转：可以直接跳转到配置页面")

def main():
    """主函数"""
    print("🎯 WMZC量化交易系统 - 交易按钮修复测试")
    print("=" * 60)
    
    try:
        # 测试配置加载
        config_test = test_config_loading()
        
        # 测试自动连接逻辑
        auto_connect_test = test_auto_connect_logic()
        
        # 模拟交易按钮点击
        button_test = simulate_trading_button_click()
        
        # 提供用户指南
        provide_user_guide()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   配置加载: {'✅ 通过' if config_test else '❌ 失败'}")
        print(f"   自动连接: {'✅ 可用' if auto_connect_test else '⚠️ 需要配置'}")
        print(f"   按钮逻辑: {'✅ 正常' if button_test else '⚠️ 需要连接交易所'}")
        
        print("\n🎉 修复效果:")
        print("   • 添加了自动连接提示功能")
        print("   • 改进了错误提示信息")
        print("   • 提供了操作指导")
        print("   • 支持快速跳转到配置页面")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
