@echo off
echo ==========================================
echo WMZC量化交易系统 - 依赖安装脚本
echo ==========================================
echo.

echo 检查Python版本...
python --version
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装核心依赖...
pip install aiohttp>=3.8.0
pip install pandas>=1.5.0
pip install numpy>=1.21.0
pip install websockets>=10.0
pip install certifi>=2022.12.7
pip install psutil>=5.9.0
pip install aiosqlite>=0.17.0

echo.
echo ==========================================
echo 核心依赖安装完成！
echo ==========================================
echo.

echo 是否安装可选功能依赖？(y/n)
set /p choice=请选择: 
if /i "%choice%"=="y" (
    echo.
    echo 安装可选依赖...
    pip install tensorflow>=2.10.0
    pip install scikit-learn>=1.1.0
    pip install matplotlib>=3.5.0
    pip install plotly>=5.10.0
    echo 可选依赖安装完成！
)

echo.
echo 验证安装...
python -c "import aiohttp, pandas, numpy, websockets, certifi, psutil; print('✅ 核心依赖验证成功！')"

echo.
echo ==========================================
echo 安装完成！现在可以运行WMZC系统了
echo 运行命令: python run_wmzc.py
echo ==========================================
pause
