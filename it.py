#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统策略信息模块 - it.py
包含所有策略的详细信息和配置参数
"""

# 策略信息字典 - 包含18个完整策略
STRATEGY_INFO = {
    "professional_rsi": {
        "name": "专业RSI策略",
        "description": "基于RSI指标的专业级交易策略，适用于震荡市场",
        "win_rate": 85.2,
        "max_drawdown": 12.5,
        "annual_return": 45.8,
        "sharpe_ratio": 2.1,
        "parameters": {
            "rsi_period": 14,
            "overbought": 70,
            "oversold": 30,
            "stop_loss": 2.0,
            "take_profit": 4.0
        },
        "category": "技术指标",
        "risk_level": "中等",
        "suitable_market": "震荡市"
    },
    
    "advanced_macd": {
        "name": "高级MACD策略",
        "description": "基于MACD指标的高级交易策略，适用于趋势市场",
        "win_rate": 82.7,
        "max_drawdown": 15.3,
        "annual_return": 52.1,
        "sharpe_ratio": 1.9,
        "parameters": {
            "fast_period": 12,
            "slow_period": 26,
            "signal_period": 9,
            "stop_loss": 2.5,
            "take_profit": 5.0
        },
        "category": "技术指标",
        "risk_level": "中等",
        "suitable_market": "趋势市"
    },
    
    "bollinger_bands": {
        "name": "布林带策略",
        "description": "基于布林带的均值回归策略",
        "win_rate": 78.9,
        "max_drawdown": 18.2,
        "annual_return": 38.6,
        "sharpe_ratio": 1.7,
        "parameters": {
            "period": 20,
            "std_dev": 2.0,
            "stop_loss": 3.0,
            "take_profit": 3.5
        },
        "category": "技术指标",
        "risk_level": "中等",
        "suitable_market": "震荡市"
    },
    
    "kdj_strategy": {
        "name": "KDJ随机指标策略",
        "description": "基于KDJ指标的超买超卖策略",
        "win_rate": 76.4,
        "max_drawdown": 20.1,
        "annual_return": 41.3,
        "sharpe_ratio": 1.6,
        "parameters": {
            "k_period": 9,
            "d_period": 3,
            "j_period": 3,
            "overbought": 80,
            "oversold": 20
        },
        "category": "技术指标",
        "risk_level": "中等",
        "suitable_market": "震荡市"
    },
    
    "moving_average_cross": {
        "name": "均线交叉策略",
        "description": "基于双均线交叉的趋势跟踪策略",
        "win_rate": 73.8,
        "max_drawdown": 22.7,
        "annual_return": 35.9,
        "sharpe_ratio": 1.5,
        "parameters": {
            "fast_ma": 10,
            "slow_ma": 30,
            "stop_loss": 2.8,
            "take_profit": 4.2
        },
        "category": "趋势跟踪",
        "risk_level": "中等",
        "suitable_market": "趋势市"
    },
    
    "volume_price_trend": {
        "name": "量价趋势策略",
        "description": "结合成交量和价格的趋势确认策略",
        "win_rate": 80.3,
        "max_drawdown": 16.8,
        "annual_return": 47.2,
        "sharpe_ratio": 1.8,
        "parameters": {
            "volume_ma": 20,
            "price_ma": 15,
            "volume_threshold": 1.5,
            "stop_loss": 2.2,
            "take_profit": 4.8
        },
        "category": "量价分析",
        "risk_level": "中等",
        "suitable_market": "全市场"
    },
    
    "momentum_strategy": {
        "name": "动量策略",
        "description": "基于价格动量的短期交易策略",
        "win_rate": 71.6,
        "max_drawdown": 25.4,
        "annual_return": 33.7,
        "sharpe_ratio": 1.4,
        "parameters": {
            "momentum_period": 10,
            "threshold": 0.02,
            "stop_loss": 3.5,
            "take_profit": 3.0
        },
        "category": "动量策略",
        "risk_level": "较高",
        "suitable_market": "波动市"
    },
    
    "mean_reversion": {
        "name": "均值回归策略",
        "description": "基于价格均值回归的反转策略",
        "win_rate": 77.2,
        "max_drawdown": 19.6,
        "annual_return": 39.4,
        "sharpe_ratio": 1.6,
        "parameters": {
            "lookback_period": 20,
            "deviation_threshold": 2.0,
            "stop_loss": 2.5,
            "take_profit": 4.0
        },
        "category": "均值回归",
        "risk_level": "中等",
        "suitable_market": "震荡市"
    },
    
    "breakout_strategy": {
        "name": "突破策略",
        "description": "基于价格突破的趋势跟踪策略",
        "win_rate": 69.8,
        "max_drawdown": 28.3,
        "annual_return": 31.5,
        "sharpe_ratio": 1.3,
        "parameters": {
            "breakout_period": 20,
            "volume_confirm": True,
            "stop_loss": 4.0,
            "take_profit": 6.0
        },
        "category": "突破策略",
        "risk_level": "较高",
        "suitable_market": "趋势市"
    },
    
    "grid_trading": {
        "name": "网格交易策略",
        "description": "基于网格的区间交易策略",
        "win_rate": 83.5,
        "max_drawdown": 14.2,
        "annual_return": 42.8,
        "sharpe_ratio": 2.0,
        "parameters": {
            "grid_size": 0.01,
            "grid_levels": 10,
            "base_amount": 100,
            "stop_loss": 5.0
        },
        "category": "网格策略",
        "risk_level": "中等",
        "suitable_market": "震荡市"
    },
    
    "arbitrage_strategy": {
        "name": "套利策略",
        "description": "基于价差的套利交易策略",
        "win_rate": 88.9,
        "max_drawdown": 8.7,
        "annual_return": 28.3,
        "sharpe_ratio": 2.4,
        "parameters": {
            "spread_threshold": 0.005,
            "correlation_period": 30,
            "hedge_ratio": 1.0,
            "stop_loss": 1.5
        },
        "category": "套利策略",
        "risk_level": "较低",
        "suitable_market": "全市场"
    },
    
    "scalping_strategy": {
        "name": "剥头皮策略",
        "description": "基于短期价格波动的高频交易策略",
        "win_rate": 65.4,
        "max_drawdown": 32.1,
        "annual_return": 58.7,
        "sharpe_ratio": 1.2,
        "parameters": {
            "tick_size": 0.0001,
            "holding_time": 60,
            "profit_target": 0.002,
            "stop_loss": 0.001
        },
        "category": "高频策略",
        "risk_level": "高",
        "suitable_market": "高波动市"
    },
    
    "swing_trading": {
        "name": "波段交易策略",
        "description": "基于中期趋势的波段交易策略",
        "win_rate": 74.7,
        "max_drawdown": 21.5,
        "annual_return": 36.2,
        "sharpe_ratio": 1.5,
        "parameters": {
            "swing_period": 5,
            "trend_filter": True,
            "stop_loss": 3.0,
            "take_profit": 5.5
        },
        "category": "波段策略",
        "risk_level": "中等",
        "suitable_market": "趋势市"
    },
    
    "pairs_trading": {
        "name": "配对交易策略",
        "description": "基于相关性的配对交易策略",
        "win_rate": 81.2,
        "max_drawdown": 17.3,
        "annual_return": 34.6,
        "sharpe_ratio": 1.7,
        "parameters": {
            "correlation_threshold": 0.8,
            "zscore_entry": 2.0,
            "zscore_exit": 0.5,
            "stop_loss": 4.0
        },
        "category": "统计套利",
        "risk_level": "中等",
        "suitable_market": "全市场"
    },
    
    "news_sentiment": {
        "name": "新闻情绪策略",
        "description": "基于新闻情绪分析的交易策略",
        "win_rate": 68.3,
        "max_drawdown": 26.8,
        "annual_return": 29.7,
        "sharpe_ratio": 1.3,
        "parameters": {
            "sentiment_threshold": 0.6,
            "news_sources": ["twitter", "reddit", "news"],
            "holding_period": 240,
            "stop_loss": 3.5
        },
        "category": "情绪策略",
        "risk_level": "较高",
        "suitable_market": "新闻驱动市"
    },
    
    "machine_learning": {
        "name": "机器学习策略",
        "description": "基于机器学习模型的预测策略",
        "win_rate": 79.6,
        "max_drawdown": 18.9,
        "annual_return": 48.3,
        "sharpe_ratio": 1.8,
        "parameters": {
            "model_type": "random_forest",
            "features": 20,
            "lookback_period": 100,
            "retrain_frequency": 7
        },
        "category": "AI策略",
        "risk_level": "中等",
        "suitable_market": "全市场"
    },
    
    "options_strategy": {
        "name": "期权策略",
        "description": "基于期权的风险管理策略",
        "win_rate": 72.8,
        "max_drawdown": 23.4,
        "annual_return": 32.1,
        "sharpe_ratio": 1.4,
        "parameters": {
            "delta_neutral": True,
            "volatility_threshold": 0.3,
            "expiry_days": 30,
            "profit_target": 0.5
        },
        "category": "期权策略",
        "risk_level": "较高",
        "suitable_market": "高波动市"
    },
    
    "crypto_momentum": {
        "name": "加密货币动量策略",
        "description": "专门针对加密货币市场的动量策略",
        "win_rate": 70.5,
        "max_drawdown": 30.2,
        "annual_return": 67.4,
        "sharpe_ratio": 1.3,
        "parameters": {
            "momentum_window": 24,
            "volume_filter": True,
            "volatility_adjustment": True,
            "stop_loss": 5.0
        },
        "category": "加密货币",
        "risk_level": "高",
        "suitable_market": "加密市场"
    }
}

# 策略分类
STRATEGY_CATEGORIES = {
    "技术指标": ["professional_rsi", "advanced_macd", "bollinger_bands", "kdj_strategy"],
    "趋势跟踪": ["moving_average_cross", "breakout_strategy", "swing_trading"],
    "量价分析": ["volume_price_trend"],
    "动量策略": ["momentum_strategy", "crypto_momentum"],
    "均值回归": ["mean_reversion"],
    "网格策略": ["grid_trading"],
    "套利策略": ["arbitrage_strategy", "pairs_trading"],
    "高频策略": ["scalping_strategy"],
    "波段策略": ["swing_trading"],
    "统计套利": ["pairs_trading"],
    "情绪策略": ["news_sentiment"],
    "AI策略": ["machine_learning"],
    "期权策略": ["options_strategy"],
    "加密货币": ["crypto_momentum"]
}

# 风险等级分类
RISK_LEVELS = {
    "较低": ["arbitrage_strategy"],
    "中等": ["professional_rsi", "advanced_macd", "bollinger_bands", "kdj_strategy", 
             "moving_average_cross", "volume_price_trend", "mean_reversion", 
             "grid_trading", "swing_trading", "pairs_trading", "machine_learning"],
    "较高": ["momentum_strategy", "breakout_strategy", "news_sentiment", "options_strategy"],
    "高": ["scalping_strategy", "crypto_momentum"]
}

def get_strategy_by_category(category: str):
    """根据分类获取策略列表"""
    return STRATEGY_CATEGORIES.get(category, [])

def get_strategy_by_risk_level(risk_level: str):
    """根据风险等级获取策略列表"""
    return RISK_LEVELS.get(risk_level, [])

def get_high_winrate_strategies(min_winrate: float = 80.0):
    """获取高胜率策略"""
    return [key for key, info in STRATEGY_INFO.items() 
            if info.get('win_rate', 0) >= min_winrate]

def get_strategy_info(strategy_key: str):
    """获取单个策略的详细信息"""
    return STRATEGY_INFO.get(strategy_key, {})
