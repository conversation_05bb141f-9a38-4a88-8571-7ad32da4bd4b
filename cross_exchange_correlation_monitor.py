#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统跨交易所相关性监控系统

专门监控OKX和Gate.io之间的价格相关性和套利机会
严格遵守WMZC系统的14项禁令、5项必须原则和5项主动原则
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import json

# 导入WMZC系统模块
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from asp import ExchangeManager, ExchangeType, LogManager
from dual_exchange_risk_manager import DualExchangeRiskManager, RiskLevel

class CorrelationLevel(Enum):
    """相关性等级"""
    VERY_HIGH = "very_high"      # >0.9
    HIGH = "high"                # 0.7-0.9
    MEDIUM = "medium"            # 0.4-0.7
    LOW = "low"                  # 0.1-0.4
    VERY_LOW = "very_low"        # <0.1

class ArbitrageType(Enum):
    """套利类型"""
    SIMPLE = "simple"            # 简单价差套利
    TRIANGULAR = "triangular"    # 三角套利
    STATISTICAL = "statistical" # 统计套利
    MOMENTUM = "momentum"        # 动量套利

@dataclass
class CorrelationMetrics:
    """相关性指标"""
    symbol: str
    correlation: float
    correlation_level: CorrelationLevel
    price_spread: float
    spread_percentage: float
    volatility_ratio: float
    volume_correlation: float
    trend_consistency: float
    timestamp: datetime

@dataclass
class ArbitrageOpportunity:
    """套利机会"""
    symbol: str
    arbitrage_type: ArbitrageType
    profit_rate: float
    okx_price: float
    gate_price: float
    price_spread: float
    volume_okx: float
    volume_gate: float
    confidence_score: float
    risk_level: RiskLevel
    estimated_profit: float
    timestamp: datetime

class CrossExchangeCorrelationMonitor:
    """
    跨交易所相关性监控系统
    
    核心功能：
    1. 实时相关性计算和监控
    2. 套利机会识别和评估
    3. 价格偏差预警
    4. 趋势一致性分析
    5. 统计套利信号生成
    """
    
    def __init__(self):
        self.logger = LogManager()
        
        # 监控配置
        self.config = {
            'correlation_window': 50,        # 相关性计算窗口
            'min_correlation': 0.3,          # 最小相关性阈值
            'max_spread_percentage': 0.5,    # 最大价差百分比
            'min_arbitrage_profit': 0.001,   # 最小套利利润率
            'volume_threshold': 10000,       # 最小成交量阈值
            'confidence_threshold': 0.7,     # 置信度阈值
            'update_interval': 10,           # 更新间隔（秒）
            'data_retention_hours': 24,      # 数据保留时间
        }
        
        # 数据存储
        self.price_data = {'OKX': {}, 'GATE': {}}
        self.volume_data = {'OKX': {}, 'GATE': {}}
        self.correlation_history = []
        self.arbitrage_opportunities = []
        self.alerts = []
        
        # 交易所管理器
        self.exchange_managers = {}
        
        # 监控状态
        self.monitoring_active = False
        self.monitored_symbols = []
        
    async def initialize(self):
        """初始化相关性监控系统"""
        try:
            self.logger.info("初始化跨交易所相关性监控系统")
            
            # 初始化交易所管理器
            for exchange_type in [ExchangeType.OKX, ExchangeType.GATE]:
                manager = ExchangeManager(exchange_type)
                await manager.initialize()
                self.exchange_managers[exchange_type] = manager
                self.logger.info(f"{exchange_type.value}交易所管理器初始化完成")
            
            self.logger.info("跨交易所相关性监控系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"相关性监控系统初始化失败: {e}")
            raise
    
    async def start_monitoring(self, symbols: List[str]):
        """开始相关性监控"""
        try:
            self.logger.info(f"开始监控 {len(symbols)} 个交易对的相关性")
            self.monitoring_active = True
            self.monitored_symbols = symbols
            
            # 启动监控任务
            tasks = []
            
            # 数据收集任务
            for symbol in symbols:
                task = asyncio.create_task(self._collect_market_data(symbol))
                tasks.append(task)
            
            # 相关性分析任务
            analysis_task = asyncio.create_task(self._correlation_analysis_loop())
            tasks.append(analysis_task)
            
            # 套利检测任务
            arbitrage_task = asyncio.create_task(self._arbitrage_detection_loop())
            tasks.append(arbitrage_task)
            
            # 数据清理任务
            cleanup_task = asyncio.create_task(self._data_cleanup_loop())
            tasks.append(cleanup_task)
            
            # 等待所有任务
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"相关性监控启动失败: {e}")
            self.monitoring_active = False
            raise
    
    async def stop_monitoring(self):
        """停止相关性监控"""
        self.monitoring_active = False
        self.logger.info("相关性监控已停止")
    
    async def _collect_market_data(self, symbol: str):
        """收集市场数据"""
        try:
            while self.monitoring_active:
                # 获取双交易所数据
                okx_data = await self._get_market_data(ExchangeType.OKX, symbol)
                gate_data = await self._get_market_data(ExchangeType.GATE, symbol)
                
                if okx_data and gate_data:
                    # 存储数据
                    self._store_market_data(symbol, okx_data, gate_data)
                
                # 等待下次更新
                await asyncio.sleep(self.config['update_interval'])
                
        except Exception as e:
            self.logger.error(f"收集 {symbol} 市场数据异常: {e}")
    
    async def _get_market_data(self, exchange_type: ExchangeType, symbol: str) -> Optional[Dict]:
        """获取市场数据"""
        try:
            manager = self.exchange_managers[exchange_type]
            
            # 获取K线数据
            kline_data = await manager.get_kline_data(symbol, "1m", 1)
            
            if not kline_data:
                return None
            
            kline = kline_data[0]
            
            # 处理不同的数据格式
            if hasattr(kline, 'close') and hasattr(kline, 'volume'):
                price = float(kline.close)
                volume = float(kline.volume)
            elif isinstance(kline, (list, tuple)) and len(kline) >= 6:
                price = float(kline[4])  # 收盘价
                volume = float(kline[5])  # 成交量
            elif isinstance(kline, dict):
                price = float(kline.get('close', kline.get('c', 0)))
                volume = float(kline.get('volume', kline.get('v', 0)))
            else:
                return None
            
            return {
                'price': price,
                'volume': volume,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"获取 {exchange_type.value} {symbol} 市场数据失败: {e}")
            return None
    
    def _store_market_data(self, symbol: str, okx_data: Dict, gate_data: Dict):
        """存储市场数据"""
        try:
            # 初始化数据结构
            for exchange in ['OKX', 'GATE']:
                if symbol not in self.price_data[exchange]:
                    self.price_data[exchange][symbol] = []
                    self.volume_data[exchange][symbol] = []
            
            # 存储OKX数据
            self.price_data['OKX'][symbol].append({
                'price': okx_data['price'],
                'timestamp': okx_data['timestamp']
            })
            self.volume_data['OKX'][symbol].append({
                'volume': okx_data['volume'],
                'timestamp': okx_data['timestamp']
            })
            
            # 存储Gate数据
            self.price_data['GATE'][symbol].append({
                'price': gate_data['price'],
                'timestamp': gate_data['timestamp']
            })
            self.volume_data['GATE'][symbol].append({
                'volume': gate_data['volume'],
                'timestamp': gate_data['timestamp']
            })
            
            # 限制数据长度
            max_length = self.config['correlation_window'] * 2
            for exchange in ['OKX', 'GATE']:
                if len(self.price_data[exchange][symbol]) > max_length:
                    self.price_data[exchange][symbol] = self.price_data[exchange][symbol][-max_length:]
                    self.volume_data[exchange][symbol] = self.volume_data[exchange][symbol][-max_length:]
            
        except Exception as e:
            self.logger.error(f"存储 {symbol} 市场数据失败: {e}")
    
    async def _correlation_analysis_loop(self):
        """相关性分析循环"""
        try:
            while self.monitoring_active:
                for symbol in self.monitored_symbols:
                    try:
                        # 计算相关性指标
                        correlation_metrics = self._calculate_correlation_metrics(symbol)
                        
                        if correlation_metrics:
                            # 存储相关性历史
                            self.correlation_history.append(correlation_metrics)
                            
                            # 检查相关性警报
                            await self._check_correlation_alerts(correlation_metrics)
                    
                    except Exception as e:
                        self.logger.error(f"分析 {symbol} 相关性异常: {e}")
                
                # 等待下次分析
                await asyncio.sleep(self.config['update_interval'] * 2)
                
        except Exception as e:
            self.logger.error(f"相关性分析循环异常: {e}")
    
    def _calculate_correlation_metrics(self, symbol: str) -> Optional[CorrelationMetrics]:
        """计算相关性指标"""
        try:
            # 检查数据是否足够
            if (symbol not in self.price_data['OKX'] or 
                symbol not in self.price_data['GATE'] or
                len(self.price_data['OKX'][symbol]) < self.config['correlation_window'] or
                len(self.price_data['GATE'][symbol]) < self.config['correlation_window']):
                return None
            
            # 获取价格数据
            okx_prices = [item['price'] for item in self.price_data['OKX'][symbol][-self.config['correlation_window']:]]
            gate_prices = [item['price'] for item in self.price_data['GATE'][symbol][-self.config['correlation_window']:]]
            
            # 获取成交量数据
            okx_volumes = [item['volume'] for item in self.volume_data['OKX'][symbol][-self.config['correlation_window']:]]
            gate_volumes = [item['volume'] for item in self.volume_data['GATE'][symbol][-self.config['correlation_window']:]]
            
            # 计算价格相关性
            price_correlation = self._calculate_correlation(okx_prices, gate_prices)
            
            # 计算成交量相关性
            volume_correlation = self._calculate_correlation(okx_volumes, gate_volumes)
            
            # 计算价格差
            current_okx_price = okx_prices[-1]
            current_gate_price = gate_prices[-1]
            price_spread = abs(current_okx_price - current_gate_price)
            spread_percentage = price_spread / min(current_okx_price, current_gate_price)
            
            # 计算波动率比率
            okx_volatility = np.std(np.diff(okx_prices) / okx_prices[:-1])
            gate_volatility = np.std(np.diff(gate_prices) / gate_prices[:-1])
            volatility_ratio = min(okx_volatility, gate_volatility) / max(okx_volatility, gate_volatility)
            
            # 计算趋势一致性
            trend_consistency = self._calculate_trend_consistency(okx_prices, gate_prices)
            
            # 确定相关性等级
            correlation_level = self._get_correlation_level(price_correlation)
            
            return CorrelationMetrics(
                symbol=symbol,
                correlation=price_correlation,
                correlation_level=correlation_level,
                price_spread=price_spread,
                spread_percentage=spread_percentage,
                volatility_ratio=volatility_ratio,
                volume_correlation=volume_correlation,
                trend_consistency=trend_consistency,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"计算 {symbol} 相关性指标失败: {e}")
            return None
    
    def _calculate_correlation(self, data1: List[float], data2: List[float]) -> float:
        """计算相关系数"""
        try:
            if len(data1) < 2 or len(data2) < 2:
                return 0.0
            
            correlation = np.corrcoef(data1, data2)[0, 1]
            return correlation if not np.isnan(correlation) else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_trend_consistency(self, prices1: List[float], prices2: List[float]) -> float:
        """计算趋势一致性"""
        try:
            if len(prices1) < 2 or len(prices2) < 2:
                return 0.0
            
            # 计算价格变化方向
            changes1 = np.diff(prices1)
            changes2 = np.diff(prices2)
            
            # 计算方向一致性
            same_direction = np.sum((changes1 > 0) == (changes2 > 0))
            consistency = same_direction / len(changes1)
            
            return consistency
            
        except Exception:
            return 0.0
    
    def _get_correlation_level(self, correlation: float) -> CorrelationLevel:
        """获取相关性等级"""
        abs_corr = abs(correlation)
        
        if abs_corr > 0.9:
            return CorrelationLevel.VERY_HIGH
        elif abs_corr > 0.7:
            return CorrelationLevel.HIGH
        elif abs_corr > 0.4:
            return CorrelationLevel.MEDIUM
        elif abs_corr > 0.1:
            return CorrelationLevel.LOW
        else:
            return CorrelationLevel.VERY_LOW
    
    async def _check_correlation_alerts(self, metrics: CorrelationMetrics):
        """检查相关性警报"""
        try:
            alerts = []
            
            # 相关性过低警报
            if abs(metrics.correlation) < self.config['min_correlation']:
                alerts.append(f"{metrics.symbol} 相关性过低: {metrics.correlation:.3f}")
            
            # 价差过大警报
            if metrics.spread_percentage > self.config['max_spread_percentage']:
                alerts.append(f"{metrics.symbol} 价差过大: {metrics.spread_percentage:.3f}%")
            
            # 趋势不一致警报
            if metrics.trend_consistency < 0.6:
                alerts.append(f"{metrics.symbol} 趋势不一致: {metrics.trend_consistency:.3f}")
            
            # 发送警报
            for alert in alerts:
                await self._send_alert(alert, "correlation")
                
        except Exception as e:
            self.logger.error(f"检查 {metrics.symbol} 相关性警报失败: {e}")
    
    async def _send_alert(self, message: str, alert_type: str):
        """发送警报"""
        try:
            alert = {
                'message': message,
                'type': alert_type,
                'timestamp': datetime.now()
            }
            
            self.alerts.append(alert)
            self.logger.warning(f"相关性警报: {message}")
            
            # 限制警报历史长度
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]
                
        except Exception as e:
            self.logger.error(f"发送警报失败: {e}")

    async def _arbitrage_detection_loop(self):
        """套利检测循环"""
        try:
            while self.monitoring_active:
                for symbol in self.monitored_symbols:
                    try:
                        # 检测套利机会
                        opportunities = self._detect_arbitrage_opportunities(symbol)

                        for opportunity in opportunities:
                            # 存储套利机会
                            self.arbitrage_opportunities.append(opportunity)

                            # 发送套利警报
                            await self._send_arbitrage_alert(opportunity)

                    except Exception as e:
                        self.logger.error(f"检测 {symbol} 套利机会异常: {e}")

                # 清理过期套利机会
                self._cleanup_arbitrage_opportunities()

                # 等待下次检测
                await asyncio.sleep(self.config['update_interval'])

        except Exception as e:
            self.logger.error(f"套利检测循环异常: {e}")

    def _detect_arbitrage_opportunities(self, symbol: str) -> List[ArbitrageOpportunity]:
        """检测套利机会"""
        try:
            opportunities = []

            # 检查数据是否足够
            if (symbol not in self.price_data['OKX'] or
                symbol not in self.price_data['GATE'] or
                not self.price_data['OKX'][symbol] or
                not self.price_data['GATE'][symbol]):
                return opportunities

            # 获取最新数据
            okx_latest = self.price_data['OKX'][symbol][-1]
            gate_latest = self.price_data['GATE'][symbol][-1]
            okx_volume_latest = self.volume_data['OKX'][symbol][-1]
            gate_volume_latest = self.volume_data['GATE'][symbol][-1]

            okx_price = okx_latest['price']
            gate_price = gate_latest['price']
            okx_volume = okx_volume_latest['volume']
            gate_volume = gate_volume_latest['volume']

            # 检查成交量是否足够
            if okx_volume < self.config['volume_threshold'] or gate_volume < self.config['volume_threshold']:
                return opportunities

            # 计算价差和利润率
            price_spread = abs(okx_price - gate_price)
            profit_rate = price_spread / min(okx_price, gate_price)

            # 检查是否达到最小利润率
            if profit_rate < self.config['min_arbitrage_profit']:
                return opportunities

            # 计算置信度
            confidence_score = self._calculate_arbitrage_confidence(symbol, okx_price, gate_price)

            # 检查置信度是否足够
            if confidence_score < self.config['confidence_threshold']:
                return opportunities

            # 评估风险等级
            risk_level = self._assess_arbitrage_risk(profit_rate, confidence_score, okx_volume, gate_volume)

            # 估算利润
            estimated_profit = self._estimate_arbitrage_profit(okx_price, gate_price, okx_volume, gate_volume)

            # 创建套利机会
            opportunity = ArbitrageOpportunity(
                symbol=symbol,
                arbitrage_type=ArbitrageType.SIMPLE,
                profit_rate=profit_rate,
                okx_price=okx_price,
                gate_price=gate_price,
                price_spread=price_spread,
                volume_okx=okx_volume,
                volume_gate=gate_volume,
                confidence_score=confidence_score,
                risk_level=risk_level,
                estimated_profit=estimated_profit,
                timestamp=datetime.now()
            )

            opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            self.logger.error(f"检测 {symbol} 套利机会失败: {e}")
            return []

    def _calculate_arbitrage_confidence(self, symbol: str, okx_price: float, gate_price: float) -> float:
        """计算套利置信度"""
        try:
            confidence_factors = []

            # 1. 价格稳定性因子
            if len(self.price_data['OKX'][symbol]) >= 5:
                okx_recent_prices = [item['price'] for item in self.price_data['OKX'][symbol][-5:]]
                gate_recent_prices = [item['price'] for item in self.price_data['GATE'][symbol][-5:]]

                okx_stability = 1 - (np.std(okx_recent_prices) / np.mean(okx_recent_prices))
                gate_stability = 1 - (np.std(gate_recent_prices) / np.mean(gate_recent_prices))

                confidence_factors.append((okx_stability + gate_stability) / 2)

            # 2. 成交量一致性因子
            if len(self.volume_data['OKX'][symbol]) >= 3:
                okx_volumes = [item['volume'] for item in self.volume_data['OKX'][symbol][-3:]]
                gate_volumes = [item['volume'] for item in self.volume_data['GATE'][symbol][-3:]]

                volume_consistency = min(np.mean(okx_volumes), np.mean(gate_volumes)) / max(np.mean(okx_volumes), np.mean(gate_volumes))
                confidence_factors.append(volume_consistency)

            # 3. 价差持续性因子
            if len(self.price_data['OKX'][symbol]) >= 3:
                recent_spreads = []
                for i in range(-3, 0):
                    okx_p = self.price_data['OKX'][symbol][i]['price']
                    gate_p = self.price_data['GATE'][symbol][i]['price']
                    spread = abs(okx_p - gate_p) / min(okx_p, gate_p)
                    recent_spreads.append(spread)

                current_spread = abs(okx_price - gate_price) / min(okx_price, gate_price)
                spread_consistency = 1 - abs(current_spread - np.mean(recent_spreads)) / current_spread
                confidence_factors.append(max(0, spread_consistency))

            # 计算综合置信度
            if confidence_factors:
                return np.mean(confidence_factors)
            else:
                return 0.5  # 默认置信度

        except Exception:
            return 0.5

    def _assess_arbitrage_risk(self, profit_rate: float, confidence: float, okx_volume: float, gate_volume: float) -> RiskLevel:
        """评估套利风险等级"""
        try:
            risk_score = 0

            # 利润率风险
            if profit_rate < 0.002:
                risk_score += 2
            elif profit_rate < 0.005:
                risk_score += 1

            # 置信度风险
            if confidence < 0.6:
                risk_score += 3
            elif confidence < 0.8:
                risk_score += 1

            # 流动性风险
            min_volume = min(okx_volume, gate_volume)
            if min_volume < 50000:
                risk_score += 2
            elif min_volume < 100000:
                risk_score += 1

            # 根据风险分数确定等级
            if risk_score >= 5:
                return RiskLevel.CRITICAL
            elif risk_score >= 3:
                return RiskLevel.HIGH
            elif risk_score >= 1:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW

        except Exception:
            return RiskLevel.HIGH

    def _estimate_arbitrage_profit(self, okx_price: float, gate_price: float, okx_volume: float, gate_volume: float) -> float:
        """估算套利利润"""
        try:
            # 简化的利润估算
            price_spread = abs(okx_price - gate_price)

            # 假设交易量为较小成交量的10%
            trade_volume = min(okx_volume, gate_volume) * 0.1

            # 估算毛利润
            gross_profit = price_spread * trade_volume

            # 扣除手续费（假设0.1%）
            fee_rate = 0.001
            trading_cost = (okx_price + gate_price) * trade_volume * fee_rate

            # 净利润
            net_profit = gross_profit - trading_cost

            return max(0, net_profit)

        except Exception:
            return 0.0

    async def _send_arbitrage_alert(self, opportunity: ArbitrageOpportunity):
        """发送套利警报"""
        try:
            message = (f"{opportunity.symbol} 套利机会: "
                      f"利润率={opportunity.profit_rate:.3f}%, "
                      f"价差={opportunity.price_spread:.4f}, "
                      f"置信度={opportunity.confidence_score:.2f}")

            await self._send_alert(message, "arbitrage")

            # 记录详细信息
            self.logger.info(f"发现套利机会: {opportunity.symbol}, "
                           f"OKX价格={opportunity.okx_price:.4f}, "
                           f"Gate价格={opportunity.gate_price:.4f}, "
                           f"估算利润={opportunity.estimated_profit:.2f}")

        except Exception as e:
            self.logger.error(f"发送套利警报失败: {e}")

    def _cleanup_arbitrage_opportunities(self):
        """清理过期套利机会"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=5)  # 5分钟过期

            self.arbitrage_opportunities = [
                opp for opp in self.arbitrage_opportunities
                if opp.timestamp > cutoff_time
            ]

        except Exception as e:
            self.logger.error(f"清理套利机会失败: {e}")

    async def _data_cleanup_loop(self):
        """数据清理循环"""
        try:
            while self.monitoring_active:
                # 清理过期数据
                cutoff_time = datetime.now() - timedelta(hours=self.config['data_retention_hours'])

                # 清理价格数据
                for exchange in ['OKX', 'GATE']:
                    for symbol in self.price_data[exchange]:
                        self.price_data[exchange][symbol] = [
                            item for item in self.price_data[exchange][symbol]
                            if item['timestamp'] > cutoff_time
                        ]
                        self.volume_data[exchange][symbol] = [
                            item for item in self.volume_data[exchange][symbol]
                            if item['timestamp'] > cutoff_time
                        ]

                # 清理相关性历史
                self.correlation_history = [
                    item for item in self.correlation_history
                    if item.timestamp > cutoff_time
                ]

                # 清理警报
                self.alerts = [
                    alert for alert in self.alerts
                    if alert['timestamp'] > cutoff_time
                ]

                # 等待下次清理
                await asyncio.sleep(3600)  # 每小时清理一次

        except Exception as e:
            self.logger.error(f"数据清理循环异常: {e}")

    async def get_correlation_report(self) -> Dict[str, Any]:
        """获取相关性报告"""
        try:
            # 获取最近1小时的数据
            recent_time = datetime.now() - timedelta(hours=1)
            recent_correlations = [
                item for item in self.correlation_history
                if item.timestamp > recent_time
            ]

            # 按交易对分组
            symbol_stats = {}
            for correlation in recent_correlations:
                symbol = correlation.symbol
                if symbol not in symbol_stats:
                    symbol_stats[symbol] = {
                        'correlations': [],
                        'spreads': [],
                        'trend_consistencies': []
                    }

                symbol_stats[symbol]['correlations'].append(correlation.correlation)
                symbol_stats[symbol]['spreads'].append(correlation.spread_percentage)
                symbol_stats[symbol]['trend_consistencies'].append(correlation.trend_consistency)

            # 计算统计信息
            report = {}
            for symbol, stats in symbol_stats.items():
                report[symbol] = {
                    'avg_correlation': np.mean(stats['correlations']),
                    'max_correlation': np.max(stats['correlations']),
                    'min_correlation': np.min(stats['correlations']),
                    'avg_spread': np.mean(stats['spreads']),
                    'max_spread': np.max(stats['spreads']),
                    'avg_trend_consistency': np.mean(stats['trend_consistencies']),
                    'data_points': len(stats['correlations'])
                }

            return {
                'monitoring_status': self.monitoring_active,
                'monitored_symbols': self.monitored_symbols,
                'symbol_statistics': report,
                'total_correlations': len(recent_correlations),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"生成相关性报告失败: {e}")
            return {'error': str(e)}

    async def get_arbitrage_report(self) -> Dict[str, Any]:
        """获取套利报告"""
        try:
            # 获取最近的套利机会
            recent_opportunities = [
                opp for opp in self.arbitrage_opportunities
                if opp.timestamp > datetime.now() - timedelta(hours=1)
            ]

            # 按交易对分组
            symbol_opportunities = {}
            for opp in recent_opportunities:
                symbol = opp.symbol
                if symbol not in symbol_opportunities:
                    symbol_opportunities[symbol] = []
                symbol_opportunities[symbol].append(opp)

            # 生成报告
            report = {}
            for symbol, opportunities in symbol_opportunities.items():
                profit_rates = [opp.profit_rate for opp in opportunities]
                confidence_scores = [opp.confidence_score for opp in opportunities]
                estimated_profits = [opp.estimated_profit for opp in opportunities]

                report[symbol] = {
                    'total_opportunities': len(opportunities),
                    'avg_profit_rate': np.mean(profit_rates),
                    'max_profit_rate': np.max(profit_rates),
                    'avg_confidence': np.mean(confidence_scores),
                    'total_estimated_profit': np.sum(estimated_profits),
                    'latest_opportunity': opportunities[-1].timestamp.isoformat() if opportunities else None
                }

            return {
                'monitoring_status': self.monitoring_active,
                'total_opportunities': len(recent_opportunities),
                'symbol_opportunities': report,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"生成套利报告失败: {e}")
            return {'error': str(e)}

    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop_monitoring()

            for manager in self.exchange_managers.values():
                await manager.close()

            self.logger.info("跨交易所相关性监控系统清理完成")

        except Exception as e:
            self.logger.error(f"相关性监控系统清理失败: {e}")

# 使用示例
async def main():
    monitor = CrossExchangeCorrelationMonitor()

    try:
        await monitor.initialize()

        # 开始监控主要交易对
        symbols = ['BTC-USDT', 'ETH-USDT']

        # 启动监控（运行60秒作为演示）
        monitoring_task = asyncio.create_task(monitor.start_monitoring(symbols))

        # 等待60秒
        await asyncio.sleep(60)

        # 停止监控
        await monitor.stop_monitoring()

        # 获取报告
        correlation_report = await monitor.get_correlation_report()
        arbitrage_report = await monitor.get_arbitrage_report()

        print("相关性报告:", correlation_report)
        print("套利报告:", arbitrage_report)

    finally:
        await monitor.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
