#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的按钮测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    try:
        print("测试导入JSONConfigManager...")
        from asp import JSONConfigManager
        print("✅ 导入成功")
        
        print("测试创建配置管理器...")
        config = JSONConfigManager("wmzc_config.json")
        print("✅ 配置管理器创建成功")
        
        print("测试加载API凭证...")
        creds = config.load_api_credentials("OKX")
        if creds:
            print(f"✅ API凭证加载成功，状态: {creds.get('enabled', False)}")
        else:
            print("⚠️ 没有找到API凭证")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始简单按钮测试...")
    success = test_import()
    
    if success:
        print("🎉 测试通过！")
        print("\n📋 '开始交易'按钮无响应的解决方案:")
        print("1. 确保已配置API凭证")
        print("2. 点击'连接交易所'按钮")
        print("3. 等待连接成功")
        print("4. 配置交易策略")
        print("5. 然后点击'开始交易'")
    else:
        print("⚠️ 测试失败")
