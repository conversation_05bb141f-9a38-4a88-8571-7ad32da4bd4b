#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - 策略回测引擎
支持多策略历史数据回测和性能分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import asyncio
from dataclasses import dataclass
import math

@dataclass
class BacktestConfig:
    """回测配置参数"""
    initial_capital: float = 100000.0  # 初始资金
    start_date: str = "2024-01-01"     # 开始日期
    end_date: str = "2024-12-31"       # 结束日期
    timeframe: str = "1h"              # 时间框架
    commission_rate: float = 0.001     # 手续费率
    slippage: float = 0.0005          # 滑点
    max_position_ratio: float = 0.2    # 最大仓位比例
    strategies: List[str] = None       # 策略列表
    strategy_weights: Dict[str, float] = None  # 策略权重

@dataclass
class Trade:
    """交易记录"""
    timestamp: datetime
    strategy: str
    action: str  # 'BUY' or 'SELL'
    price: float
    quantity: float
    commission: float
    slippage_cost: float
    pnl: float = 0.0
    cumulative_pnl: float = 0.0

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float

class BacktestEngine:
    """策略回测引擎"""
    
    def __init__(self, config: BacktestConfig):
        """
        初始化回测引擎
        
        Args:
            config: 回测配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 回测状态
        self.current_capital = config.initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.daily_returns: List[float] = []
        
        # 策略实例
        self.strategy_instances = {}
        self.strategy_signals = {}
        
        # 回测结果
        self.backtest_results = {}
        self.performance_metrics = {}
        
        # 进度回调
        self.progress_callback = None
        
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def add_strategy(self, strategy_name: str, strategy_instance: Any, weight: float = 1.0):
        """
        添加策略到回测引擎
        
        Args:
            strategy_name: 策略名称
            strategy_instance: 策略实例
            weight: 策略权重
        """
        self.strategy_instances[strategy_name] = strategy_instance
        if self.config.strategy_weights is None:
            self.config.strategy_weights = {}
        self.config.strategy_weights[strategy_name] = weight
        
        self.logger.info(f"添加策略到回测引擎: {strategy_name}, 权重: {weight}")
    
    def load_historical_data(self, symbol: str = "BTCUSDT") -> pd.DataFrame:
        """
        加载历史数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            历史数据DataFrame
        """
        try:
            # 这里应该从真实的数据源加载历史数据
            # 为了演示，我们生成模拟的历史数据
            start_date = pd.to_datetime(self.config.start_date)
            end_date = pd.to_datetime(self.config.end_date)
            
            # 根据时间框架确定数据频率
            freq_map = {
                '1m': '1T', '5m': '5T', '15m': '15T',
                '1h': '1h', '4h': '4h', '1d': '1D'
            }
            freq = freq_map.get(self.config.timeframe, '1h')
            
            # 生成时间序列
            date_range = pd.date_range(start=start_date, end=end_date, freq=freq)
            
            # 生成模拟价格数据（基于随机游走）
            np.random.seed(42)  # 确保结果可重现
            base_price = 50000.0
            returns = np.random.normal(0.0001, 0.02, len(date_range))
            
            prices = [base_price]
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # 生成OHLCV数据
            data = []
            for i, (timestamp, price) in enumerate(zip(date_range, prices)):
                # 生成开高低收
                open_price = price
                high_price = open_price * (1 + abs(np.random.normal(0, 0.005)))
                low_price = open_price * (1 - abs(np.random.normal(0, 0.005)))
                close_price = open_price + np.random.normal(0, open_price * 0.01)
                
                # 确保价格逻辑正确
                high_price = max(high_price, open_price, close_price)
                low_price = min(low_price, open_price, close_price)
                
                volume = np.random.randint(1000, 10000)
                
                data.append({
                    'timestamp': timestamp,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            
            self.logger.info(f"加载历史数据完成: {len(df)} 条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
            return pd.DataFrame()
    
    def generate_strategy_signals(self, data: pd.DataFrame) -> Dict[str, List[Dict]]:
        """
        生成所有策略的交易信号
        
        Args:
            data: 历史数据
            
        Returns:
            策略信号字典
        """
        all_signals = {}
        
        for strategy_name, strategy_instance in self.strategy_instances.items():
            try:
                # 根据策略类型生成信号
                if hasattr(strategy_instance, 'analyze_market_data'):
                    # Pin Bar策略
                    pin_bars = strategy_instance.analyze_market_data(data)
                    signals = []
                    for pin_bar in pin_bars:
                        signal = strategy_instance.generate_trading_signal(pin_bar)
                        signals.append(signal)
                    all_signals[strategy_name] = signals
                    
                elif hasattr(strategy_instance, 'calculate_rsi'):
                    # RSI策略 - 需要实现RSI信号生成逻辑
                    signals = self.generate_rsi_signals(data, strategy_instance)
                    all_signals[strategy_name] = signals
                    
                elif hasattr(strategy_instance, 'calculate_macd'):
                    # MACD策略 - 需要实现MACD信号生成逻辑
                    signals = self.generate_macd_signals(data, strategy_instance)
                    all_signals[strategy_name] = signals
                    
                else:
                    # 通用策略接口
                    signals = self.generate_generic_signals(data, strategy_instance)
                    all_signals[strategy_name] = signals
                    
                self.logger.info(f"策略 {strategy_name} 生成 {len(signals)} 个信号")
                
            except Exception as e:
                self.logger.error(f"策略 {strategy_name} 信号生成失败: {e}")
                all_signals[strategy_name] = []
        
        return all_signals
    
    def generate_rsi_signals(self, data: pd.DataFrame, strategy_instance) -> List[Dict]:
        """生成RSI策略信号"""
        signals = []
        try:
            # 计算RSI指标
            rsi_data = self.calculate_rsi(data, period=14)
            
            for i in range(1, len(rsi_data)):
                current_rsi = rsi_data.iloc[i]['rsi']
                prev_rsi = rsi_data.iloc[i-1]['rsi']
                current_price = data.iloc[i]['close']
                timestamp = data.index[i]
                
                # RSI超买超卖信号
                if prev_rsi <= 30 and current_rsi > 30:
                    # 超卖反弹信号
                    signals.append({
                        'type': 'BUY',
                        'timestamp': timestamp,
                        'price': current_price,
                        'strength': 0.7,
                        'reason': f'RSI超卖反弹 ({current_rsi:.1f})',
                        'strategy': 'RSI'
                    })
                elif prev_rsi >= 70 and current_rsi < 70:
                    # 超买回落信号
                    signals.append({
                        'type': 'SELL',
                        'timestamp': timestamp,
                        'price': current_price,
                        'strength': 0.7,
                        'reason': f'RSI超买回落 ({current_rsi:.1f})',
                        'strategy': 'RSI'
                    })
                    
        except Exception as e:
            self.logger.error(f"RSI信号生成失败: {e}")
            
        return signals
    
    def generate_macd_signals(self, data: pd.DataFrame, strategy_instance) -> List[Dict]:
        """生成MACD策略信号"""
        signals = []
        try:
            # 计算MACD指标
            macd_data = self.calculate_macd(data)
            
            for i in range(1, len(macd_data)):
                current_macd = macd_data.iloc[i]['macd']
                current_signal = macd_data.iloc[i]['signal']
                prev_macd = macd_data.iloc[i-1]['macd']
                prev_signal = macd_data.iloc[i-1]['signal']
                current_price = data.iloc[i]['close']
                timestamp = data.index[i]
                
                # MACD金叉死叉信号
                if prev_macd <= prev_signal and current_macd > current_signal:
                    # 金叉买入信号
                    signals.append({
                        'type': 'BUY',
                        'timestamp': timestamp,
                        'price': current_price,
                        'strength': 0.6,
                        'reason': f'MACD金叉',
                        'strategy': 'MACD'
                    })
                elif prev_macd >= prev_signal and current_macd < current_signal:
                    # 死叉卖出信号
                    signals.append({
                        'type': 'SELL',
                        'timestamp': timestamp,
                        'price': current_price,
                        'strength': 0.6,
                        'reason': f'MACD死叉',
                        'strategy': 'MACD'
                    })
                    
        except Exception as e:
            self.logger.error(f"MACD信号生成失败: {e}")
            
        return signals
    
    def generate_generic_signals(self, data: pd.DataFrame, strategy_instance) -> List[Dict]:
        """生成通用策略信号"""
        signals = []
        # 这里可以实现通用的信号生成逻辑
        return signals
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算RSI指标"""
        close_prices = data['close']
        delta = close_prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        result = data.copy()
        result['rsi'] = rsi
        return result
    
    def calculate_macd(self, data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """计算MACD指标"""
        close_prices = data['close']
        ema_fast = close_prices.ewm(span=fast).mean()
        ema_slow = close_prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        
        result = data.copy()
        result['macd'] = macd
        result['signal'] = signal_line
        result['histogram'] = histogram
        return result

    def execute_trade(self, signal: Dict, current_price: float, timestamp: datetime, symbol: str = "BTCUSDT") -> Optional[Trade]:
        """
        执行交易

        Args:
            signal: 交易信号
            current_price: 当前价格
            timestamp: 时间戳
            symbol: 交易对符号

        Returns:
            交易记录
        """
        try:
            action = signal['type']
            strategy = signal.get('strategy', 'Unknown')
            strength = signal.get('strength', 0.5)

            # 计算交易数量
            position_value = self.current_capital * self.config.max_position_ratio * strength
            quantity = position_value / current_price

            # 计算滑点和手续费
            slippage_cost = quantity * current_price * self.config.slippage
            commission = quantity * current_price * self.config.commission_rate

            # 实际交易价格（考虑滑点）
            if action == 'BUY':
                actual_price = current_price * (1 + self.config.slippage)
            else:
                actual_price = current_price * (1 - self.config.slippage)

            # 检查资金是否足够
            total_cost = quantity * actual_price + commission + slippage_cost
            if action == 'BUY' and total_cost > self.current_capital:
                # 资金不足，调整数量
                available_capital = self.current_capital * 0.95  # 保留5%缓冲
                quantity = available_capital / (actual_price + self.config.commission_rate * actual_price + self.config.slippage * actual_price)
                total_cost = quantity * actual_price + commission + slippage_cost

            # 创建交易记录
            trade = Trade(
                timestamp=timestamp,
                strategy=strategy,
                action=action,
                price=actual_price,
                quantity=quantity,
                commission=commission,
                slippage_cost=slippage_cost
            )

            # 更新资金和持仓
            if action == 'BUY':
                self.current_capital -= total_cost
                # 更新持仓 - 使用传入的symbol参数
                if symbol in self.positions:
                    # 已有持仓，计算平均价格
                    old_pos = self.positions[symbol]
                    total_quantity = old_pos.quantity + quantity
                    avg_price = (old_pos.avg_price * old_pos.quantity + actual_price * quantity) / total_quantity
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=total_quantity,
                        avg_price=avg_price,
                        market_value=total_quantity * current_price,
                        unrealized_pnl=(current_price - avg_price) * total_quantity
                    )
                else:
                    # 新建持仓
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=actual_price,
                        market_value=quantity * current_price,
                        unrealized_pnl=0.0
                    )

            elif action == 'SELL':
                # 使用传入的symbol参数
                if symbol in self.positions and self.positions[symbol].quantity > 0:
                    # 有持仓才能卖出
                    pos = self.positions[symbol]
                    sell_quantity = min(quantity, pos.quantity)

                    # 计算盈亏
                    pnl = (actual_price - pos.avg_price) * sell_quantity - commission - slippage_cost
                    trade.pnl = pnl

                    # 更新资金
                    self.current_capital += sell_quantity * actual_price - commission - slippage_cost

                    # 更新持仓
                    remaining_quantity = pos.quantity - sell_quantity
                    if remaining_quantity > 0:
                        self.positions[symbol] = Position(
                            symbol=symbol,
                            quantity=remaining_quantity,
                            avg_price=pos.avg_price,
                            market_value=remaining_quantity * current_price,
                            unrealized_pnl=(current_price - pos.avg_price) * remaining_quantity
                        )
                    else:
                        del self.positions[symbol]

                    trade.quantity = sell_quantity
                else:
                    # 没有持仓，无法卖出
                    return None

            # 计算累计盈亏
            total_pnl = sum(t.pnl for t in self.trades) + trade.pnl
            trade.cumulative_pnl = total_pnl

            # 添加到交易记录
            self.trades.append(trade)

            return trade

        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")
            return None

    def update_equity_curve(self, timestamp: datetime, current_price: float):
        """更新资金曲线"""
        try:
            # 计算当前总资产
            total_equity = self.current_capital

            # 加上持仓市值
            for position in self.positions.values():
                total_equity += position.quantity * current_price

            # 记录资金曲线
            self.equity_curve.append((timestamp, total_equity))

            # 计算日收益率
            if len(self.equity_curve) > 1:
                prev_equity = self.equity_curve[-2][1]
                daily_return = (total_equity - prev_equity) / prev_equity
                self.daily_returns.append(daily_return)

        except Exception as e:
            self.logger.error(f"更新资金曲线失败: {e}")

    async def run_backtest(self, symbol: str = "BTCUSDT") -> Dict:
        """
        运行回测

        Args:
            symbol: 交易对符号

        Returns:
            回测结果
        """
        try:
            self.logger.info("开始运行策略回测...")

            # 重置回测状态
            self.current_capital = self.config.initial_capital
            self.positions = {}
            self.trades = []
            self.equity_curve = []
            self.daily_returns = []

            # 加载历史数据
            if self.progress_callback:
                self.progress_callback(10, "加载历史数据...")

            historical_data = self.load_historical_data(symbol)
            if historical_data.empty:
                raise ValueError("历史数据为空")

            # 生成策略信号
            if self.progress_callback:
                self.progress_callback(30, "生成策略信号...")

            all_signals = self.generate_strategy_signals(historical_data)

            # 合并所有策略信号并按时间排序
            combined_signals = []
            for strategy_name, signals in all_signals.items():
                for signal in signals:
                    signal['strategy'] = strategy_name
                    combined_signals.append(signal)

            # 按时间戳排序
            combined_signals.sort(key=lambda x: x['timestamp'])

            if self.progress_callback:
                self.progress_callback(50, f"开始回测，共{len(combined_signals)}个信号...")

            # 执行回测
            total_signals = len(combined_signals)
            for i, signal in enumerate(combined_signals):
                timestamp = signal['timestamp']

                # 获取当前价格
                try:
                    current_price = historical_data.loc[timestamp]['close']
                except KeyError:
                    # 如果找不到确切时间，使用最近的价格
                    nearest_idx = historical_data.index.get_loc(timestamp, method='nearest')
                    current_price = historical_data.iloc[nearest_idx]['close']

                # 执行交易 - 传入symbol参数
                trade = self.execute_trade(signal, current_price, timestamp, symbol)

                # 更新资金曲线
                self.update_equity_curve(timestamp, current_price)

                # 更新进度
                if self.progress_callback and i % 10 == 0:
                    progress = 50 + (i / total_signals) * 40
                    self.progress_callback(progress, f"处理信号 {i+1}/{total_signals}")

                # 让出控制权，避免界面卡顿
                if i % 100 == 0:
                    await asyncio.sleep(0.001)

            # 计算最终资金曲线
            if historical_data.index[-1] not in [eq[0] for eq in self.equity_curve]:
                final_price = historical_data.iloc[-1]['close']
                self.update_equity_curve(historical_data.index[-1], final_price)

            # 计算性能指标
            if self.progress_callback:
                self.progress_callback(90, "计算性能指标...")

            self.performance_metrics = self.calculate_performance_metrics()

            # 生成回测结果
            self.backtest_results = {
                'config': self.config,
                'trades': self.trades,
                'equity_curve': self.equity_curve,
                'performance_metrics': self.performance_metrics,
                'final_capital': self.equity_curve[-1][1] if self.equity_curve else self.config.initial_capital,
                'total_trades': len(self.trades),
                'strategy_signals': all_signals
            }

            if self.progress_callback:
                self.progress_callback(100, "回测完成！")

            self.logger.info(f"回测完成，共执行{len(self.trades)}笔交易")
            return self.backtest_results

        except Exception as e:
            self.logger.error(f"回测运行失败: {e}")
            if self.progress_callback:
                self.progress_callback(0, f"回测失败: {e}")
            raise e

    def calculate_performance_metrics(self) -> Dict:
        """计算回测性能指标"""
        try:
            if not self.equity_curve or len(self.equity_curve) < 2:
                return {}

            # 基础数据
            initial_capital = self.config.initial_capital
            final_capital = self.equity_curve[-1][1]
            equity_values = [eq[1] for eq in self.equity_curve]

            # 总收益率
            total_return = (final_capital - initial_capital) / initial_capital

            # 年化收益率
            start_date = pd.to_datetime(self.config.start_date)
            end_date = pd.to_datetime(self.config.end_date)
            days = (end_date - start_date).days
            years = days / 365.25
            annual_return = (final_capital / initial_capital) ** (1 / years) - 1 if years > 0 else 0

            # 最大回撤
            peak = equity_values[0]
            max_drawdown = 0
            drawdown_duration = 0
            current_drawdown_duration = 0

            for value in equity_values:
                if value > peak:
                    peak = value
                    current_drawdown_duration = 0
                else:
                    drawdown = (peak - value) / peak
                    max_drawdown = max(max_drawdown, drawdown)
                    current_drawdown_duration += 1
                    drawdown_duration = max(drawdown_duration, current_drawdown_duration)

            # 夏普比率
            if len(self.daily_returns) > 1:
                returns_std = np.std(self.daily_returns)
                avg_return = np.mean(self.daily_returns)
                sharpe_ratio = (avg_return / returns_std) * np.sqrt(252) if returns_std > 0 else 0
            else:
                sharpe_ratio = 0

            # 交易统计
            winning_trades = [t for t in self.trades if t.pnl > 0]
            losing_trades = [t for t in self.trades if t.pnl < 0]

            win_rate = len(winning_trades) / len(self.trades) if self.trades else 0
            avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
            avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

            # 最大连续亏损
            max_consecutive_losses = 0
            current_consecutive_losses = 0

            for trade in self.trades:
                if trade.pnl < 0:
                    current_consecutive_losses += 1
                    max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)
                else:
                    current_consecutive_losses = 0

            # VaR计算（95%置信度）
            var_95 = np.percentile(self.daily_returns, 5) if self.daily_returns else 0

            # 卡尔马比率
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0

            # 索提诺比率
            downside_returns = [r for r in self.daily_returns if r < 0]
            downside_std = np.std(downside_returns) if downside_returns else 0
            sortino_ratio = (avg_return / downside_std) * np.sqrt(252) if downside_std > 0 else 0

            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'calmar_ratio': calmar_ratio,
                'sortino_ratio': sortino_ratio,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'max_consecutive_losses': max_consecutive_losses,
                'var_95': var_95,
                'total_trades': len(self.trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'drawdown_duration': drawdown_duration,
                'volatility': np.std(self.daily_returns) * np.sqrt(252) if self.daily_returns else 0
            }

        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {}

    def get_monthly_returns(self) -> pd.DataFrame:
        """获取月度收益率"""
        try:
            if not self.equity_curve:
                return pd.DataFrame()

            # 转换为DataFrame
            df = pd.DataFrame(self.equity_curve, columns=['date', 'equity'])
            df.set_index('date', inplace=True)

            # 重采样为月度数据
            monthly_equity = df.resample('M').last()
            monthly_returns = monthly_equity.pct_change().dropna()

            return monthly_returns

        except Exception as e:
            self.logger.error(f"计算月度收益率失败: {e}")
            return pd.DataFrame()

    def get_strategy_performance(self) -> Dict[str, Dict]:
        """获取各策略的表现"""
        try:
            strategy_stats = {}

            for strategy_name in self.strategy_instances.keys():
                strategy_trades = [t for t in self.trades if t.strategy == strategy_name]

                if strategy_trades:
                    total_pnl = sum(t.pnl for t in strategy_trades)
                    winning_trades = [t for t in strategy_trades if t.pnl > 0]
                    win_rate = len(winning_trades) / len(strategy_trades)
                    avg_pnl = total_pnl / len(strategy_trades)

                    strategy_stats[strategy_name] = {
                        'total_trades': len(strategy_trades),
                        'total_pnl': total_pnl,
                        'win_rate': win_rate,
                        'avg_pnl': avg_pnl,
                        'winning_trades': len(winning_trades),
                        'losing_trades': len(strategy_trades) - len(winning_trades)
                    }
                else:
                    strategy_stats[strategy_name] = {
                        'total_trades': 0,
                        'total_pnl': 0,
                        'win_rate': 0,
                        'avg_pnl': 0,
                        'winning_trades': 0,
                        'losing_trades': 0
                    }

            return strategy_stats

        except Exception as e:
            self.logger.error(f"计算策略表现失败: {e}")
            return {}

    def export_results(self, format_type: str = 'json') -> str:
        """
        导出回测结果

        Args:
            format_type: 导出格式 ('json', 'csv')

        Returns:
            导出的文件路径或数据
        """
        try:
            if format_type == 'json':
                # 准备JSON数据
                export_data = {
                    'backtest_config': {
                        'initial_capital': self.config.initial_capital,
                        'start_date': self.config.start_date,
                        'end_date': self.config.end_date,
                        'timeframe': self.config.timeframe,
                        'commission_rate': self.config.commission_rate,
                        'slippage': self.config.slippage,
                        'max_position_ratio': self.config.max_position_ratio
                    },
                    'performance_metrics': self.performance_metrics,
                    'strategy_performance': self.get_strategy_performance(),
                    'trades': [
                        {
                            'timestamp': trade.timestamp.isoformat(),
                            'strategy': trade.strategy,
                            'action': trade.action,
                            'price': trade.price,
                            'quantity': trade.quantity,
                            'pnl': trade.pnl,
                            'cumulative_pnl': trade.cumulative_pnl
                        }
                        for trade in self.trades
                    ],
                    'equity_curve': [
                        {
                            'timestamp': timestamp.isoformat(),
                            'equity': equity
                        }
                        for timestamp, equity in self.equity_curve
                    ]
                }

                return json.dumps(export_data, indent=2, ensure_ascii=False)

            elif format_type == 'csv':
                # 导出交易记录为CSV
                trades_data = []
                for trade in self.trades:
                    trades_data.append({
                        '时间': trade.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        '策略': trade.strategy,
                        '操作': trade.action,
                        '价格': trade.price,
                        '数量': trade.quantity,
                        '手续费': trade.commission,
                        '滑点成本': trade.slippage_cost,
                        '盈亏': trade.pnl,
                        '累计盈亏': trade.cumulative_pnl
                    })

                df = pd.DataFrame(trades_data)
                return df.to_csv(index=False, encoding='utf-8-sig')

        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")
            return ""
