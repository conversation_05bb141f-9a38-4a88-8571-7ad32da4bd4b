#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC量化交易系统 - 统一配置管理器
整合所有设置的保存和加载功能
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

class UnifiedConfigManager:
    """统一配置管理器 - 整合所有设置的保存和加载"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        # 使用全局日志管理器避免LogManager类冲突
        from asp import _global_log_manager
        self.logger = _global_log_manager
        self.config_cache = {}
        
    async def save_config(self, category: str, key: str, value: Any) -> bool:
        """保存配置"""
        try:
            config_key = f"{category}_{key}"
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False)
            else:
                value_str = str(value)
            
            success = await self.db_manager.save_setting(config_key, value_str)
            if success:
                self.config_cache[config_key] = value
                self.logger.info(f"配置已保存: {config_key}")
            return success
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    async def load_config(self, category: str, key: str, default_value: Any = None) -> Any:
        """加载配置"""
        try:
            config_key = f"{category}_{key}"
            
            # 先检查缓存
            if config_key in self.config_cache:
                return self.config_cache[config_key]
            
            # 从数据库加载
            value_str = await self.db_manager.get_setting(config_key)
            if value_str:
                if isinstance(default_value, (dict, list)):
                    value = json.loads(value_str)
                elif isinstance(default_value, bool):
                    value = value_str.lower() == 'true'
                elif isinstance(default_value, (int, float)):
                    value = type(default_value)(value_str)
                else:
                    value = value_str
                
                self.config_cache[config_key] = value
                return value
            
            return default_value
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return default_value
    
    async def save_window_state(self, window_state: Dict[str, Any]) -> bool:
        """保存窗口状态"""
        try:
            return await self.save_config('window', 'state', window_state)
        except Exception as e:
            self.logger.error(f"保存窗口状态失败: {e}")
            return False
    
    async def load_window_state(self) -> Dict[str, Any]:
        """加载窗口状态"""
        try:
            return await self.load_config('window', 'state', {})
        except Exception as e:
            self.logger.error(f"加载窗口状态失败: {e}")
            return {}
    
    async def save_strategy_states(self, strategy_states: Dict[str, bool]) -> bool:
        """保存策略启用/禁用状态"""
        try:
            return await self.save_config('strategy', 'states', strategy_states)
        except Exception as e:
            self.logger.error(f"保存策略状态失败: {e}")
            return False
    
    async def load_strategy_states(self) -> Dict[str, bool]:
        """加载策略启用/禁用状态"""
        try:
            return await self.load_config('strategy', 'states', {})
        except Exception as e:
            self.logger.error(f"加载策略状态失败: {e}")
            return {}
    
    async def save_layout_preferences(self, layout_prefs: Dict[str, Any]) -> bool:
        """保存界面布局偏好"""
        try:
            return await self.save_config('layout', 'preferences', layout_prefs)
        except Exception as e:
            self.logger.error(f"保存布局偏好失败: {e}")
            return False
    
    async def load_layout_preferences(self) -> Dict[str, Any]:
        """加载界面布局偏好"""
        try:
            default_layout = {
                'layout_mode': 'large',
                'panel_sizes': {},
                'tab_order': [],
                'hidden_panels': []
            }
            return await self.load_config('layout', 'preferences', default_layout)
        except Exception as e:
            self.logger.error(f"加载布局偏好失败: {e}")
            return {}
    
    async def export_all_configs(self, file_path: str) -> bool:
        """导出所有配置"""
        try:
            # 获取所有设置
            all_settings = {}
            
            # 从数据库获取所有设置
            results = self.db_manager.execute_query("SELECT key, value FROM settings")
            for key, value in results:
                try:
                    # 尝试解析JSON
                    all_settings[key] = json.loads(value)
                except:
                    all_settings[key] = value
            
            # 添加导出信息
            export_info = {
                'export_time': datetime.now().isoformat(),
                'version': '1.0',
                'total_configs': len(all_settings),
                'configs': all_settings
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_info, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    async def import_all_configs(self, file_path: str) -> bool:
        """导入所有配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 检查导入数据格式
            if 'configs' in import_data:
                all_settings = import_data['configs']
            else:
                all_settings = import_data
            
            success_count = 0
            for key, value in all_settings.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, ensure_ascii=False)
                else:
                    value_str = str(value)
                
                if await self.db_manager.save_setting(key, value_str):
                    success_count += 1
            
            # 清空缓存以强制重新加载
            self.config_cache.clear()
            
            self.logger.info(f"成功导入 {success_count} 个配置")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False
    
    async def backup_configs(self, backup_dir: str = "config_backups") -> str:
        """备份所有配置"""
        try:
            import os
            from pathlib import Path
            
            # 创建备份目录
            backup_path = Path(backup_dir)
            backup_path.mkdir(exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_path / f"wmzc_config_backup_{timestamp}.json"
            
            # 导出配置
            if await self.export_all_configs(str(backup_file)):
                self.logger.info(f"配置备份完成: {backup_file}")
                return str(backup_file)
            else:
                return ""
                
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
            return ""
    
    async def validate_configs(self) -> Dict[str, Any]:
        """验证配置完整性"""
        try:
            validation_result = {
                'valid': True,
                'issues': [],
                'total_configs': 0,
                'valid_configs': 0
            }
            
            # 获取所有配置
            results = self.db_manager.execute_query("SELECT key, value FROM settings")
            validation_result['total_configs'] = len(results)
            
            for key, value in results:
                try:
                    # 尝试解析JSON配置
                    if value.startswith('{') or value.startswith('['):
                        json.loads(value)
                    validation_result['valid_configs'] += 1
                except json.JSONDecodeError:
                    validation_result['issues'].append(f"配置格式错误: {key}")
                    validation_result['valid'] = False
                except Exception as e:
                    validation_result['issues'].append(f"配置验证失败: {key} - {e}")
                    validation_result['valid'] = False
            
            self.logger.info(f"配置验证完成: {validation_result['valid_configs']}/{validation_result['total_configs']} 有效")
            return validation_result
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return {'valid': False, 'issues': [str(e)], 'total_configs': 0, 'valid_configs': 0}
    
    def clear_cache(self):
        """清空配置缓存"""
        self.config_cache.clear()
        self.logger.info("配置缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self.config_cache),
            'cached_keys': list(self.config_cache.keys())
        }
