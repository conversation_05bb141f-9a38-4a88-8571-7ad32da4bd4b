#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强技术指标监视面板
"""

import tkinter as tk
from tkinter import ttk
import random
import time
from datetime import datetime

class EnhancedIndicatorPanelDemo:
    """增强技术指标面板演示"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 增强技术指标监视面板演示")
        self.root.geometry("1200x800")
        
        # 初始化变量
        self.setup_variables()
        
        # 创建界面
        self.create_demo_interface()
        
        # 启动模拟数据更新
        self.start_demo_updates()
    
    def setup_variables(self):
        """设置演示变量"""
        # 模式控制
        self.beginner_mode_var = tk.BooleanVar(value=True)
        self.status_var = tk.StringVar(value="🟡 演示模式")
        
        # RSI变量
        self.rsi_value_var = tk.StringVar(value="--")
        self.rsi_status_var = tk.StringVar(value="🟡 等待数据")
        self.rsi_trend_var = tk.StringVar(value="➡️ 横盘")
        self.rsi_advice_var = tk.StringVar(value="等待数据更新...")
        
        # KDJ变量
        self.kdj_k_var = tk.StringVar(value="--")
        self.kdj_d_var = tk.StringVar(value="--")
        self.kdj_j_var = tk.StringVar(value="--")
        self.kdj_status_var = tk.StringVar(value="🟡 等待数据")
        self.kdj_signal_var = tk.StringVar(value="⚪ 无信号")
        self.kdj_advice_var = tk.StringVar(value="等待数据更新...")
        
        # MACD变量
        self.macd_value_var = tk.StringVar(value="--")
        self.macd_signal_var = tk.StringVar(value="--")
        self.macd_hist_var = tk.StringVar(value="--")
        self.macd_status_var = tk.StringVar(value="🟡 等待数据")
        self.macd_trend_var = tk.StringVar(value="➡️ 横盘")
        self.macd_cross_var = tk.StringVar(value="⚪ 无交叉")
        self.macd_advice_var = tk.StringVar(value="等待数据更新...")
        
        # 布林带变量
        self.bb_upper_var = tk.StringVar(value="--")
        self.bb_middle_var = tk.StringVar(value="--")
        self.bb_lower_var = tk.StringVar(value="--")
        self.bb_position_var = tk.StringVar(value="🟡 等待数据")
        self.bb_width_var = tk.StringVar(value="➡️ 稳定")
        self.bb_advice_var = tk.StringVar(value="等待数据更新...")
    
    def create_demo_interface(self):
        """创建演示界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="🚀 增强技术指标监视面板演示", 
                 font=("Microsoft YaHei", 16, "bold")).pack(side=tk.LEFT)
        
        # 控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 模式切换
        ttk.Checkbutton(control_frame, text="🔰 新手模式", 
                       variable=self.beginner_mode_var,
                       command=self.toggle_mode).pack(side=tk.LEFT, padx=(0, 20))
        
        # 控制按钮
        ttk.Button(control_frame, text="🔄 刷新数据", 
                  command=self.refresh_demo_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="❓ 帮助", 
                  command=self.show_demo_help).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.RIGHT)
        
        # 指标面板容器
        indicators_frame = ttk.Frame(main_frame)
        indicators_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置网格
        indicators_frame.columnconfigure(0, weight=1)
        indicators_frame.columnconfigure(1, weight=1)
        indicators_frame.rowconfigure(0, weight=1)
        indicators_frame.rowconfigure(1, weight=1)
        
        # 创建四个指标面板
        self.create_rsi_panel(indicators_frame, 0, 0)
        self.create_kdj_panel(indicators_frame, 0, 1)
        self.create_macd_panel(indicators_frame, 1, 0)
        self.create_bb_panel(indicators_frame, 1, 1)
    
    def create_rsi_panel(self, parent, row, col):
        """创建RSI面板"""
        frame = ttk.LabelFrame(parent, text="📈 RSI相对强弱指数")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)
        
        # 数值显示
        value_frame = ttk.Frame(frame)
        value_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(value_frame, text="当前RSI:", font=("Arial", 10)).pack(side=tk.LEFT)
        self.rsi_value_label = ttk.Label(value_frame, textvariable=self.rsi_value_var,
                                        font=("Arial", 16, "bold"))
        self.rsi_value_label.pack(side=tk.LEFT, padx=10)
        
        # 状态和趋势
        ttk.Label(frame, textvariable=self.rsi_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.rsi_trend_var).pack(anchor=tk.W, padx=5)
        
        # 建议
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.rsi_advice_var, 
                 wraplength=200).pack(padx=5, pady=5)
    
    def create_kdj_panel(self, parent, row, col):
        """创建KDJ面板"""
        frame = ttk.LabelFrame(parent, text="📊 KDJ随机指标")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)
        
        # KDJ值显示
        kdj_frame = ttk.Frame(frame)
        kdj_frame.pack(fill=tk.X, padx=5, pady=5)
        
        for label, var in [("K:", self.kdj_k_var), ("D:", self.kdj_d_var), ("J:", self.kdj_j_var)]:
            col_frame = ttk.Frame(kdj_frame)
            col_frame.pack(side=tk.LEFT, padx=10)
            ttk.Label(col_frame, text=label).pack()
            ttk.Label(col_frame, textvariable=var, font=("Arial", 12, "bold")).pack()
        
        # 状态和信号
        ttk.Label(frame, textvariable=self.kdj_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.kdj_signal_var).pack(anchor=tk.W, padx=5)
        
        # 建议
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.kdj_advice_var, 
                 wraplength=200).pack(padx=5, pady=5)
    
    def create_macd_panel(self, parent, row, col):
        """创建MACD面板"""
        frame = ttk.LabelFrame(parent, text="📈 MACD指数平滑移动平均")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)
        
        # MACD值显示
        value_frame = ttk.Frame(frame)
        value_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(value_frame, text="MACD:").pack(side=tk.LEFT)
        self.macd_value_label = ttk.Label(value_frame, textvariable=self.macd_value_var,
                                         font=("Arial", 12, "bold"))
        self.macd_value_label.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        ttk.Label(frame, textvariable=self.macd_status_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.macd_trend_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.macd_cross_var).pack(anchor=tk.W, padx=5)
        
        # 建议
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.macd_advice_var, 
                 wraplength=200).pack(padx=5, pady=5)
    
    def create_bb_panel(self, parent, row, col):
        """创建布林带面板"""
        frame = ttk.LabelFrame(parent, text="📊 布林带指标")
        frame.grid(row=row, column=col, sticky=tk.NSEW, padx=5, pady=5)
        
        # 布林带值显示
        bb_frame = ttk.Frame(frame)
        bb_frame.pack(fill=tk.X, padx=5, pady=5)
        
        for label, var in [("上轨:", self.bb_upper_var), ("中轨:", self.bb_middle_var), ("下轨:", self.bb_lower_var)]:
            col_frame = ttk.Frame(bb_frame)
            col_frame.pack(side=tk.LEFT, padx=5)
            ttk.Label(col_frame, text=label).pack()
            ttk.Label(col_frame, textvariable=var, font=("Arial", 10)).pack()
        
        # 状态显示
        ttk.Label(frame, textvariable=self.bb_position_var).pack(anchor=tk.W, padx=5)
        ttk.Label(frame, textvariable=self.bb_width_var).pack(anchor=tk.W, padx=5)
        
        # 建议
        advice_frame = ttk.LabelFrame(frame, text="💡 交易建议")
        advice_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(advice_frame, textvariable=self.bb_advice_var, 
                 wraplength=200).pack(padx=5, pady=5)
    
    def toggle_mode(self):
        """切换模式"""
        is_beginner = self.beginner_mode_var.get()
        mode_text = "新手模式" if is_beginner else "专业模式"
        self.status_var.set(f"🔰 {mode_text}")
        print(f"切换到{mode_text}")
    
    def refresh_demo_data(self):
        """刷新演示数据"""
        self.status_var.set("🔄 正在刷新...")
        self.generate_random_data()
        self.status_var.set("🟢 数据已更新")
    
    def generate_random_data(self):
        """生成随机演示数据"""
        # 生成RSI数据
        rsi = random.uniform(20, 80)
        self.update_rsi_demo(rsi)
        
        # 生成KDJ数据
        k = random.uniform(20, 80)
        d = random.uniform(20, 80)
        j = random.uniform(0, 100)
        self.update_kdj_demo(k, d, j)
        
        # 生成MACD数据
        macd = random.uniform(-50, 50)
        signal = random.uniform(-50, 50)
        hist = macd - signal
        self.update_macd_demo(macd, signal, hist)
        
        # 生成布林带数据
        base_price = 117000
        upper = base_price * 1.02
        middle = base_price
        lower = base_price * 0.98
        current = random.uniform(lower, upper)
        self.update_bb_demo(upper, middle, lower, current)
    
    def update_rsi_demo(self, rsi):
        """更新RSI演示数据"""
        self.rsi_value_var.set(f"{rsi:.1f}")
        
        if rsi >= 70:
            self.rsi_value_label.config(foreground="red")
            self.rsi_status_var.set("🔴 超买区域")
            self.rsi_trend_var.set("⬇️ 可能下跌")
            self.rsi_advice_var.set("🔴 卖出信号：RSI超买，建议减仓")
        elif rsi <= 30:
            self.rsi_value_label.config(foreground="green")
            self.rsi_status_var.set("🟢 超卖区域")
            self.rsi_trend_var.set("⬆️ 可能上涨")
            self.rsi_advice_var.set("🟢 买入信号：RSI超卖，建议买入")
        else:
            self.rsi_value_label.config(foreground="blue")
            self.rsi_status_var.set("🔵 正常区间")
            self.rsi_trend_var.set("➡️ 横盘整理")
            self.rsi_advice_var.set("🔵 观望为主：RSI在正常区间")
    
    def update_kdj_demo(self, k, d, j):
        """更新KDJ演示数据"""
        self.kdj_k_var.set(f"{k:.1f}")
        self.kdj_d_var.set(f"{d:.1f}")
        self.kdj_j_var.set(f"{j:.1f}")
        
        if j >= 80:
            self.kdj_status_var.set("🔴 超买区域")
            self.kdj_advice_var.set("🔴 卖出信号：KDJ超买")
        elif j <= 20:
            self.kdj_status_var.set("🟢 超卖区域")
            self.kdj_advice_var.set("🟢 买入信号：KDJ超卖")
        else:
            self.kdj_status_var.set("🔵 正常区间")
            self.kdj_advice_var.set("🔵 观望为主：KDJ正常")
        
        if k > d:
            self.kdj_signal_var.set("🟡 金叉信号")
        else:
            self.kdj_signal_var.set("🔴 死叉信号")
    
    def update_macd_demo(self, macd, signal, hist):
        """更新MACD演示数据"""
        self.macd_value_var.set(f"{macd:.2f}")
        self.macd_signal_var.set(f"{signal:.2f}")
        self.macd_hist_var.set(f"{hist:.2f}")
        
        if macd > 0:
            self.macd_value_label.config(foreground="green")
            self.macd_status_var.set("🟢 多头市场")
        else:
            self.macd_value_label.config(foreground="red")
            self.macd_status_var.set("🔴 空头市场")
        
        if hist > 0:
            self.macd_trend_var.set("⬆️ 向上")
        else:
            self.macd_trend_var.set("⬇️ 向下")
        
        if macd > signal:
            self.macd_cross_var.set("🟡 金叉")
            self.macd_advice_var.set("🟢 买入信号：MACD金叉")
        else:
            self.macd_cross_var.set("🔴 死叉")
            self.macd_advice_var.set("🔴 卖出信号：MACD死叉")
    
    def update_bb_demo(self, upper, middle, lower, current):
        """更新布林带演示数据"""
        self.bb_upper_var.set(f"{upper:.0f}")
        self.bb_middle_var.set(f"{middle:.0f}")
        self.bb_lower_var.set(f"{lower:.0f}")
        
        if current >= upper:
            self.bb_position_var.set("🔴 上轨附近")
            self.bb_advice_var.set("🔴 卖出信号：价格触及上轨")
        elif current <= lower:
            self.bb_position_var.set("🟢 下轨附近")
            self.bb_advice_var.set("🟢 买入信号：价格触及下轨")
        else:
            self.bb_position_var.set("🔵 中轨区域")
            self.bb_advice_var.set("🔵 观望为主：价格在中轨区域")
        
        self.bb_width_var.set("➡️ 稳定")
    
    def show_demo_help(self):
        """显示演示帮助"""
        help_text = """
🚀 增强技术指标监视面板演示

这是一个演示版本，展示了增强技术指标面板的功能：

📈 RSI：显示相对强弱指数和交易建议
📊 KDJ：显示随机指标和金叉死叉信号
📈 MACD：显示指数平滑移动平均和趋势
📊 布林带：显示价格通道和位置分析

🔰 新手模式：简化显示，突出交易建议
🔧 专业模式：详细数值，完整技术分析

点击"刷新数据"可以生成新的随机演示数据。
"""
        print(help_text)
    
    def start_demo_updates(self):
        """启动演示数据更新"""
        self.generate_random_data()
        # 每3秒更新一次演示数据
        self.root.after(3000, self.start_demo_updates)
    
    def run(self):
        """运行演示"""
        print("🚀 启动增强技术指标面板演示...")
        self.root.mainloop()

if __name__ == "__main__":
    demo = EnhancedIndicatorPanelDemo()
    demo.run()
