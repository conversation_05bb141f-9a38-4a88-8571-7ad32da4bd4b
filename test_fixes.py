#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC系统Bug修复验证测试
验证所有修复的Bug是否生效
"""

import sys
import os
import asyncio
import traceback

def test_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    try:
        # 测试基本Python模块
        import json
        import time
        import sqlite3
        import logging
        print("✅ 基本模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 基本模块导入失败: {e}")
        return False

def test_syntax():
    """测试asp.py语法"""
    print("🔍 测试asp.py语法...")
    try:
        import ast
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        ast.parse(content)
        print("✅ asp.py语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ asp.py语法错误: {e}")
        print(f"   行号: {e.lineno}, 位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ asp.py语法检查失败: {e}")
        return False

def test_logmanager_fix():
    """测试LogManager循环引用修复"""
    print("🔍 测试LogManager循环引用修复...")
    try:
        # 检查文件中是否有LogManager的前置定义
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有全局日志管理器
        if '_global_log_manager = LogManager()' in content:
            print("✅ LogManager前置定义存在")
        else:
            print("❌ LogManager前置定义缺失")
            return False
            
        # 检查是否删除了重复的LogManager类定义
        logmanager_count = content.count('class LogManager:')
        if logmanager_count == 1:
            print("✅ LogManager重复定义已清理")
        else:
            print(f"❌ LogManager仍有{logmanager_count}个定义")
            return False
            
        return True
    except Exception as e:
        print(f"❌ LogManager修复验证失败: {e}")
        return False

def test_async_loop_fix():
    """测试异步事件循环管理修复"""
    print("🔍 测试异步事件循环管理修复...")
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有新的安全方法
        if 'run_async_in_loop' in content and 'create_task_safely' in content:
            print("✅ 异步事件循环安全方法已添加")
        else:
            print("❌ 异步事件循环安全方法缺失")
            return False
            
        # 检查是否修复了协程泄漏问题
        if 'coro.close()' in content:
            print("✅ 协程清理机制已添加")
        else:
            print("❌ 协程清理机制缺失")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 异步事件循环修复验证失败: {e}")
        return False

def test_database_connection_fix():
    """测试数据库连接资源管理修复"""
    print("🔍 测试数据库连接资源管理修复...")
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有清理方法
        if '_cleanup_connection' in content:
            print("✅ 数据库连接清理方法已添加")
        else:
            print("❌ 数据库连接清理方法缺失")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 数据库连接修复验证失败: {e}")
        return False

def test_decorator_fix():
    """测试装饰器使用错误修复"""
    print("🔍 测试装饰器使用错误修复...")
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有安全装饰器
        if 'safe_performance_timer' in content:
            print("✅ 安全性能装饰器已添加")
        else:
            print("❌ 安全性能装饰器缺失")
            return False
            
        # 检查是否替换了不安全的装饰器使用
        if '@performance_monitor.performance_timer' not in content:
            print("✅ 不安全的装饰器使用已替换")
        else:
            print("❌ 仍有不安全的装饰器使用")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 装饰器修复验证失败: {e}")
        return False

def test_duplicate_methods_fix():
    """测试重复方法定义修复"""
    print("🔍 测试重复方法定义修复...")
    try:
        with open('asp.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查get_performance_report方法数量
        performance_report_count = content.count('def get_performance_report(')
        if performance_report_count == 1:
            print("✅ get_performance_report重复定义已清理")
        else:
            print(f"❌ get_performance_report仍有{performance_report_count}个定义")
            return False
            
        # 检查run_database_operation方法数量
        database_operation_count = content.count('def run_database_operation(')
        if database_operation_count == 1:
            print("✅ run_database_operation重复定义已清理")
        else:
            print(f"❌ run_database_operation仍有{database_operation_count}个定义")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 重复方法修复验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 WMZC系统Bug修复验证测试开始")
    print("=" * 50)
    
    tests = [
        ("基本导入测试", test_imports),
        ("语法检查测试", test_syntax),
        ("LogManager循环引用修复", test_logmanager_fix),
        ("异步事件循环管理修复", test_async_loop_fix),
        ("数据库连接资源管理修复", test_database_connection_fix),
        ("装饰器使用错误修复", test_decorator_fix),
        ("重复方法定义修复", test_duplicate_methods_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   测试失败")
        except Exception as e:
            print(f"   测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Bug修复验证通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
