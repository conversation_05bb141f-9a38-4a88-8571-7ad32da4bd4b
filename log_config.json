{"version": 1, "disable_existing_loggers": false, "formatters": {"standard": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "detailed": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "kline": {"format": "%(asctime)s - K线数据 - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "standard", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "logs/wmzc_system.log", "maxBytes": 10485760, "backupCount": 5, "encoding": "utf-8"}, "kline_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "kline", "filename": "logs/kline_data.log", "maxBytes": 5242880, "backupCount": 3, "encoding": "utf-8"}, "error_file": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "detailed", "filename": "logs/errors.log", "maxBytes": 5242880, "backupCount": 3, "encoding": "utf-8"}}, "loggers": {"wmzc": {"level": "DEBUG", "handlers": ["console", "file", "error_file"], "propagate": false}, "wmzc.kline": {"level": "INFO", "handlers": ["kline_file", "console"], "propagate": false}, "wmzc.api": {"level": "INFO", "handlers": ["file", "console"], "propagate": false}, "wmzc.strategy": {"level": "INFO", "handlers": ["file", "console"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console", "file"]}}