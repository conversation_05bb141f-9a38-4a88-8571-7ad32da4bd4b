# WMZC量化交易系统 - 企业级部署配置
# 支持单机、集群、容器化部署

# 应用配置
application:
  name: "wmzc-trading-system"
  version: "2.0.0"
  environment: "production"  # development, staging, production
  debug: false
  log_level: "INFO"

# 数据库配置
database:
  # 主数据库
  primary:
    type: "sqlite"
    path: "/data/wmzc/wmzc_trading.db"
    backup_path: "/data/wmzc/backup/"
    
  # 连接池配置
  pool:
    min_connections: 5
    max_connections: 20
    max_idle_time: 300
    connection_timeout: 30
    enable_monitoring: true
    
  # 备份配置
  backup:
    enabled: true
    interval: "0 2 * * *"  # 每天凌晨2点
    retention_days: 30
    compression: true

# Redis缓存配置
redis:
  enabled: true
  
  # 单机配置
  standalone:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    
  # 集群配置（可选）
  cluster:
    enabled: false
    nodes:
      - host: "redis-1"
        port: 6379
      - host: "redis-2"
        port: 6379
      - host: "redis-3"
        port: 6379
        
  # Sentinel配置（可选）
  sentinel:
    enabled: false
    master_name: "wmzc-master"
    hosts:
      - host: "sentinel-1"
        port: 26379
      - host: "sentinel-2"
        port: 26379
      - host: "sentinel-3"
        port: 26379
        
  # 缓存策略
  cache:
    default_ttl: 1800  # 30分钟
    max_memory_policy: "allkeys-lru"
    max_connections: 50

# 分布式配置
distributed:
  enabled: false
  
  # 服务发现
  service_discovery:
    type: "consul"  # consul, etcd, zookeeper
    consul:
      host: "localhost"
      port: 8500
      datacenter: "dc1"
      
  # 负载均衡
  load_balancer:
    strategy: "round_robin"  # round_robin, least_connections, weighted_round_robin
    health_check:
      enabled: true
      interval: 30
      timeout: 5
      
  # 集群配置
  cluster:
    min_instances: 2
    max_instances: 10
    auto_scaling: true

# 性能配置
performance:
  # 线程池配置
  thread_pools:
    core_pool_size: 8
    max_pool_size: 32
    keep_alive_time: 60
    queue_size: 1000
    
  # 内存配置
  memory:
    max_heap_size: "2g"
    initial_heap_size: "512m"
    gc_strategy: "G1GC"
    
  # 监控配置
  monitoring:
    enabled: true
    metrics_interval: 30
    performance_logging: true
    slow_query_threshold: 100  # ms

# 安全配置
security:
  # API密钥加密
  encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_interval: 86400  # 24小时
    
  # 访问控制
  access_control:
    enabled: true
    rate_limiting:
      enabled: true
      requests_per_minute: 1000
      burst_size: 100
      
  # SSL/TLS配置
  tls:
    enabled: false
    cert_file: "/etc/ssl/certs/wmzc.crt"
    key_file: "/etc/ssl/private/wmzc.key"

# 日志配置
logging:
  # 日志级别
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # 日志输出
  handlers:
    console:
      enabled: true
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      
    file:
      enabled: true
      path: "/var/log/wmzc/"
      max_size: "100MB"
      backup_count: 10
      rotation: "daily"
      
    syslog:
      enabled: false
      host: "localhost"
      port: 514
      facility: "local0"
      
  # 特定模块日志级别
  loggers:
    "database": "DEBUG"
    "redis": "INFO"
    "trading": "INFO"
    "performance": "WARNING"

# 交易配置
trading:
  # 交易所配置
  exchanges:
    okx:
      enabled: true
      api_key_encrypted: true
      rate_limit: 100  # 请求/分钟
      
    gate:
      enabled: true
      api_key_encrypted: true
      rate_limit: 100  # 请求/分钟
      
  # 风险控制
  risk_management:
    max_position_size: 10000  # USDT
    max_daily_loss: 1000      # USDT
    max_drawdown: 0.1         # 10%
    
  # 策略配置
  strategies:
    rsi:
      enabled: true
      default_params:
        period: 14
        overbought: 70
        oversold: 30
        
    macd:
      enabled: true
      default_params:
        fast: 12
        slow: 26
        signal: 9

# 监控和报警
monitoring:
  # 系统监控
  system:
    enabled: true
    cpu_threshold: 80      # %
    memory_threshold: 85   # %
    disk_threshold: 90     # %
    
  # 应用监控
  application:
    enabled: true
    response_time_threshold: 1000  # ms
    error_rate_threshold: 0.01     # 1%
    
  # 报警配置
  alerts:
    email:
      enabled: false
      smtp_host: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      recipients:
        - "<EMAIL>"
        
    webhook:
      enabled: false
      url: "https://hooks.slack.com/services/..."
      
    wechat:
      enabled: true
      sendkey: "your_sendkey_here"

# 部署配置
deployment:
  # 部署模式
  mode: "standalone"  # standalone, cluster, container
  
  # 单机部署
  standalone:
    data_dir: "/data/wmzc"
    log_dir: "/var/log/wmzc"
    pid_file: "/var/run/wmzc.pid"
    
  # 集群部署
  cluster:
    nodes:
      - host: "wmzc-node-1"
        port: 8000
        weight: 1
      - host: "wmzc-node-2"
        port: 8000
        weight: 1
      - host: "wmzc-node-3"
        port: 8000
        weight: 1
        
  # 容器部署
  container:
    image: "wmzc/trading-system:2.0.0"
    replicas: 3
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
        
    # 环境变量
    env:
      - name: "WMZC_ENV"
        value: "production"
      - name: "WMZC_LOG_LEVEL"
        value: "INFO"
        
    # 存储卷
    volumes:
      - name: "data"
        path: "/data/wmzc"
        size: "100Gi"
      - name: "logs"
        path: "/var/log/wmzc"
        size: "50Gi"

# 备份和恢复
backup:
  # 数据备份
  data:
    enabled: true
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention: 30          # 保留30天
    compression: true
    encryption: true
    
  # 配置备份
  config:
    enabled: true
    schedule: "0 3 * * 0"  # 每周日凌晨3点
    retention: 12          # 保留12周
    
  # 远程备份
  remote:
    enabled: false
    type: "s3"  # s3, ftp, sftp
    s3:
      bucket: "wmzc-backups"
      region: "us-east-1"
      access_key: "your_access_key"
      secret_key: "your_secret_key"

# 开发和测试配置
development:
  # 开发模式配置
  debug: true
  auto_reload: true
  test_data: true
  
  # 测试配置
  testing:
    database_path: "/tmp/wmzc_test.db"
    redis_db: 15
    mock_exchanges: true
    
  # 性能测试
  performance_testing:
    enabled: false
    concurrent_users: 50
    test_duration: 300  # 5分钟
    
# 扩展配置
extensions:
  # 插件配置
  plugins:
    enabled: true
    directory: "/opt/wmzc/plugins"
    
  # API配置
  api:
    enabled: false
    host: "0.0.0.0"
    port: 8080
    cors_enabled: true
    
  # WebSocket配置
  websocket:
    enabled: false
    port: 8081
    max_connections: 1000
