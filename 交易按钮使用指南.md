# WMZC量化交易系统 - "开始交易"按钮使用指南

## 🎯 问题解决方案

如果您点击"开始交易"按钮没有反应，这是正常的系统保护机制。系统需要确保所有前置条件都满足后才能开始交易。

## 📋 完整操作步骤

### 步骤1️⃣ 启动系统
```bash
python run_wmzc.py
```
- 等待系统完全加载
- 如果有保存的API凭证，系统会询问是否自动连接

### 步骤2️⃣ 配置API凭证
在主配置页面输入您的交易所API凭证：

**OKX交易所：**
- API Key: 从OKX获取的API密钥
- Secret Key: 从OKX获取的私钥
- Passphrase: 您在OKX设置的密码短语

**Gate.io交易所：**
- API Key: 从Gate.io获取的API密钥
- Secret Key: 从Gate.io获取的私钥
- Passphrase: 留空

### 步骤3️⃣ 连接交易所
1. 输入API凭证后，点击 **"连接交易所"** 按钮
2. 等待连接成功提示
3. 状态栏应显示 "已连接到XXX"

### 步骤4️⃣ 配置交易策略
1. 切换到 **"RSI策略"** 或 **"MACD策略"** 页面
2. 启用至少一个策略（勾选启用复选框）
3. 设置合适的参数

### 步骤5️⃣ 开始交易
1. 点击 **"开始交易"** 按钮
2. 系统会显示确认对话框
3. 确认后开始自动交易

## 🔧 新增功能

### 自动连接提示
- 如果系统检测到已保存的API凭证，启动时会询问是否自动连接
- 选择"是"可以快速连接到交易所

### 友好错误提示
- 点击"开始交易"时，如果未连接交易所，会显示详细的操作指导
- 可以选择直接跳转到配置页面

### 快速跳转
- 系统会自动跳转到相应的配置页面
- 减少用户查找的时间

## ⚠️ 常见问题

### Q: 点击"开始交易"没有反应？
**A:** 这是正常的保护机制，请按照以下顺序检查：
1. 是否已连接交易所？（状态栏显示"已连接"）
2. 是否已配置交易策略？（至少启用一个策略）
3. 是否有足够的账户余额？

### Q: 连接交易所失败？
**A:** 请检查：
1. API凭证是否正确
2. 网络连接是否正常
3. API密钥是否有交易权限
4. 是否超过了API调用频率限制

### Q: 如何获取API凭证？
**A:** 
- **OKX**: 登录OKX官网 → 个人中心 → API管理 → 创建API
- **Gate.io**: 登录Gate.io官网 → 个人中心 → API管理 → 创建API

### Q: API凭证安全吗？
**A:** 
- 系统使用加密算法保存您的API凭证
- 凭证存储在本地JSON文件中，不会上传到服务器
- 建议定期更换API密钥

## 🎉 系统状态指示

### 连接状态
- **未连接**: 红色，需要先连接交易所
- **已连接**: 绿色，可以开始交易
- **连接中**: 黄色，正在连接

### 交易状态
- **停止**: 可以点击"开始交易"
- **运行**: 交易进行中，可以暂停或停止
- **暂停**: 可以继续或停止交易

## 📞 技术支持

如果按照上述步骤操作后仍有问题，请：
1. 查看系统日志文件
2. 检查网络连接
3. 确认API凭证权限
4. 联系技术支持

---

**记住：安全第一，请在充分了解风险的情况下进行量化交易！**
