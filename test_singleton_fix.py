#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单例模式线程安全修复效果
"""

import threading
import time
import concurrent.futures

def test_singleton_thread_safety():
    """测试单例模式的线程安全性"""
    print("🔍 测试单例模式线程安全性...")
    
    # 存储创建的实例
    instances = []
    lock = threading.Lock()
    
    def create_async_task_manager():
        """在线程中创建AsyncTaskManager实例"""
        try:
            from asp import AsyncTaskManager
            instance = AsyncTaskManager()
            with lock:
                instances.append(id(instance))
            return instance
        except Exception as e:
            print(f"创建AsyncTaskManager失败: {e}")
            return None
    
    def create_async_loop_manager():
        """在线程中创建AsyncLoopManager实例"""
        try:
            from asp import AsyncLoopManager
            instance = AsyncLoopManager()
            with lock:
                instances.append(id(instance))
            return instance
        except Exception as e:
            print(f"创建AsyncLoopManager失败: {e}")
            return None
    
    # 测试AsyncTaskManager
    print("测试AsyncTaskManager线程安全性...")
    instances.clear()
    
    # 重置实例
    from asp import AsyncTaskManager
    AsyncTaskManager.reset_instance()
    
    # 使用多线程同时创建实例
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(create_async_task_manager) for _ in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # 检查是否所有实例都是同一个
    unique_instances = set(instances)
    if len(unique_instances) == 1:
        print("✅ AsyncTaskManager线程安全测试通过")
        task_manager_safe = True
    else:
        print(f"❌ AsyncTaskManager线程安全测试失败，创建了{len(unique_instances)}个不同实例")
        task_manager_safe = False
    
    # 测试AsyncLoopManager
    print("测试AsyncLoopManager线程安全性...")
    instances.clear()
    
    # 重置实例
    from asp import AsyncLoopManager
    AsyncLoopManager.reset_instance()
    
    # 使用多线程同时创建实例
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(create_async_loop_manager) for _ in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # 检查是否所有实例都是同一个
    unique_instances = set(instances)
    if len(unique_instances) == 1:
        print("✅ AsyncLoopManager线程安全测试通过")
        loop_manager_safe = True
    else:
        print(f"❌ AsyncLoopManager线程安全测试失败，创建了{len(unique_instances)}个不同实例")
        loop_manager_safe = False
    
    return task_manager_safe and loop_manager_safe

def test_singleton_functionality():
    """测试单例模式的基本功能"""
    print("🔍 测试单例模式基本功能...")
    
    try:
        from asp import AsyncTaskManager, AsyncLoopManager
        
        # 重置实例
        AsyncTaskManager.reset_instance()
        AsyncLoopManager.reset_instance()
        
        # 测试AsyncTaskManager
        print("测试AsyncTaskManager...")
        
        # 检查初始状态
        assert not AsyncTaskManager.is_initialized(), "初始状态应该未初始化"
        
        # 创建第一个实例
        instance1 = AsyncTaskManager()
        assert AsyncTaskManager.is_initialized(), "创建实例后应该已初始化"
        
        # 创建第二个实例
        instance2 = AsyncTaskManager()
        
        # 检查是否是同一个实例
        assert instance1 is instance2, "应该返回同一个实例"
        assert id(instance1) == id(instance2), "实例ID应该相同"
        
        # 使用get_instance方法
        instance3 = AsyncTaskManager.get_instance()
        assert instance1 is instance3, "get_instance应该返回同一个实例"
        
        print("✅ AsyncTaskManager功能测试通过")
        
        # 测试AsyncLoopManager
        print("测试AsyncLoopManager...")
        
        # 检查初始状态
        assert not AsyncLoopManager.is_initialized(), "初始状态应该未初始化"
        
        # 创建实例
        loop_instance1 = AsyncLoopManager()
        loop_instance2 = AsyncLoopManager()
        
        # 检查是否是同一个实例
        assert loop_instance1 is loop_instance2, "应该返回同一个实例"
        
        # 使用get_instance方法
        loop_instance3 = AsyncLoopManager.get_instance()
        assert loop_instance1 is loop_instance3, "get_instance应该返回同一个实例"
        
        print("✅ AsyncLoopManager功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 单例模式功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始测试单例模式线程安全修复效果...")
    
    # 测试基本功能
    test1 = test_singleton_functionality()
    
    print("\n" + "="*50 + "\n")
    
    # 测试线程安全性
    test2 = test_singleton_thread_safety()
    
    print("\n" + "="*50 + "\n")
    
    if test1 and test2:
        print("🎉 所有测试通过！单例模式线程安全问题已修复！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
